[package]
name = "indidus_sql"
version = "0.1.0"
edition = "2024"

[dependencies]

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Native database drivers
# SQLite (synchronous)
rusqlite = { version = "0.30", features = ["bundled", "chrono", "serde_json", "uuid"], optional = true }
r2d2 = { version = "0.8", optional = true }
r2d2_sqlite = { version = "0.23", optional = true }

# PostgreSQL (async)
tokio-postgres = { version = "0.7", features = ["with-chrono-0_4", "with-serde_json-1", "with-uuid-1"], optional = true }
deadpool-postgres = { version = "0.12", optional = true }
r2d2_postgres = "=0.18.2"
postgres = { version = "=0.19.10", features = ["with-serde_json-1", "with-chrono-0_4"] }

# MySQL (async)
mysql_async = { version = "0.33", features = ["default"], optional = true }
deadpool = { version = "0.10", optional = true }
mysql = "=26.0.0"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Core utilities
ulid = "1.2.1"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
async-trait = "0.1"
url = "2.4"
anyhow = "1.0.97"

# Performance optimizations
once_cell = "1.19"
regex = "1.10"
bytes = "1.5"
dashmap = "6.1.0"

# Security and cryptography
rand = "0.8"
hmac = "0.12"
sha2 = "0.10"
subtle = "2.4"
hex = "0.4"

# Observability (optional)
env_logger = "0.11.7"
log = "0.4.27"

# Development dependencies
[dev-dependencies]
tempfile = "3.0"

actix-web = "4.10.2"
proptest = "=1.6.0"
test-log = "=0.2.17"
