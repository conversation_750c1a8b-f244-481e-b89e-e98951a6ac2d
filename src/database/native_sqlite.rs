//! Native SQLite database provider implementation

use std::{sync::Arc, time::Instant};

use async_trait::async_trait;
use dashmap::DashMap;
use once_cell::sync::Lazy;

use crate::{
    database::{
        native::{
            common::traits::{DatabaseConnection, DatabasePool, DatabaseValue},
            sqlite::SqlitePool,
        },
        DatabaseHealth, DatabaseProvider, DatabaseStats, DatabaseTransaction,
    },
    error::{AuthError, Result},
    types::{
        CreatePermissionRequest, CreateRoleRequest, Permission, PermissionCheck, PermissionFilter,
        PermissionId, PermissionResult, Role, RoleFilter, RoleId, RolePermission, Session,
        SessionId, User, UserAttribute, UserAttributeFilter, UserAttributeRequest,
        UserAttributeValue, UserAttributes, UserId, UserRoleAssignment,
    },
};

static SQL_CACHE: Lazy<DashMap<String, Arc<String>>> = Lazy::new(DashMap::new);

/// Native SQLite database provider
#[derive(Clone)]
pub struct NativeSqliteProvider {
    pub pool: SqlitePool,
    pub table_prefix: String,
}

impl NativeSqliteProvider {
    /// Create a new SQLite provider with the given database path
    pub fn new(database_path: &str) -> Result<Self> {
        let pool = SqlitePool::new(database_path, 10)
            .map_err(|e| AuthError::internal(format!("Failed to create SQLite pool: {e}")))?;

        Ok(Self {
            pool,
            table_prefix: String::new(),
        })
    }

    /// Create a new SQLite provider with the given database path and table prefix
    pub fn new_with_prefix(database_path: &str, table_prefix: String) -> Result<Self> {
        let pool = SqlitePool::new(database_path, 10)
            .map_err(|e| AuthError::internal(format!("Failed to create SQLite pool: {e}")))?;

        Ok(Self { pool, table_prefix })
    }

    /// Helper function to get table name with prefix
    fn table_name(&self, base_name: &str) -> String {
        format!("{}{}", self.table_prefix, base_name)
    }

    /// Gets the cached SQL string for setting an attribute, generating it if not present.
    fn get_cached_set_attribute_sql(&self) -> Arc<String> {
        let key = self.table_prefix.clone();
        if let Some(sql) = SQL_CACHE.get(&key) {
            return sql.clone();
        }
        let table_name = self.table_name("user_attributes");
        let sql = Arc::new(format!(
            "INSERT OR REPLACE INTO {table_name} (id, user_id, key, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, COALESCE((SELECT created_at FROM {table_name} WHERE user_id = ? AND key = ?), ?), ?)"
        ));
        SQL_CACHE.insert(key, sql.clone());
        sql
    }

    /// Enable foreign key constraints for a connection
    async fn _enable_foreign_keys(&self) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        conn.execute("PRAGMA foreign_keys = ON", &[])
            .await
            .map_err(|e| AuthError::internal(format!("Failed to enable foreign keys: {e}")))?;

        Ok(())
    }
}

#[async_trait]
impl DatabaseProvider for NativeSqliteProvider {
    fn provider_type(&self) -> crate::database::connection::DatabaseType {
        crate::database::connection::DatabaseType::Sqlite
    }

    fn supports_feature(&self, feature: &str) -> bool {
        match feature {
            "auth" | "rbac" | "transactions" => true,
            "json" => true,             // SQLite supports JSON functions
            "full_text_search" => true, // SQLite supports FTS
            _ => false,
        }
    }

    async fn initialize(&self) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Create users table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL,
                last_login INTEGER,
                role TEXT NOT NULL DEFAULT 'user',
                status TEXT NOT NULL DEFAULT 'active',
                is_verified BOOLEAN NOT NULL DEFAULT 0,
                login_attempts INTEGER NOT NULL DEFAULT 0,
                locked_until INTEGER,
                metadata TEXT
            )",
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create users table: {e}")))?;

        // Enable foreign key constraints
        conn.execute("PRAGMA foreign_keys = ON", &[])
            .await
            .map_err(|e| AuthError::internal(format!("Failed to enable foreign keys: {e}")))?;

        // Create sessions table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                token TEXT NOT NULL,
                refresh_token TEXT,
                expires_at INTEGER NOT NULL,
                created_at INTEGER NOT NULL,
                last_accessed INTEGER NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                session_data TEXT,
                metadata TEXT,
                FOREIGN KEY (user_id) REFERENCES {} (id) ON DELETE CASCADE
            )",
                self.table_name("sessions"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create sessions table: {e}")))?;

        // Create indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_users_username ON {} (username)",
                self.table_prefix,
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create username index: {e}")))?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_users_email ON {} (email)",
                self.table_prefix,
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create email index: {e}")))?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_sessions_user_id ON {} (user_id)",
                self.table_prefix,
                self.table_name("sessions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create sessions user_id index: {e}"))
        })?;

        // Create optimized RBAC tables with consistent naming and better indexes

        // Create permissions table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                resource TEXT NOT NULL,
                action TEXT NOT NULL,
                created_at TEXT NOT NULL DEFAULT (datetime('now')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                metadata TEXT NOT NULL DEFAULT '{{}}',
                UNIQUE(resource, action)
            )",
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create permissions table: {e}")))?;

        // Create roles table (consistent with migration.rs naming)
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                parent_id TEXT,
                level INTEGER NOT NULL DEFAULT 0,
                is_system INTEGER NOT NULL DEFAULT 0,
                is_active INTEGER NOT NULL DEFAULT 1,
                created_at TEXT NOT NULL DEFAULT (datetime('now')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                metadata TEXT NOT NULL DEFAULT '{{}}',
                FOREIGN KEY (parent_id) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("roles"),
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles table: {e}")))?;

        // Create role_permissions junction table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                role_id TEXT NOT NULL,
                permission_id TEXT NOT NULL,
                granted INTEGER NOT NULL DEFAULT 1,
                granted_by TEXT,
                granted_at TEXT NOT NULL DEFAULT (datetime('now')),
                metadata TEXT NOT NULL DEFAULT '{{}}',
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (granted_by) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("role_permissions"),
                self.table_name("roles"),
                self.table_name("permissions"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create role_permissions table: {e}"))
        })?;

        // Create user_role_assignments junction table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                user_id TEXT NOT NULL,
                role_id TEXT NOT NULL,
                assigned_by TEXT,
                assigned_at TEXT NOT NULL DEFAULT (datetime('now')),
                expires_at TEXT,
                is_active INTEGER NOT NULL DEFAULT 1,
                metadata TEXT NOT NULL DEFAULT '{{}}',
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("user_role_assignments"),
                self.table_name("users"),
                self.table_name("roles"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create user_role_assignments table: {e}"))
        })?;

        // Create user_attributes table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                key TEXT NOT NULL,
                value_type TEXT NOT NULL,
                value_string TEXT,
                value_number REAL,
                value_boolean INTEGER,
                value_json TEXT,
                created_at TEXT NOT NULL DEFAULT (datetime('now')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                UNIQUE(user_id, key),
                FOREIGN KEY (user_id) REFERENCES {} (id) ON DELETE CASCADE
            )",
                self.table_name("user_attributes"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create user_attributes table: {e}")))?;

        // Create optimized indexes for RBAC performance

        // Permissions indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_permissions_name ON {}(name)",
                self.table_prefix,
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create permissions name index: {e}"))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_permissions_resource ON {}(resource)",
                self.table_prefix,
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create permissions resource index: {e}"))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_permissions_action ON {}(action)",
                self.table_prefix,
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create permissions action index: {e}"))
        })?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_permissions_resource_action ON {}(resource, action)", self.table_prefix, self.table_name("permissions")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create permissions resource_action index: {e}")))?;

        // Roles indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_roles_name ON {}(name)",
                self.table_prefix,
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles name index: {e}")))?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_roles_parent_id ON {}(parent_id)",
                self.table_prefix,
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles parent_id index: {e}")))?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_roles_level ON {}(level)",
                self.table_prefix,
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles level index: {e}")))?;
        conn.execute(
            &format!("CREATE INDEX IF NOT EXISTS {}idx_roles_active ON {}(is_active) WHERE is_active = 1", self.table_prefix, self.table_name("roles")),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles active index: {e}")))?;

        // Role permissions indexes (optimized for permission checking)
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_role_permissions_role_id ON {}(role_id)",
                self.table_prefix,
                self.table_name("role_permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create role_permissions role_id index: {e}"
            ))
        })?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_role_permissions_permission_id ON {}(permission_id)", self.table_prefix, self.table_name("role_permissions")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create role_permissions permission_id index: {e}")))?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_role_permissions_granted ON {}(granted) WHERE granted = 1", self.table_prefix, self.table_name("role_permissions")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create role_permissions granted index: {e}")))?;

        // User role assignments indexes (optimized for user permission lookups)
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_role_assignments_user_id ON {}(user_id)",
                self.table_prefix,
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create user_role_assignments user_id index: {e}"
            ))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_role_assignments_role_id ON {}(role_id)",
                self.table_prefix,
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create user_role_assignments role_id index: {e}"
            ))
        })?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_user_role_assignments_active ON {}(is_active, expires_at) WHERE is_active = 1", self.table_prefix, self.table_name("user_role_assignments")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create user_role_assignments active index: {e}")))?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_user_role_assignments_expires ON {}(expires_at) WHERE expires_at IS NOT NULL", self.table_prefix, self.table_name("user_role_assignments")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create user_role_assignments expires index: {e}")))?;

        // Composite index for the most common permission check query
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_permission_check_composite ON {}(user_id, is_active)", self.table_prefix, self.table_name("user_role_assignments")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create permission check composite index: {e}")))?;

        // User attributes indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_attributes_user_id ON {}(user_id)",
                self.table_prefix,
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create user_attributes user_id index: {e}"
            ))
        })?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_attributes_key ON {}(key)",
                self.table_prefix,
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create user_attributes key index: {e}"))
        })?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_attributes_value_type ON {}(value_type)",
                self.table_prefix,
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create user_attributes value_type index: {e}"
            ))
        })?;

        Ok(())
    }

    async fn health_check(&self) -> Result<DatabaseHealth> {
        let start = Instant::now();

        match self.pool.get_connection().await {
            Ok(mut conn) => match conn.execute("SELECT 1", &[]).await {
                Ok(_) => {
                    let response_time = start.elapsed();
                    Ok(DatabaseHealth {
                        is_healthy: true,
                        response_time,
                        connection_count: Some(self.pool.active_connections() as u32),
                        error_message: None,
                    })
                }
                Err(e) => Ok(DatabaseHealth {
                    is_healthy: false,
                    response_time: start.elapsed(),
                    connection_count: None,
                    error_message: Some(format!("Query failed: {e}")),
                }),
            },
            Err(e) => Ok(DatabaseHealth {
                is_healthy: false,
                response_time: start.elapsed(),
                connection_count: None,
                error_message: Some(format!("Connection failed: {e}")),
            }),
        }
    }

    async fn get_stats(&self) -> Result<DatabaseStats> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let row = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", self.table_name("users")),
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to count users: {e}")))?;
        let total_users = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;
        let row = conn
            .query_one(
                &format!(
                    "SELECT COUNT(*) FROM {} WHERE is_active = 1 AND expires_at > ?",
                    self.table_name("sessions")
                ),
                &[DatabaseValue::I64(current_time)],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to count active sessions: {e}")))?;
        let active_sessions = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        let row = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", self.table_name("sessions")),
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to count total sessions: {e}")))?;
        let total_sessions = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        Ok(DatabaseStats {
            total_users,
            active_sessions,
            total_sessions,
            database_size: None, // SQLite doesn't easily provide this
            connection_pool_size: Some(self.pool.max_connections()),
            active_connections: Some(self.pool.active_connections()),
        })
    }

    // User operations
    async fn create_user(&self, user: &User) -> Result<UserId> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let created_at = user.created_at.timestamp();
        let updated_at = user.updated_at.timestamp();
        let last_login = user.last_login.map(|dt| dt.timestamp());
        let metadata_json = serde_json::to_string(&user.metadata).unwrap_or_default();

        let locked_until = user.locked_until.map(|dt| dt.timestamp());
        let _result = conn.execute(
            &format!("INSERT INTO {} (id, username, email, password_hash, salt, created_at, updated_at, 
             last_login, role, status, is_verified, login_attempts, locked_until, metadata) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", self.table_name("users")),
            &[
                DatabaseValue::Text(user.id.to_string()),
                DatabaseValue::Text(user.username.clone()),
                DatabaseValue::Text(user.email.clone()),
                DatabaseValue::Text(user.password_hash.clone()),
                DatabaseValue::Text(user.salt.clone()),
                DatabaseValue::I64(created_at),
                DatabaseValue::I64(updated_at),
                last_login.map(DatabaseValue::I64).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(user.role.to_string()),
                DatabaseValue::Text(user.status.to_string()),
                DatabaseValue::I64(if user.is_verified { 1 } else { 0 }),
                DatabaseValue::I64(user.failed_login_attempts as i64),
                locked_until.map(DatabaseValue::I64).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(metadata_json),
            ]
        ).await.map_err(|e| AuthError::internal(format!("Failed to create user: {e}")))?;

        // Return the user ID that was actually inserted
        // Since we're using the user.id in the INSERT statement, we should return that same ID
        // But let's verify it was actually inserted correctly by querying the database directly
        let mut verification_conn = self.pool.get_connection().await.map_err(|e| {
            AuthError::internal(format!("Failed to get verification connection: {e}"))
        })?;

        let verification_rows = verification_conn
            .query_all(
                &format!(
                    "SELECT id FROM {} WHERE username = ?",
                    self.table_name("users")
                ),
                &[DatabaseValue::Text(user.username.clone())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to verify user creation: {e}")))?;

        if verification_rows.is_empty() {
            return Err(AuthError::internal(format!(
                "User was not inserted into database for username: {}",
                user.username
            )));
        }

        let _actual_id_str = match verification_rows[0].get_by_index(0)? {
            crate::database::native::common::traits::DatabaseValue::Text(s) => s,
            crate::database::native::common::traits::DatabaseValue::Uuid(uuid) => uuid.to_string(),
            crate::database::native::common::traits::DatabaseValue::Null => {
                return Err(AuthError::internal(
                    "User ID is NULL in database".to_string(),
                ));
            }
            other => {
                return Err(AuthError::internal(format!(
                    "User ID has unexpected type: {other:?}"
                )));
            }
        };

        // Return the original user ID since that's what we inserted
        Ok(user.id)
    }

    async fn get_user_by_id(&self, user_id: UserId) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, username, email, password_hash, salt, created_at, updated_at, last_login, role, status, is_verified, login_attempts, locked_until, metadata FROM {} WHERE id = ?", self.table_name("users")),
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to query user by id: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];

        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                DatabaseValue::Uuid(uuid) => uuid.to_string(),
                DatabaseValue::Json(j) => j.to_string(),
                _ => String::new(),
            }
        };

        let get_i64 = |idx: usize| -> Option<i64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::I64(i) => Some(i),
                DatabaseValue::I32(i) => Some(i as i64),
                _ => None,
            }
        };

        let user = User {
            id: get_text(0)
                .parse::<uuid::Uuid>()
                .map(UserId::from)
                .unwrap_or_default(),
            username: get_text(1),
            email: get_text(2),
            password_hash: get_text(3),
            salt: get_text(4),
            role: get_text(8).parse().unwrap_or(crate::types::UserRole::User),
            status: get_text(9)
                .parse()
                .unwrap_or(crate::types::UserStatus::Active),
            created_at: get_i64(5)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            updated_at: get_i64(6)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            last_login: get_i64(7).and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            is_active: true, // Default for compatibility
            is_verified: get_i64(10).unwrap_or(0) != 0,
            failed_login_attempts: get_i64(11).unwrap_or(0) as u32,
            login_attempts: get_i64(11).unwrap_or(0) as i32,
            locked_until: None, // Default for compatibility
            email_verified: get_i64(10).unwrap_or(0) != 0,
            email_verification_token: None, // Default for compatibility
            password_reset_token: None,     // Default for compatibility
            password_reset_expires: None,   // Default for compatibility
            metadata: {
                let metadata_str = get_text(13);
                if metadata_str.is_empty() {
                    serde_json::Value::Object(serde_json::Map::new())
                } else {
                    serde_json::from_str(&metadata_str)
                        .unwrap_or_else(|_| serde_json::Value::Object(serde_json::Map::new()))
                }
            },
        };
        Ok(Some(user))
    }

    async fn get_user_by_username(&self, username: &str) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, username, email, password_hash, salt, created_at, updated_at, last_login, role, status, is_verified, login_attempts, locked_until, metadata FROM {} WHERE username = ?", self.table_name("users")),
                &[DatabaseValue::Text(username.to_string())],
            )
            .await
            .map_err(|e| {
                AuthError::internal(format!("Failed to query user by username: {e}"))
            })?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];

        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                DatabaseValue::Uuid(uuid) => uuid.to_string(),
                DatabaseValue::Json(j) => j.to_string(),
                _ => String::new(),
            }
        };

        let get_i64 = |idx: usize| -> Option<i64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::I64(i) => Some(i),
                DatabaseValue::I32(i) => Some(i as i64),
                _ => None,
            }
        };

        let user_id_str = get_text(0);
        if user_id_str.is_empty() {
            return Err(AuthError::internal("User ID is empty in database"));
        }
        let user_id = user_id_str
            .parse::<uuid::Uuid>()
            .map(UserId::from)
            .map_err(|e| {
                AuthError::internal(format!("Failed to parse user ID '{user_id_str}': {e}"))
            })?;

        let user = User {
            id: user_id,
            username: get_text(1),
            email: get_text(2),
            password_hash: get_text(3),
            salt: get_text(4),
            role: get_text(8).parse().unwrap_or(crate::types::UserRole::User),
            status: get_text(9)
                .parse()
                .unwrap_or(crate::types::UserStatus::Active),
            created_at: get_i64(5)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_else(chrono::Utc::now),
            updated_at: get_i64(6)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_else(chrono::Utc::now),
            last_login: get_i64(7).and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            is_active: true, // Default for compatibility
            is_verified: get_i64(10).unwrap_or(0) != 0,
            failed_login_attempts: get_i64(11).unwrap_or(0) as u32,
            login_attempts: get_i64(11).unwrap_or(0) as i32,
            locked_until: get_i64(12).and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            email_verified: get_i64(10).unwrap_or(0) != 0,
            email_verification_token: None, // Default for compatibility
            password_reset_token: None,     // Default for compatibility
            password_reset_expires: None,   // Default for compatibility
            metadata: {
                let metadata_str = get_text(13);
                if metadata_str.is_empty() {
                    serde_json::Value::Object(serde_json::Map::new())
                } else {
                    serde_json::from_str(&metadata_str)
                        .unwrap_or_else(|_| serde_json::Value::Object(serde_json::Map::new()))
                }
            },
        };
        Ok(Some(user))
    }

    async fn get_user_by_email(&self, email: &str) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, username, email, password_hash, salt, created_at, updated_at, last_login, role, status, is_verified, login_attempts, locked_until, metadata FROM {} WHERE email = ?", self.table_name("users")),
                &[DatabaseValue::Text(email.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to query user by email: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];

        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                DatabaseValue::Uuid(uuid) => uuid.to_string(),
                DatabaseValue::Json(j) => j.to_string(),
                _ => String::new(),
            }
        };

        let get_i64 = |idx: usize| -> Option<i64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::I64(i) => Some(i),
                DatabaseValue::I32(i) => Some(i as i64),
                _ => None,
            }
        };

        let user = User {
            id: get_text(0)
                .parse::<uuid::Uuid>()
                .map(UserId::from)
                .unwrap_or_default(),
            username: get_text(1),
            email: get_text(2),
            password_hash: get_text(3),
            salt: get_text(4),
            role: get_text(8).parse().unwrap_or(crate::types::UserRole::User),
            status: get_text(9)
                .parse()
                .unwrap_or(crate::types::UserStatus::Active),
            created_at: get_i64(5)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            updated_at: get_i64(6)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            last_login: get_i64(7).and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            is_active: true, // Default for compatibility
            is_verified: get_i64(10).unwrap_or(0) != 0,
            failed_login_attempts: get_i64(11).unwrap_or(0) as u32,
            login_attempts: get_i64(11).unwrap_or(0) as i32,
            locked_until: None, // Default for compatibility
            email_verified: get_i64(10).unwrap_or(0) != 0,
            email_verification_token: None, // Default for compatibility
            password_reset_token: None,     // Default for compatibility
            password_reset_expires: None,   // Default for compatibility
            metadata: {
                let metadata_str = get_text(13);
                if metadata_str.is_empty() {
                    serde_json::Value::Object(serde_json::Map::new())
                } else {
                    serde_json::from_str(&metadata_str)
                        .unwrap_or_else(|_| serde_json::Value::Object(serde_json::Map::new()))
                }
            },
        };
        Ok(Some(user))
    }

    async fn update_user(&self, user: &User) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let updated_at = user.updated_at.timestamp();
        let last_login = user.last_login.map(|dt| dt.timestamp());
        let metadata_json = serde_json::to_string(&user.metadata).unwrap_or_default();

        let locked_until = user.locked_until.map(|dt| dt.timestamp());
        conn.execute(
            &format!("UPDATE {} SET username = ?, email = ?, password_hash = ?, salt = ?, updated_at = ?, 
             last_login = ?, role = ?, status = ?, is_verified = ?, login_attempts = ?, locked_until = ?, metadata = ? 
             WHERE id = ?", self.table_name("users")),
            &[
                DatabaseValue::Text(user.username.clone()),
                DatabaseValue::Text(user.email.clone()),
                DatabaseValue::Text(user.password_hash.clone()),
                DatabaseValue::Text(user.salt.clone()),
                DatabaseValue::I64(updated_at),
                last_login.map(DatabaseValue::I64).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(user.role.to_string()),
                DatabaseValue::Text(user.status.to_string()),
                DatabaseValue::I64(if user.is_verified { 1 } else { 0 }),
                DatabaseValue::I64(user.failed_login_attempts as i64),
                locked_until.map(DatabaseValue::I64).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(metadata_json),
                DatabaseValue::Text(user.id.to_string()),
            ]
        ).await.map_err(|e| AuthError::internal(format!("Failed to update user: {e}")))?;

        Ok(())
    }

    async fn delete_user(&self, user_id: UserId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        conn.execute(
            &format!("DELETE FROM {} WHERE id = ?", self.table_name("users")),
            &[DatabaseValue::Text(user_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to delete user: {e}")))?;

        Ok(())
    }

    async fn list_users(&self, offset: u64, limit: u64) -> Result<Vec<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, username, email, password_hash, salt, created_at, updated_at, last_login, role, status, is_verified, login_attempts, locked_until, metadata FROM {} ORDER BY created_at DESC LIMIT ? OFFSET ?", self.table_name("users")),
                &[
                    DatabaseValue::I64(limit as i64),
                    DatabaseValue::I64(offset as i64),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to list users: {e}")))?;

        let mut users = Vec::new();
        for row in rows {
            let get_text = |idx: usize| -> String {
                match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                    DatabaseValue::Text(s) => s,
                    DatabaseValue::Uuid(uuid) => uuid.to_string(),
                    DatabaseValue::Json(j) => j.to_string(),
                    _ => String::new(),
                }
            };

            let get_i64 = |idx: usize| -> Option<i64> {
                match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                    DatabaseValue::I64(i) => Some(i),
                    DatabaseValue::I32(i) => Some(i as i64),
                    _ => None,
                }
            };

            let user_id_str = get_text(0);
            if user_id_str.is_empty() {
                return Err(AuthError::internal("User ID is empty in database"));
            }
            let user_id = user_id_str
                .parse::<uuid::Uuid>()
                .map(UserId::from)
                .map_err(|e| {
                    AuthError::internal(format!("Failed to parse user ID '{user_id_str}': {e}"))
                })?;

            let user = User {
                id: user_id,
                username: get_text(1),
                email: get_text(2),
                password_hash: get_text(3),
                salt: get_text(4),
                role: get_text(8).parse().unwrap_or(crate::types::UserRole::User),
                status: get_text(9)
                    .parse()
                    .unwrap_or(crate::types::UserStatus::Active),
                created_at: get_i64(5)
                    .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                    .unwrap_or_default(),
                updated_at: get_i64(6)
                    .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                    .unwrap_or_default(),
                last_login: get_i64(7).and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
                is_active: true, // Default for compatibility
                is_verified: get_i64(10).unwrap_or(0) != 0,
                failed_login_attempts: get_i64(11).unwrap_or(0) as u32,
                login_attempts: get_i64(11).unwrap_or(0) as i32,
                locked_until: get_i64(12).and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
                email_verified: get_i64(10).unwrap_or(0) != 0,
                email_verification_token: None, // Default for compatibility
                password_reset_token: None,     // Default for compatibility
                password_reset_expires: None,   // Default for compatibility
                metadata: {
                    let metadata_str = get_text(13);
                    if metadata_str.is_empty() {
                        serde_json::Value::Object(serde_json::Map::new())
                    } else {
                        serde_json::from_str(&metadata_str)
                            .unwrap_or_else(|_| serde_json::Value::Object(serde_json::Map::new()))
                    }
                },
            };
            users.push(user);
        }

        Ok(users)
    }

    async fn count_users(&self) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let row = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", self.table_name("users")),
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to count users: {e}")))?;

        let count = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        Ok(count)
    }

    // Session operations
    async fn create_session(&self, session: &Session) -> Result<SessionId> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Enable foreign key constraints for this connection
        conn.execute("PRAGMA foreign_keys = ON", &[])
            .await
            .map_err(|e| AuthError::internal(format!("Failed to enable foreign keys: {e}")))?;

        // Foreign key constraint will handle user existence validation

        let created_at = session.created_at.timestamp();
        let expires_at = session.expires_at.timestamp();
        let last_accessed = session.last_accessed.timestamp();
        let ip_address = session.ip_address.map(|ip| ip.to_string());
        let session_data_json = serde_json::to_string(&session.session_data).unwrap_or_default();
        let metadata_json = serde_json::to_string(&session.metadata).unwrap_or_default();

        conn.execute(
            &format!("INSERT INTO {} (id, user_id, token, refresh_token, expires_at, created_at, last_accessed, 
             ip_address, user_agent, is_active, session_data, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", self.table_name("sessions")),
            &[
                DatabaseValue::Text(session.id.to_string()),
                DatabaseValue::Text(session.user_id.to_string()),
                DatabaseValue::Text(session.token.clone()),
                session.refresh_token.as_ref().map(|t| DatabaseValue::Text(t.clone())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(expires_at),
                DatabaseValue::I64(created_at),
                DatabaseValue::I64(last_accessed),
                ip_address.map(DatabaseValue::Text).unwrap_or(DatabaseValue::Null),
                session.user_agent.as_ref().map(|ua| DatabaseValue::Text(ua.clone())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(if session.is_active { 1 } else { 0 }),
                DatabaseValue::Text(session_data_json),
                DatabaseValue::Text(metadata_json),
            ]
        ).await.map_err(|e| AuthError::internal(format!("Failed to create session: {e}")))?;

        Ok(session.id)
    }

    async fn get_session_by_id(&self, session_id: SessionId) -> Result<Option<Session>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT * FROM {} WHERE id = ?", self.table_name("sessions")),
                &[DatabaseValue::Text(session_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to query session by id: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];

        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                DatabaseValue::Uuid(uuid) => uuid.to_string(),
                _ => String::new(),
            }
        };

        let get_i64 = |idx: usize| -> Option<i64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::I64(i) => Some(i),
                DatabaseValue::I32(i) => Some(i as i64),
                _ => None,
            }
        };

        let session_id_str = get_text(0);
        if session_id_str.is_empty() {
            return Err(AuthError::internal("Session ID is empty in database"));
        }
        let session_id = session_id_str
            .parse::<uuid::Uuid>()
            .map(SessionId::from)
            .map_err(|e| {
                AuthError::internal(format!(
                    "Failed to parse session ID '{session_id_str}': {e}"
                ))
            })?;

        let user_id_str = get_text(1);
        if user_id_str.is_empty() {
            return Err(AuthError::internal("User ID is empty in database"));
        }
        let user_id = user_id_str
            .parse::<uuid::Uuid>()
            .map(UserId::from)
            .map_err(|e| {
                AuthError::internal(format!("Failed to parse user ID '{user_id_str}': {e}"))
            })?;

        let session = Session {
            id: session_id,
            user_id,
            token: get_text(2),
            refresh_token: if get_text(3).is_empty() {
                None
            } else {
                Some(get_text(3))
            },
            expires_at: get_i64(4)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            created_at: get_i64(5)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            last_accessed: get_i64(6)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            ip_address: if get_text(7).is_empty() {
                None
            } else {
                get_text(7).parse().ok()
            },
            user_agent: if get_text(8).is_empty() {
                None
            } else {
                Some(get_text(8))
            },
            is_active: get_i64(9).unwrap_or(0) != 0,
            session_data: serde_json::from_str(&get_text(10)).unwrap_or_default(),
            metadata: serde_json::from_str(&get_text(11)).unwrap_or_default(),
        };
        Ok(Some(session))
    }

    async fn update_session(&self, session: &Session) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let expires_at = session.expires_at.timestamp();
        let last_accessed = session.last_accessed.timestamp();
        let ip_address = session.ip_address.map(|ip| ip.to_string());
        let session_data_json = serde_json::to_string(&session.session_data).unwrap_or_default();
        let metadata_json = serde_json::to_string(&session.metadata).unwrap_or_default();

        conn.execute(
            &format!("UPDATE {} SET user_id = ?, token = ?, refresh_token = ?, expires_at = ?, last_accessed = ?, 
             ip_address = ?, user_agent = ?, is_active = ?, session_data = ?, metadata = ? WHERE id = ?", self.table_name("sessions")),
            &[
                DatabaseValue::Text(session.user_id.to_string()),
                DatabaseValue::Text(session.token.clone()),
                session.refresh_token.as_ref().map(|t| DatabaseValue::Text(t.clone())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(expires_at),
                DatabaseValue::I64(last_accessed),
                ip_address.map(DatabaseValue::Text).unwrap_or(DatabaseValue::Null),
                session.user_agent.as_ref().map(|ua| DatabaseValue::Text(ua.clone())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(if session.is_active { 1 } else { 0 }),
                DatabaseValue::Text(session_data_json),
                DatabaseValue::Text(metadata_json),
                DatabaseValue::Text(session.id.to_string()),
            ]
        ).await.map_err(|e| AuthError::internal(format!("Failed to update session: {e}")))?;

        Ok(())
    }

    async fn delete_session(&self, session_id: SessionId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        conn.execute(
            &format!("DELETE FROM {} WHERE id = ?", self.table_name("sessions")),
            &[DatabaseValue::Text(session_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to delete session: {e}")))?;

        Ok(())
    }

    async fn delete_user_sessions(&self, user_id: UserId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        conn.execute(
            &format!(
                "DELETE FROM {} WHERE user_id = ?",
                self.table_name("sessions")
            ),
            &[DatabaseValue::Text(user_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to delete user sessions: {e}")))?;

        Ok(())
    }

    async fn list_user_sessions(&self, user_id: UserId) -> Result<Vec<Session>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn.query_all(&format!("SELECT * FROM {} WHERE user_id = ? AND is_active = 1 ORDER BY last_accessed DESC", self.table_name("sessions")),
            &[DatabaseValue::Text(user_id.to_string())])
            .await.map_err(|e| AuthError::internal(format!("Failed to list user sessions: {e}")))?;

        let mut sessions = Vec::new();
        for row in rows {
            let get_text = |idx: usize| -> String {
                match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                    DatabaseValue::Text(s) => s,
                    DatabaseValue::Uuid(uuid) => uuid.to_string(),
                    _ => String::new(),
                }
            };

            let get_i64 = |idx: usize| -> Option<i64> {
                match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                    DatabaseValue::I64(i) => Some(i),
                    DatabaseValue::I32(i) => Some(i as i64),
                    _ => None,
                }
            };

            let session_id_str = get_text(0);
            if session_id_str.is_empty() {
                return Err(AuthError::internal("Session ID is empty in database"));
            }
            let session_id = session_id_str
                .parse::<uuid::Uuid>()
                .map(SessionId::from)
                .map_err(|e| {
                    AuthError::internal(format!(
                        "Failed to parse session ID '{session_id_str}': {e}"
                    ))
                })?;

            let user_id_str = get_text(1);
            let user_id = user_id_str
                .parse::<uuid::Uuid>()
                .map(UserId::from)
                .map_err(|e| {
                    AuthError::internal(format!("Failed to parse user ID '{user_id_str}': {e}"))
                })?;

            let session = Session {
                id: session_id,
                user_id,
                token: get_text(2),
                refresh_token: if get_text(3).is_empty() {
                    None
                } else {
                    Some(get_text(3))
                },
                expires_at: get_i64(4)
                    .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                    .unwrap_or_default(),
                created_at: get_i64(5)
                    .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                    .unwrap_or_default(),
                last_accessed: get_i64(6)
                    .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                    .unwrap_or_default(),
                ip_address: if get_text(7).is_empty() {
                    None
                } else {
                    get_text(7).parse().ok()
                },
                user_agent: if get_text(8).is_empty() {
                    None
                } else {
                    Some(get_text(8))
                },
                is_active: get_i64(9).unwrap_or(0) != 0,
                session_data: serde_json::from_str(&get_text(10)).unwrap_or_default(),
                metadata: serde_json::from_str(&get_text(11)).unwrap_or_default(),
            };
            sessions.push(session);
        }

        Ok(sessions)
    }

    async fn cleanup_expired_sessions(&self) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;

        let result = conn
            .execute(
                &format!(
                    "DELETE FROM {} WHERE expires_at < ?",
                    self.table_name("sessions")
                ),
                &[DatabaseValue::I64(current_time)],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to cleanup expired sessions: {e}")))?;

        Ok(result)
    }

    // RBAC operations - Role management
    async fn create_role(&self, request: CreateRoleRequest) -> Result<Role> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let role_id = uuid::Uuid::new_v4().to_string();
        let now = chrono::Utc::now().timestamp();

        // Calculate level based on parent role if provided
        let level = if let Some(parent_id) = &request.parent_id {
            // Get parent role level and increment
            let rows = conn
                .query_all(
                    &format!(
                        "SELECT level FROM {} WHERE id = ?",
                        self.table_name("roles")
                    ),
                    &[DatabaseValue::Text(parent_id.to_string())],
                )
                .await
                .map_err(|e| AuthError::internal(format!("Failed to get parent role: {e}")))?;

            if rows.is_empty() {
                return Err(AuthError::NotFound {
                    resource: "parent_role".to_string(),
                    id: parent_id.to_string(),
                });
            }

            let parent_level: i64 = rows[0]
                .get_by_index(0)
                .unwrap_or(DatabaseValue::I64(0))
                .to_string()
                .parse::<i64>()
                .map_err(|_| AuthError::internal("Invalid parent level"))?;

            parent_level + 1
        } else {
            0 // Root level
        };

        conn.execute(
            &format!("INSERT INTO {} (id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", self.table_name("roles")),
            &[
                DatabaseValue::Text(role_id.clone()),
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                request.parent_id.as_ref().map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(level),
                DatabaseValue::I64(0), // is_system defaults to false (SQLite INTEGER)
                DatabaseValue::I64(1),  // is_active defaults to true (SQLite INTEGER)
                DatabaseValue::I64(now),
                DatabaseValue::I64(now),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("UNIQUE constraint failed") {
                AuthError::Validation {
                    message: format!("Role name '{}' already exists", request.name),
                    field: Some("name".to_string()),
                }
            } else {
                AuthError::internal(format!("Failed to create role: {e}"))
            }
        })?;

        Ok(Role {
            id: RoleId::from(uuid::Uuid::parse_str(&role_id).unwrap()),
            name: request.name,
            description: request.description,
            parent_id: request.parent_id,
            level: level as u32,
            is_system: false,
            is_active: true,
            created_at: chrono::DateTime::from_timestamp(now, 0).unwrap(),
            updated_at: chrono::DateTime::from_timestamp(now, 0).unwrap(),
            metadata: request.metadata.unwrap_or(serde_json::Value::Null),
        })
    }

    async fn get_role(&self, role_id: RoleId) -> Result<Option<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn.query_all(
            &format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata 
             FROM {} WHERE id = ?", self.table_name("roles")),
            &[DatabaseValue::Text(role_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to get role: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let role = Role {
            id: RoleId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            parent_id: {
                if let Ok(parent_value) = row.get_by_index(3) {
                    let parent_str = parent_value.to_string();
                    if parent_str.is_empty() || parent_str == "null" {
                        None
                    } else {
                        Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                    }
                } else {
                    None
                }
            },
            level: row
                .get_by_index(4)
                .unwrap()
                .to_string()
                .parse()
                .unwrap_or(0),
            is_system: row.get_by_index(5).unwrap().to_string() == "1"
                || row.get_by_index(5).unwrap().to_string() == "true",
            is_active: row.get_by_index(6).unwrap().to_string() == "1"
                || row.get_by_index(6).unwrap().to_string() == "true",
            created_at: {
                let timestamp: i64 = row
                    .get_by_index(7)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            updated_at: {
                let timestamp: i64 = row
                    .get_by_index(8)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            metadata: {
                if let Ok(meta_value) = row.get_by_index(9) {
                    let meta_str = meta_value.to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::Value::String(meta_str)
                    }
                } else {
                    serde_json::Value::Null
                }
            },
        };

        Ok(Some(role))
    }

    async fn get_role_by_name(&self, name: &str) -> Result<Option<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn.query_all(
            &format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata 
             FROM {} WHERE name = ?", self.table_name("roles")),
            &[DatabaseValue::Text(name.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to get role by name: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let role = Role {
            id: RoleId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            parent_id: {
                if let Ok(parent_value) = row.get_by_index(3) {
                    let parent_str = parent_value.to_string();
                    if parent_str.is_empty() || parent_str == "null" {
                        None
                    } else {
                        Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                    }
                } else {
                    None
                }
            },
            level: row
                .get_by_index(4)
                .unwrap()
                .to_string()
                .parse()
                .unwrap_or(0),
            is_system: row.get_by_index(5).unwrap().to_string() == "1"
                || row.get_by_index(5).unwrap().to_string() == "true",
            is_active: row.get_by_index(6).unwrap().to_string() == "1"
                || row.get_by_index(6).unwrap().to_string() == "true",
            created_at: {
                let timestamp: i64 = row
                    .get_by_index(7)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            updated_at: {
                let timestamp: i64 = row
                    .get_by_index(8)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            metadata: {
                if let Ok(meta_value) = row.get_by_index(9) {
                    let meta_str = meta_value.to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::Value::String(meta_str)
                    }
                } else {
                    serde_json::Value::Null
                }
            },
        };

        Ok(Some(role))
    }

    async fn update_role(&self, role_id: RoleId, request: CreateRoleRequest) -> Result<Role> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let now = chrono::Utc::now().timestamp();

        // Calculate level based on parent role if provided
        let level = if let Some(parent_id) = &request.parent_id {
            let rows = conn
                .query_all(
                    &format!(
                        "SELECT level FROM {} WHERE id = ?",
                        self.table_name("roles")
                    ),
                    &[DatabaseValue::Text(parent_id.to_string())],
                )
                .await
                .map_err(|e| AuthError::internal(format!("Failed to get parent role: {e}")))?;

            if rows.is_empty() {
                return Err(AuthError::NotFound {
                    resource: "parent_role".to_string(),
                    id: parent_id.to_string(),
                });
            }

            let parent_level: i64 = rows[0]
                .get_by_index(0)
                .unwrap_or(DatabaseValue::I64(0))
                .to_string()
                .parse::<i64>()
                .map_err(|_| AuthError::internal("Invalid parent level"))?;

            parent_level + 1
        } else {
            0
        };

        conn.execute(
            &format!("UPDATE {} SET name = ?, description = ?, parent_id = ?, level = ?, updated_at = ?, metadata = ? WHERE id = ?", self.table_name("roles")),
            &[
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                request.parent_id.as_ref().map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(level),
                DatabaseValue::I64(now),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(role_id.to_string()),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("UNIQUE constraint failed") {
                AuthError::Validation {
                    message: format!("Role name '{}' already exists", request.name),
                    field: Some("name".to_string()),
                }
            } else {
                AuthError::internal(format!("Failed to update role: {e}"))
            }
        })?;

        // Return the updated role
        self.get_role(role_id)
            .await?
            .ok_or_else(|| AuthError::NotFound {
                resource: "role".to_string(),
                id: role_id.to_string(),
            })
    }

    async fn delete_role(&self, role_id: RoleId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Check if role exists first
        let role_exists = conn
            .query_all(
                &format!("SELECT id FROM {} WHERE id = ?", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check role existence: {e}")))?;

        if role_exists.is_empty() {
            return Ok(false); // Role doesn't exist
        }

        // Check if role has child roles (prevent deletion if it has children)
        let child_roles = conn
            .query_all(
                &format!(
                    "SELECT id FROM {} WHERE parent_id = ?",
                    self.table_name("roles")
                ),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check child roles: {e}")))?;

        if !child_roles.is_empty() {
            return Err(AuthError::Validation {
                message: "Cannot delete role that has child roles".to_string(),
                field: Some("parent_id".to_string()),
            });
        }

        // Delete the role (CASCADE will handle role_permissions and user_role_assignments)
        let result = conn
            .execute(
                &format!("DELETE FROM {} WHERE id = ?", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to delete role: {e}")))?;

        Ok(result > 0)
    }

    async fn list_roles(&self, filter: RoleFilter) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let mut query = format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata FROM {}", self.table_name("roles"));
        let mut conditions = Vec::new();
        let mut params = Vec::new();

        // Apply filters
        if let Some(name) = &filter.name {
            conditions.push("name LIKE ?");
            params.push(DatabaseValue::Text(format!("%{name}%")));
        }

        if let Some(parent_id) = &filter.parent_id {
            conditions.push("parent_id = ?");
            params.push(DatabaseValue::Text(parent_id.to_string()));
        }

        if let Some(level) = filter.level {
            conditions.push("level = ?");
            params.push(DatabaseValue::I64(level as i64));
        }

        if let Some(is_system) = filter.is_system {
            conditions.push("is_system = ?");
            params.push(DatabaseValue::I64(if is_system { 1 } else { 0 }));
        }

        if let Some(is_active) = filter.is_active {
            conditions.push("is_active = ?");
            params.push(DatabaseValue::I64(if is_active { 1 } else { 0 }));
        }

        if !conditions.is_empty() {
            query.push_str(" WHERE ");
            query.push_str(&conditions.join(" AND "));
        }

        // Add ordering
        query.push_str(" ORDER BY level ASC, name ASC");

        // Add pagination
        if let Some(limit) = filter.limit {
            query.push_str(" LIMIT ?");
            params.push(DatabaseValue::I64(limit as i64));

            if let Some(offset) = filter.offset {
                query.push_str(" OFFSET ?");
                params.push(DatabaseValue::I64(offset as i64));
            }
        }

        let rows = conn
            .query_all(&query, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to list roles: {e}")))?;

        let mut roles = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    if let Ok(parent_value) = row.get_by_index(3) {
                        let parent_str = parent_value.to_string();
                        if parent_str.is_empty() || parent_str == "null" {
                            None
                        } else {
                            Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                        }
                    } else {
                        None
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: row.get_by_index(5).unwrap().to_string() == "1",
                is_active: row.get_by_index(6).unwrap().to_string() == "1",
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    if let Ok(meta_value) = row.get_by_index(9) {
                        let meta_str = meta_value.to_string();
                        if meta_str.is_empty() || meta_str == "null" {
                            serde_json::Value::Null
                        } else {
                            serde_json::Value::String(meta_str)
                        }
                    } else {
                        serde_json::Value::Null
                    }
                },
            };
            roles.push(role);
        }

        Ok(roles)
    }

    // RBAC operations - Permission management
    async fn create_permission(&self, request: CreatePermissionRequest) -> Result<Permission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let permission_id = uuid::Uuid::new_v4().to_string();
        let now = chrono::Utc::now().timestamp();

        conn.execute(
            &format!("INSERT INTO {} (id, name, description, resource, action, created_at, updated_at, metadata)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)", self.table_name("permissions")),
            &[
                DatabaseValue::Text(permission_id.clone()),
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                DatabaseValue::Text(request.resource.clone()),
                DatabaseValue::Text(request.action.clone()),
                DatabaseValue::I64(now),
                DatabaseValue::I64(now),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("UNIQUE constraint failed") {
                AuthError::Conflict {
                    resource: "permission".to_string(),
                    field: "resource_action".to_string(),
                    value: format!("{}:{}", request.resource, request.action),
                }
            } else {
                AuthError::internal(format!("Failed to create permission: {e}"))
            }
        })?;

        Ok(Permission {
            id: PermissionId::from(uuid::Uuid::parse_str(&permission_id).unwrap()),
            name: request.name,
            description: request.description,
            resource: request.resource,
            action: request.action,
            created_at: chrono::DateTime::from_timestamp(now, 0).unwrap(),
            updated_at: chrono::DateTime::from_timestamp(now, 0).unwrap(),
            metadata: request.metadata.unwrap_or(serde_json::Value::Null),
        })
    }

    async fn get_permission(&self, permission_id: PermissionId) -> Result<Option<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, name, description, resource, action, created_at, updated_at, metadata 
             FROM {} WHERE id = ?", self.table_name("permissions")),
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get permission: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let permission = Permission {
            id: PermissionId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            resource: row.get_by_index(3).unwrap().to_string(),
            action: row.get_by_index(4).unwrap().to_string(),
            created_at: {
                let timestamp: i64 = row
                    .get_by_index(5)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            updated_at: {
                let timestamp: i64 = row
                    .get_by_index(6)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            metadata: {
                if let Ok(meta_value) = row.get_by_index(7) {
                    let meta_str = meta_value.to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                } else {
                    serde_json::Value::Null
                }
            },
        };

        Ok(Some(permission))
    }

    async fn get_permission_by_key(
        &self,
        resource: &str,
        action: &str,
    ) -> Result<Option<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, name, description, resource, action, created_at, updated_at, metadata 
             FROM {} WHERE resource = ? AND action = ?", self.table_name("permissions")),
                &[
                    DatabaseValue::Text(resource.to_string()),
                    DatabaseValue::Text(action.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get permission by key: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let permission = Permission {
            id: PermissionId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            resource: row.get_by_index(3).unwrap().to_string(),
            action: row.get_by_index(4).unwrap().to_string(),
            created_at: {
                let timestamp: i64 = row
                    .get_by_index(5)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            updated_at: {
                let timestamp: i64 = row
                    .get_by_index(6)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            metadata: {
                if let Ok(meta_value) = row.get_by_index(7) {
                    let meta_str = meta_value.to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                } else {
                    serde_json::Value::Null
                }
            },
        };

        Ok(Some(permission))
    }

    async fn update_permission(
        &self,
        permission_id: PermissionId,
        request: CreatePermissionRequest,
    ) -> Result<Permission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let now = chrono::Utc::now().timestamp();

        conn.execute(
            &format!("UPDATE {} SET name = ?, description = ?, resource = ?, action = ?, updated_at = ?, metadata = ? WHERE id = ?", self.table_name("permissions")),
            &[
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                DatabaseValue::Text(request.resource.clone()),
                DatabaseValue::Text(request.action.clone()),
                DatabaseValue::I64(now),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(permission_id.to_string()),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("UNIQUE constraint failed") {
                AuthError::Conflict {
                    resource: "permission".to_string(),
                    field: "resource_action".to_string(),
                    value: format!("{}:{}", request.resource, request.action),
                }
            } else {
                AuthError::internal(format!("Failed to update permission: {e}"))
            }
        })?;

        // Return the updated permission
        self.get_permission(permission_id)
            .await?
            .ok_or_else(|| AuthError::NotFound {
                resource: "permission".to_string(),
                id: permission_id.to_string(),
            })
    }

    async fn delete_permission(&self, permission_id: PermissionId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Check if permission exists first
        let permission_exists = conn
            .query_all(
                &format!(
                    "SELECT id FROM {} WHERE id = ?",
                    self.table_name("permissions")
                ),
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| {
                AuthError::internal(format!("Failed to check permission existence: {e}"))
            })?;

        if permission_exists.is_empty() {
            return Ok(false); // Permission doesn't exist
        }

        // Delete the permission (CASCADE will handle role_permissions)
        let result = conn
            .execute(
                &format!(
                    "DELETE FROM {} WHERE id = ?",
                    self.table_name("permissions")
                ),
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to delete permission: {e}")))?;

        Ok(result > 0)
    }

    async fn list_permissions(&self, filter: PermissionFilter) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let mut query = format!("SELECT id, name, description, resource, action, created_at, updated_at, metadata FROM {}", self.table_name("permissions"));
        let mut conditions = Vec::new();
        let mut params = Vec::new();

        // Apply filters
        if let Some(name) = &filter.name {
            conditions.push("name LIKE ?");
            params.push(DatabaseValue::Text(format!("%{name}%")));
        }

        if let Some(resource) = &filter.resource {
            conditions.push("resource = ?");
            params.push(DatabaseValue::Text(resource.clone()));
        }

        if let Some(action) = &filter.action {
            conditions.push("action = ?");
            params.push(DatabaseValue::Text(action.clone()));
        }

        if !conditions.is_empty() {
            query.push_str(" WHERE ");
            query.push_str(&conditions.join(" AND "));
        }

        // Add ordering
        query.push_str(" ORDER BY resource ASC, action ASC, name ASC");

        // Add pagination
        if let Some(limit) = filter.limit {
            query.push_str(" LIMIT ?");
            params.push(DatabaseValue::I64(limit as i64));

            if let Some(offset) = filter.offset {
                query.push_str(" OFFSET ?");
                params.push(DatabaseValue::I64(offset as i64));
            }
        }

        let rows = conn
            .query_all(&query, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to list permissions: {e}")))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    if let Ok(meta_value) = row.get_by_index(7) {
                        let meta_str = meta_value.to_string();
                        if meta_str.is_empty() || meta_str == "null" {
                            serde_json::Value::Null
                        } else {
                            serde_json::from_str(&meta_str)
                                .unwrap_or(serde_json::Value::String(meta_str))
                        }
                    } else {
                        serde_json::Value::Null
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }

    // RBAC operations - Role-Permission assignments
    async fn assign_permission_to_role(
        &self,
        role_id: RoleId,
        permission_id: PermissionId,
        granted_by: Option<UserId>,
    ) -> Result<RolePermission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let now = chrono::Utc::now().timestamp();

        // Check if role exists
        let role_rows = conn
            .query_all(
                &format!("SELECT id FROM {} WHERE id = ?", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check role: {e}")))?;

        if role_rows.is_empty() {
            return Err(AuthError::NotFound {
                resource: "role".to_string(),
                id: role_id.to_string(),
            });
        }

        // Check if permission exists
        let permission_rows = conn
            .query_all(
                &format!(
                    "SELECT id FROM {} WHERE id = ?",
                    self.table_name("permissions")
                ),
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check permission: {e}")))?;

        if permission_rows.is_empty() {
            return Err(AuthError::NotFound {
                resource: "permission".to_string(),
                id: permission_id.to_string(),
            });
        }

        conn.execute(
            &format!("INSERT OR REPLACE INTO {} (role_id, permission_id, granted, granted_by, granted_at)
             VALUES (?, ?, ?, ?, ?)", self.table_name("role_permissions")),
            &[
                DatabaseValue::Text(role_id.to_string()),
                DatabaseValue::Text(permission_id.to_string()),
                DatabaseValue::I64(1), // granted = true (SQLite INTEGER)
                granted_by.map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(now),
            ],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to assign permission to role: {e}")))?;

        Ok(RolePermission {
            role_id,
            permission_id,
            granted: true,
            granted_by,
            granted_at: chrono::DateTime::from_timestamp(now, 0).unwrap(),
            metadata: serde_json::Value::Null,
        })
    }

    async fn revoke_permission_from_role(
        &self,
        role_id: RoleId,
        permission_id: PermissionId,
    ) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let result = conn
            .execute(
                &format!(
                    "DELETE FROM {} WHERE role_id = ? AND permission_id = ?",
                    self.table_name("role_permissions")
                ),
                &[
                    DatabaseValue::Text(role_id.to_string()),
                    DatabaseValue::Text(permission_id.to_string()),
                ],
            )
            .await
            .map_err(|e| {
                AuthError::internal(format!("Failed to revoke permission from role: {e}"))
            })?;

        Ok(result > 0)
    }

    async fn get_role_permissions(&self, role_id: RoleId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
                 FROM {} p
                 JOIN {} rp ON p.id = rp.permission_id
                 WHERE rp.role_id = ? AND rp.granted = 1
                 ORDER BY p.resource ASC, p.action ASC, p.name ASC", 
                    self.table_name("permissions"), self.table_name("role_permissions")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get role permissions: {e}")))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    if let Ok(meta_value) = row.get_by_index(7) {
                        let meta_str = meta_value.to_string();
                        if meta_str.is_empty() || meta_str == "null" {
                            serde_json::Value::Null
                        } else {
                            serde_json::from_str(&meta_str)
                                .unwrap_or(serde_json::Value::String(meta_str))
                        }
                    } else {
                        serde_json::Value::Null
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }

    // RBAC operations - User-Role assignments
    async fn assign_role_to_user(
        &self,
        user_id: UserId,
        role_id: RoleId,
        assigned_by: Option<UserId>,
    ) -> Result<UserRoleAssignment> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let now = chrono::Utc::now().timestamp();

        // Check if user exists
        let user_rows = conn
            .query_all(
                &format!("SELECT id FROM {} WHERE id = ?", self.table_name("users")),
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check user: {e}")))?;

        if user_rows.is_empty() {
            return Err(AuthError::NotFound {
                resource: "user".to_string(),
                id: user_id.to_string(),
            });
        }

        // Check if role exists
        let role_rows = conn
            .query_all(
                &format!("SELECT id FROM {} WHERE id = ?", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check role: {e}")))?;

        if role_rows.is_empty() {
            return Err(AuthError::NotFound {
                resource: "role".to_string(),
                id: role_id.to_string(),
            });
        }

        conn.execute(
            &format!("INSERT OR REPLACE INTO {} (user_id, role_id, is_active, assigned_by, assigned_at, expires_at)
             VALUES (?, ?, ?, ?, ?, ?)", self.table_name("user_role_assignments")),
            &[
                DatabaseValue::Text(user_id.to_string()),
                DatabaseValue::Text(role_id.to_string()),
                DatabaseValue::I64(1), // is_active = true (SQLite INTEGER)
                assigned_by.map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(now),
                DatabaseValue::Null, // No expiration by default
            ],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to assign role to user: {e}")))?;

        Ok(UserRoleAssignment {
            user_id,
            role_id,
            is_active: true,
            assigned_by,
            assigned_at: chrono::DateTime::from_timestamp(now, 0).unwrap(),
            expires_at: None,
            metadata: serde_json::Value::Null,
        })
    }

    async fn revoke_role_from_user(&self, user_id: UserId, role_id: RoleId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let result = conn
            .execute(
                &format!(
                    "DELETE FROM {} WHERE user_id = ? AND role_id = ?",
                    self.table_name("user_role_assignments")
                ),
                &[
                    DatabaseValue::Text(user_id.to_string()),
                    DatabaseValue::Text(role_id.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to revoke role from user: {e}")))?;

        Ok(result > 0)
    }

    async fn get_user_roles(&self, user_id: UserId) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT r.id, r.name, r.description, r.parent_id, r.level, r.is_system, r.is_active, r.created_at, r.updated_at, r.metadata 
                 FROM {} r
                 JOIN {} ura ON r.id = ura.role_id
                 WHERE ura.user_id = ? 
                   AND ura.is_active = 1 
                   AND (ura.expires_at IS NULL OR ura.expires_at > datetime('now'))
                 ORDER BY r.level ASC, r.name ASC", self.table_name("roles"), self.table_name("user_role_assignments")),
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get user roles: {e}")))?;

        let mut roles = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    if let Ok(parent_value) = row.get_by_index(3) {
                        let parent_str = parent_value.to_string();
                        if parent_str.is_empty() || parent_str == "null" {
                            None
                        } else {
                            Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                        }
                    } else {
                        None
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: row.get_by_index(5).unwrap().to_string() == "1",
                is_active: row.get_by_index(6).unwrap().to_string() == "1",
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    if let Ok(meta_value) = row.get_by_index(9) {
                        let meta_str = meta_value.to_string();
                        if meta_str.is_empty() || meta_str == "null" {
                            serde_json::Value::Null
                        } else {
                            serde_json::from_str(&meta_str)
                                .unwrap_or(serde_json::Value::String(meta_str))
                        }
                    } else {
                        serde_json::Value::Null
                    }
                },
            };
            roles.push(role);
        }

        Ok(roles)
    }

    async fn get_users_with_role(&self, role_id: RoleId) -> Result<Vec<UserId>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT user_id FROM {} 
                 WHERE role_id = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > datetime('now'))
                 ORDER BY assigned_at ASC", self.table_name("user_role_assignments")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get users with role: {e}")))?;

        let mut user_ids = Vec::new();
        for row in rows {
            let user_id = UserId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            );
            user_ids.push(user_id);
        }

        Ok(user_ids)
    }

    // RBAC operations - Permission checking
    async fn check_permission(&self, check: PermissionCheck) -> Result<PermissionResult> {
        let has_permission = self
            .has_permission(check.user_id, &check.resource, &check.action)
            .await?;

        // Get matched permissions if access is granted
        let matched_permissions = if has_permission {
            self.get_user_permissions(check.user_id)
                .await?
                .into_iter()
                .filter(|p| p.resource == check.resource && p.action == check.action)
                .collect()
        } else {
            Vec::new()
        };

        // Get effective roles for the user
        let effective_roles = self.get_user_roles(check.user_id).await?;

        Ok(PermissionResult {
            allowed: has_permission,
            reason: if has_permission {
                Some("Permission granted through role assignment".to_string())
            } else {
                Some("Permission denied - no matching role permissions".to_string())
            },
            matched_permissions,
            effective_roles,
        })
    }

    async fn has_permission(&self, user_id: UserId, resource: &str, action: &str) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Check permission through role hierarchy using recursive CTE
        // This query checks permissions from direct role assignments and inherited from parent roles
        let rows = conn
            .query_all(
                &format!(
                    "WITH RECURSIVE role_hierarchy AS (
                        -- Base case: direct user role assignments
                        SELECT r.id, r.parent_id, r.level
                        FROM {} r
                        JOIN {} ura ON r.id = ura.role_id
                        WHERE ura.user_id = ? 
                          AND ura.is_active = 1 
                          AND (ura.expires_at IS NULL OR ura.expires_at > datetime('now'))
                        
                        UNION ALL
                        
                        -- Recursive case: parent roles
                        SELECT r.id, r.parent_id, r.level
                        FROM {} r
                        JOIN role_hierarchy rh ON r.id = rh.parent_id
                        WHERE r.is_active = 1
                    )
                    SELECT COUNT(*) FROM role_hierarchy rh
                    JOIN {} rp ON rh.id = rp.role_id
                    JOIN {} p ON rp.permission_id = p.id
                    WHERE rp.granted = 1
                      AND p.resource = ? 
                      AND p.action = ?",
                    self.table_name("roles"),
                    self.table_name("user_role_assignments"),
                    self.table_name("roles"),
                    self.table_name("role_permissions"),
                    self.table_name("permissions")
                ),
                &[
                    DatabaseValue::Text(user_id.to_string()),
                    DatabaseValue::Text(resource.to_string()),
                    DatabaseValue::Text(action.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check permission: {e}")))?;

        if rows.is_empty() {
            return Ok(false);
        }

        let count: i64 = rows[0]
            .get_by_index(0)
            .unwrap_or(DatabaseValue::I64(0))
            .to_string()
            .parse()
            .map_err(|_| AuthError::internal("Invalid permission count"))?;

        Ok(count > 0)
    }

    async fn get_user_permissions(&self, user_id: UserId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT DISTINCT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
                 FROM {} p
                 JOIN {} rp ON p.id = rp.permission_id
                 JOIN {} ura ON rp.role_id = ura.role_id
                 WHERE ura.user_id = ? 
                   AND ura.is_active = 1 
                   AND (ura.expires_at IS NULL OR ura.expires_at > datetime('now'))
                   AND rp.granted = 1
                 ORDER BY p.resource ASC, p.action ASC, p.name ASC", 
                    self.table_name("permissions"), self.table_name("role_permissions"), self.table_name("user_role_assignments")),
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get user permissions: {e}")))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    if let Ok(meta_value) = row.get_by_index(7) {
                        let meta_str = meta_value.to_string();
                        if meta_str.is_empty() || meta_str == "null" {
                            serde_json::Value::Null
                        } else {
                            serde_json::from_str(&meta_str)
                                .unwrap_or(serde_json::Value::String(meta_str))
                        }
                    } else {
                        serde_json::Value::Null
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }

    // RBAC operations - Role hierarchy
    async fn get_role_hierarchy(&self, role_id: RoleId) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Use recursive CTE to get the complete role hierarchy
        // SQLite 3.8.3+ supports recursive CTEs
        let rows = conn
            .query_all(
                &format!("WITH RECURSIVE role_hierarchy AS (
                    -- Base case: start with the given role
                    SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata, 0 as depth
                    FROM {} 
                    WHERE id = ? AND is_active = 1
                    
                    UNION ALL
                    
                    -- Recursive case: get parent roles
                    SELECT r.id, r.name, r.description, r.parent_id, r.level, r.is_system, r.is_active, r.created_at, r.updated_at, r.metadata, rh.depth + 1
                    FROM {} r
                    INNER JOIN role_hierarchy rh ON r.id = rh.parent_id
                    WHERE r.is_active = 1 AND rh.depth < 10
                )
                SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata
                FROM role_hierarchy
                ORDER BY depth ASC, level ASC", self.table_name("roles"), self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get role hierarchy: {e}")))?;

        let mut hierarchy = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    if let Ok(parent_value) = row.get_by_index(3) {
                        let parent_str = parent_value.to_string().to_lowercase();
                        if parent_str.is_empty() || parent_str == "null" {
                            None
                        } else {
                            Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                        }
                    } else {
                        None
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: row.get_by_index(5).unwrap().to_string() == "1",
                is_active: row.get_by_index(6).unwrap().to_string() == "1",
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    if let Ok(meta_value) = row.get_by_index(9) {
                        let meta_str = meta_value.to_string();
                        if meta_str.is_empty() || meta_str == "null" {
                            serde_json::Value::Null
                        } else {
                            serde_json::from_str(&meta_str)
                                .unwrap_or(serde_json::Value::String(meta_str))
                        }
                    } else {
                        serde_json::Value::Null
                    }
                },
            };
            hierarchy.push(role);
        }

        Ok(hierarchy)
    }

    async fn get_effective_permissions(&self, user_id: UserId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Get all permissions for the user including inherited permissions from role hierarchy
        // This query gets permissions from direct role assignments and parent roles
        let rows = conn
            .query_all(
                &format!("WITH RECURSIVE role_hierarchy AS (
                    -- Base case: direct user role assignments
                    SELECT r.id, r.parent_id, r.level
                    FROM {} r
                    JOIN {} ura ON r.id = ura.role_id
                    WHERE ura.user_id = ? 
                      AND ura.is_active = 1 
                      AND (ura.expires_at IS NULL OR ura.expires_at > datetime('now'))
                    
                    UNION ALL
                    
                    -- Recursive case: parent roles
                    SELECT r.id, r.parent_id, r.level
                    FROM {} r
                    JOIN role_hierarchy rh ON r.id = rh.parent_id
                    WHERE r.is_active = 1
                )
                SELECT DISTINCT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
                FROM role_hierarchy rh
                JOIN {} rp ON rh.id = rp.role_id
                JOIN {} p ON rp.permission_id = p.id
                WHERE rp.granted = 1
                ORDER BY p.resource ASC, p.action ASC, p.name ASC",
                self.table_name("roles"),
                self.table_name("user_role_assignments"),
                self.table_name("roles"),
                self.table_name("role_permissions"),
                self.table_name("permissions")
                ),
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get effective permissions: {e}")))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    if let Ok(meta_value) = row.get_by_index(7) {
                        let meta_str = meta_value.to_string();
                        if meta_str.is_empty() || meta_str == "null" {
                            serde_json::Value::Null
                        } else {
                            serde_json::from_str(&meta_str)
                                .unwrap_or(serde_json::Value::String(meta_str))
                        }
                    } else {
                        serde_json::Value::Null
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }

    async fn begin_transaction(&self) -> Result<Box<dyn DatabaseTransaction>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let native_transaction = conn
            .begin_transaction()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to begin transaction: {e}")))?;

        Ok(Box::new(
            crate::database::transaction_bridge::TransactionBridge::new(native_transaction),
        ))
    }

    // User attributes operations
    async fn set_attribute(
        &self,
        user_id: UserId,
        request: UserAttributeRequest,
    ) -> Result<UserAttribute> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let attribute_id = uuid::Uuid::new_v4().to_string();
        let user_id_str = user_id.to_string();
        let now = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();

        // Determine value type and column values
        let (value_type, value_string, value_number, value_boolean, value_json) =
            match &request.value {
                UserAttributeValue::String(s) => ("string", Some(s.clone()), None, None, None),
                UserAttributeValue::Number(n) => ("number", None, Some(*n), None, None),
                UserAttributeValue::Integer(i) => ("number", None, Some(*i as f64), None, None),
                UserAttributeValue::Boolean(b) => {
                    ("boolean", None, None, Some(if *b { 1 } else { 0 }), None)
                }
                UserAttributeValue::Json(j) => (
                    "json",
                    None,
                    None,
                    None,
                    Some(serde_json::to_string(j).unwrap_or_default()),
                ),
                UserAttributeValue::Array(a) => (
                    "json",
                    None,
                    None,
                    None,
                    Some(serde_json::to_string(a).unwrap_or_default()),
                ),
            };

        // Use INSERT OR REPLACE for upsert behavior, with a cached SQL string
        let sql = self.get_cached_set_attribute_sql();

        let params = vec![
            DatabaseValue::Text(attribute_id.clone()),
            DatabaseValue::Text(user_id_str),
            DatabaseValue::Text(request.key.clone()),
            DatabaseValue::Text(value_type.to_string()),
            value_string
                .map(DatabaseValue::Text)
                .unwrap_or(DatabaseValue::Null),
            value_number
                .map(DatabaseValue::F64)
                .unwrap_or(DatabaseValue::Null),
            value_boolean
                .map(DatabaseValue::I32)
                .unwrap_or(DatabaseValue::Null),
            value_json
                .map(DatabaseValue::Text)
                .unwrap_or(DatabaseValue::Null),
            DatabaseValue::Text(user_id.to_string()),
            DatabaseValue::Text(request.key.clone()),
            DatabaseValue::Text(now.clone()),
            DatabaseValue::Text(now),
        ];

        conn.execute(&sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to set user attribute: {e}")))?;

        // Return the created/updated attribute
        Ok(UserAttribute {
            id: crate::types::UserAttributeId::from(uuid::Uuid::parse_str(&attribute_id).unwrap()),
            user_id,
            key: request.key,
            value: request.value,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            metadata: request.metadata.unwrap_or_default(),
        })
    }

    async fn get_attribute(&self, user_id: UserId, key: &str) -> Result<Option<UserAttribute>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let sql = format!("SELECT id, user_id, key, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                   FROM {} WHERE user_id = ? AND key = ?", self.table_name("user_attributes"));

        let params = vec![
            DatabaseValue::Text(user_id.to_string()),
            DatabaseValue::Text(key.to_string()),
        ];

        let rows = conn
            .query_all(&sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get user attribute: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        Ok(Some(self.row_to_user_attribute(row.as_ref())?))
    }

    async fn get_user_attributes(&self, user_id: UserId) -> Result<UserAttributes> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let sql = format!("SELECT id, user_id, key, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                   FROM {} WHERE user_id = ? ORDER BY key", self.table_name("user_attributes"));

        let params = vec![DatabaseValue::Text(user_id.to_string())];

        let rows = conn
            .query_all(&sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get user attributes: {e}")))?;

        let mut attributes = std::collections::HashMap::new();
        let mut last_updated = chrono::Utc::now();

        for row in rows {
            let attr = self.row_to_user_attribute(row.as_ref())?;
            if attr.updated_at > last_updated {
                last_updated = attr.updated_at;
            }
            attributes.insert(attr.key.clone(), attr.value);
        }

        Ok(UserAttributes {
            user_id,
            attributes,
            last_updated,
        })
    }

    async fn delete_attribute(&self, user_id: UserId, key: &str) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let sql = format!(
            "DELETE FROM {} WHERE user_id = ? AND key = ?",
            self.table_name("user_attributes")
        );
        let params = vec![
            DatabaseValue::Text(user_id.to_string()),
            DatabaseValue::Text(key.to_string()),
        ];

        let affected = conn
            .execute(&sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to delete user attribute: {e}")))?;

        Ok(affected > 0)
    }

    async fn delete_user_attributes(&self, user_id: UserId) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let sql = format!(
            "DELETE FROM {} WHERE user_id = ?",
            self.table_name("user_attributes")
        );
        let params = vec![DatabaseValue::Text(user_id.to_string())];

        let affected = conn
            .execute(&sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to delete user attributes: {e}")))?;

        Ok(affected as u64)
    }

    async fn update_attribute(
        &self,
        user_id: UserId,
        key: &str,
        value: UserAttributeValue,
    ) -> Result<UserAttribute> {
        // Use set_attribute for upsert behavior
        let request = UserAttributeRequest {
            key: key.to_string(),
            value,
            metadata: None,
        };
        self.set_attribute(user_id, request).await
    }

    async fn search_attributes(&self, filter: UserAttributeFilter) -> Result<Vec<UserAttribute>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let mut sql = format!("SELECT id, user_id, key, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                       FROM {} WHERE 1=1", self.table_name("user_attributes"));
        let mut params = Vec::new();

        if let Some(user_id) = filter.user_id {
            sql.push_str(" AND user_id = ?");
            params.push(DatabaseValue::Text(user_id.to_string()));
        }

        if let Some(keys) = filter.keys {
            if !keys.is_empty() {
                let placeholders = keys.iter().map(|_| "?").collect::<Vec<_>>().join(",");
                sql.push_str(&format!(" AND key IN ({placeholders})"));
                for key in keys {
                    params.push(DatabaseValue::Text(key));
                }
            }
        }

        if let Some(value_type) = filter.value_type {
            sql.push_str(" AND value_type = ?");
            params.push(DatabaseValue::Text(value_type));
        }

        if let Some(created_after) = filter.created_after {
            sql.push_str(" AND created_at > ?");
            params.push(DatabaseValue::Text(
                created_after.format("%Y-%m-%d %H:%M:%S").to_string(),
            ));
        }

        if let Some(created_before) = filter.created_before {
            sql.push_str(" AND created_at < ?");
            params.push(DatabaseValue::Text(
                created_before.format("%Y-%m-%d %H:%M:%S").to_string(),
            ));
        }

        sql.push_str(" ORDER BY created_at DESC");

        if let Some(limit) = filter.limit {
            sql.push_str(&format!(" LIMIT {limit}"));
            if let Some(offset) = filter.offset {
                sql.push_str(&format!(" OFFSET {offset}"));
            }
        }

        let rows = conn
            .query_all(&sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to search user attributes: {e}")))?;

        let mut results = Vec::new();
        for row in rows {
            results.push(self.row_to_user_attribute(row.as_ref())?);
        }

        Ok(results)
    }

    async fn find_users_by_attribute(
        &self,
        key: &str,
        value: &UserAttributeValue,
    ) -> Result<Vec<UserId>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let (sql, params) = match value {
            UserAttributeValue::String(s) => (
                &format!("SELECT DISTINCT user_id FROM {} WHERE key = ? AND value_type = 'string' AND value_string = ?", self.table_name("user_attributes")),
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(s.clone())]
            ),
            UserAttributeValue::Number(n) => (
                &format!("SELECT DISTINCT user_id FROM {} WHERE key = ? AND value_type = 'number' AND value_number = ?", self.table_name("user_attributes")),
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::F64(*n)]
            ),
            UserAttributeValue::Integer(i) => (
                &format!("SELECT DISTINCT user_id FROM {} WHERE key = ? AND value_type = 'number' AND value_number = ?", self.table_name("user_attributes")),
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::F64(*i as f64)]
            ),
            UserAttributeValue::Boolean(b) => (
                &format!("SELECT DISTINCT user_id FROM {} WHERE key = ? AND value_type = 'boolean' AND value_boolean = ?", self.table_name("user_attributes")),
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::I32(if *b { 1 } else { 0 })]
            ),
            UserAttributeValue::Json(j) => (
                &format!("SELECT DISTINCT user_id FROM {} WHERE key = ? AND value_type = 'json' AND value_json = ?", self.table_name("user_attributes")),
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(serde_json::to_string(j).unwrap_or_default())]
            ),
            UserAttributeValue::Array(a) => (
                &format!("SELECT DISTINCT user_id FROM {} WHERE key = ? AND value_type = 'json' AND value_json = ?", self.table_name("user_attributes")),
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(serde_json::to_string(a).unwrap_or_default())]
            ),
        };

        let rows = conn
            .query_all(sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to find users by attribute: {e}")))?;

        let mut user_ids = Vec::new();
        for row in rows {
            if let Ok(DatabaseValue::Text(user_id_str)) = row.get_by_index(0) {
                if let Ok(uuid) = uuid::Uuid::parse_str(&user_id_str) {
                    user_ids.push(UserId::from(uuid));
                }
            }
        }

        Ok(user_ids)
    }

    async fn set_attributes(
        &self,
        user_id: UserId,
        attributes: std::collections::HashMap<String, UserAttributeValue>,
    ) -> Result<Vec<UserAttribute>> {
        let mut results = Vec::new();

        for (key, value) in attributes {
            let request = UserAttributeRequest {
                key,
                value,
                metadata: None,
            };
            let attr = self.set_attribute(user_id, request).await?;
            results.push(attr);
        }

        Ok(results)
    }
}

impl NativeSqliteProvider {
    // Helper method to convert database row to UserAttribute
    fn row_to_user_attribute(
        &self,
        row: &dyn crate::database::native::common::traits::DatabaseRow,
    ) -> Result<UserAttribute> {
        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                _ => String::new(),
            }
        };

        let get_f64 = |idx: usize| -> Option<f64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::F64(f) => Some(f),
                DatabaseValue::I32(i) => Some(i as f64),
                DatabaseValue::I64(i) => Some(i as f64),
                _ => None,
            }
        };

        let get_i32 = |idx: usize| -> Option<i32> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::I32(i) => Some(i),
                DatabaseValue::I64(i) => Some(i as i32),
                _ => None,
            }
        };

        let id_str = get_text(0);
        let user_id_str = get_text(1);
        let key = get_text(2);
        let value_type = get_text(3);
        let value_string = get_text(4);
        let value_number = get_f64(5);
        let value_boolean = get_i32(6);
        let value_json = get_text(7);
        let created_at_str = get_text(8);
        let updated_at_str = get_text(9);

        // Parse UUIDs
        let id = uuid::Uuid::parse_str(&id_str)
            .map_err(|_| AuthError::internal("Invalid attribute ID"))?;
        let user_id = uuid::Uuid::parse_str(&user_id_str)
            .map_err(|_| AuthError::internal("Invalid user ID"))?;

        // Parse timestamps
        let created_at = chrono::DateTime::parse_from_str(&created_at_str, "%Y-%m-%d %H:%M:%S")
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .unwrap_or_else(|_| chrono::Utc::now());
        let updated_at = chrono::DateTime::parse_from_str(&updated_at_str, "%Y-%m-%d %H:%M:%S")
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .unwrap_or_else(|_| chrono::Utc::now());

        // Reconstruct the value based on type
        let value = match value_type.as_str() {
            "string" => UserAttributeValue::String(value_string),
            "number" => {
                if let Some(n) = value_number {
                    // Check if it's an integer value
                    if n.fract() == 0.0 && n >= i64::MIN as f64 && n <= i64::MAX as f64 {
                        UserAttributeValue::Integer(n as i64)
                    } else {
                        UserAttributeValue::Number(n)
                    }
                } else {
                    UserAttributeValue::Number(0.0)
                }
            }
            "boolean" => UserAttributeValue::Boolean(value_boolean.unwrap_or(0) != 0),
            "json" => {
                if value_json.is_empty() {
                    UserAttributeValue::Json(serde_json::Value::Null)
                } else {
                    match serde_json::from_str(&value_json) {
                        Ok(serde_json::Value::Array(arr)) => UserAttributeValue::Array(arr),
                        Ok(json_val) => UserAttributeValue::Json(json_val),
                        Err(_) => UserAttributeValue::String(value_json),
                    }
                }
            }
            _ => UserAttributeValue::String(value_string),
        };

        Ok(UserAttribute {
            id: crate::types::UserAttributeId::from(id),
            user_id: UserId::from(user_id),
            key,
            value,
            created_at,
            updated_at,
            metadata: serde_json::Value::Object(serde_json::Map::new()),
        })
    }
}

// Note: Transaction implementation is handled by the native database layer
// The begin_transaction method returns the native transaction directly
