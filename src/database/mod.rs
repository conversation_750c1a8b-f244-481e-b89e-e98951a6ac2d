//! Database abstraction layer and connection management

pub mod connection;
pub mod factory;
pub mod migration;
pub mod pool;

// Native database drivers
pub mod native;

// Native providers
#[cfg(feature = "sqlite-native")]
pub mod native_sqlite;

#[cfg(feature = "postgres-native")]
pub mod native_postgres;

#[cfg(feature = "mysql-native")]
pub mod native_mysql;

// Transaction bridge
pub mod transaction_bridge;

// Re-export main types
use std::time::Duration;

use async_trait::async_trait;
pub use connection::*;
pub use factory::{DatabaseProviderBuilder, DatabaseProviderFactory};
pub use migration::*;
#[cfg(feature = "mysql-native")]
pub use native_mysql::NativeMysqlProvider;
#[cfg(feature = "postgres-native")]
pub use native_postgres::NativePostgresProvider;
// Re-export native providers
#[cfg(feature = "sqlite-native")]
pub use native_sqlite::NativeSqliteProvider;
pub use pool::*;

use crate::{
    error::Result,
    types::{
        CreatePermissionRequest, CreateRoleRequest, Permission, PermissionCheck, PermissionFilter,
        PermissionId, PermissionResult, Role, RoleFilter, RoleId, RolePermission, Session,
        SessionId, User, UserAttribute, UserAttributeFilter, UserAttributeRequest,
        UserAttributeValue, UserAttributes, UserId, UserRoleAssignment,
    },
};

/// Database provider trait with comprehensive operations including RBAC
#[async_trait]
pub trait DatabaseProvider: Send + Sync {
    // Provider metadata
    /// Get the provider type as an enum
    fn provider_type(&self) -> connection::DatabaseType;

    /// Check if the provider supports a specific feature
    fn supports_feature(&self, feature: &str) -> bool {
        matches!(feature, "transactions" | "rbac" | "auth")
    }

    /// Get detailed provider capabilities and information
    fn get_capabilities(&self) -> DatabaseCapabilities {
        DatabaseCapabilities {
            provider_type: self.provider_type(),
            supports_transactions: self.supports_feature("transactions"),
            supports_rbac: self.supports_feature("rbac"),
            supports_json: self.supports_feature("json"),
            supports_full_text_search: self.supports_feature("full_text_search"),
            max_connections: None,
            version: None,
        }
    }

    /// Initialize the database (create tables, run migrations)
    async fn initialize(&self) -> Result<()>;

    /// Health check for the database connection
    async fn health_check(&self) -> Result<DatabaseHealth>;

    /// Get database statistics
    async fn get_stats(&self) -> Result<DatabaseStats>;

    // User operations
    /// Create a new user
    async fn create_user(&self, user: &User) -> Result<UserId>;

    /// Get user by ID
    async fn get_user_by_id(&self, user_id: UserId) -> Result<Option<User>>;

    /// Get user by username
    async fn get_user_by_username(&self, username: &str) -> Result<Option<User>>;

    /// Get user by email
    async fn get_user_by_email(&self, email: &str) -> Result<Option<User>>;

    /// Update user
    async fn update_user(&self, user: &User) -> Result<()>;

    /// Delete user
    async fn delete_user(&self, user_id: UserId) -> Result<()>;

    /// List users with pagination
    async fn list_users(&self, offset: u64, limit: u64) -> Result<Vec<User>>;

    /// Count total users
    async fn count_users(&self) -> Result<u64>;

    // Session operations
    /// Create a new session
    async fn create_session(&self, session: &Session) -> Result<SessionId>;

    /// Get session by ID
    async fn get_session_by_id(&self, session_id: SessionId) -> Result<Option<Session>>;

    /// Update session
    async fn update_session(&self, session: &Session) -> Result<()>;

    /// Delete session
    async fn delete_session(&self, session_id: SessionId) -> Result<()>;

    /// Delete all sessions for a user
    async fn delete_user_sessions(&self, user_id: UserId) -> Result<()>;

    /// List active sessions for a user
    async fn list_user_sessions(&self, user_id: UserId) -> Result<Vec<Session>>;

    /// Clean up expired sessions
    async fn cleanup_expired_sessions(&self) -> Result<u64>;

    // RBAC operations - Role management
    /// Create a new role
    async fn create_role(&self, request: CreateRoleRequest) -> Result<Role>;

    /// Get a role by its ID
    async fn get_role(&self, role_id: RoleId) -> Result<Option<Role>>;

    /// Get a role by its name
    async fn get_role_by_name(&self, name: &str) -> Result<Option<Role>>;

    /// Update an existing role
    async fn update_role(&self, role_id: RoleId, request: CreateRoleRequest) -> Result<Role>;

    /// Delete a role (also removes all assignments)
    async fn delete_role(&self, role_id: RoleId) -> Result<bool>;

    /// List roles with optional filtering
    async fn list_roles(&self, filter: RoleFilter) -> Result<Vec<Role>>;

    // RBAC operations - Permission management
    /// Create a new permission
    async fn create_permission(&self, request: CreatePermissionRequest) -> Result<Permission>;

    /// Get a permission by its ID
    async fn get_permission(&self, permission_id: PermissionId) -> Result<Option<Permission>>;

    /// Get a permission by resource and action
    async fn get_permission_by_key(
        &self,
        resource: &str,
        action: &str,
    ) -> Result<Option<Permission>>;

    /// Update an existing permission
    async fn update_permission(
        &self,
        permission_id: PermissionId,
        request: CreatePermissionRequest,
    ) -> Result<Permission>;

    /// Delete a permission (also removes all assignments)
    async fn delete_permission(&self, permission_id: PermissionId) -> Result<bool>;

    /// List permissions with optional filtering
    async fn list_permissions(&self, filter: PermissionFilter) -> Result<Vec<Permission>>;

    // RBAC operations - Role-Permission assignments
    /// Assign a permission to a role
    async fn assign_permission_to_role(
        &self,
        role_id: RoleId,
        permission_id: PermissionId,
        granted_by: Option<UserId>,
    ) -> Result<RolePermission>;

    /// Revoke a permission from a role
    async fn revoke_permission_from_role(
        &self,
        role_id: RoleId,
        permission_id: PermissionId,
    ) -> Result<bool>;

    /// Get all permissions assigned to a role
    async fn get_role_permissions(&self, role_id: RoleId) -> Result<Vec<Permission>>;

    // RBAC operations - User-Role assignments
    /// Assign a role to a user
    async fn assign_role_to_user(
        &self,
        user_id: UserId,
        role_id: RoleId,
        assigned_by: Option<UserId>,
    ) -> Result<UserRoleAssignment>;

    /// Revoke a role from a user
    async fn revoke_role_from_user(&self, user_id: UserId, role_id: RoleId) -> Result<bool>;

    /// Get all roles assigned to a user
    async fn get_user_roles(&self, user_id: UserId) -> Result<Vec<Role>>;

    /// Get all users that have a specific role
    async fn get_users_with_role(&self, role_id: RoleId) -> Result<Vec<UserId>>;

    // RBAC operations - Permission checking
    /// Check if a user has a specific permission with detailed result
    async fn check_permission(&self, check: PermissionCheck) -> Result<PermissionResult>;

    /// Simple boolean check if a user has a permission
    async fn has_permission(&self, user_id: UserId, resource: &str, action: &str) -> Result<bool>;

    /// Get all permissions available to a user (direct + inherited)
    async fn get_user_permissions(&self, user_id: UserId) -> Result<Vec<Permission>>;

    // RBAC operations - Role hierarchy
    /// Get the parent roles in the hierarchy for a given role
    async fn get_role_hierarchy(&self, role_id: RoleId) -> Result<Vec<Role>>;

    /// Get all effective permissions for a user (including inherited)
    async fn get_effective_permissions(&self, user_id: UserId) -> Result<Vec<Permission>>;

    // User attributes operations
    /// Set a user attribute (upsert operation)
    async fn set_attribute(
        &self,
        user_id: UserId,
        request: UserAttributeRequest,
    ) -> Result<UserAttribute>;

    /// Get a user attribute by key
    async fn get_attribute(&self, user_id: UserId, key: &str) -> Result<Option<UserAttribute>>;

    /// Get all attributes for a user
    async fn get_user_attributes(&self, user_id: UserId) -> Result<UserAttributes>;

    /// Delete a user attribute
    async fn delete_attribute(&self, user_id: UserId, key: &str) -> Result<bool>;

    /// Delete all attributes for a user
    async fn delete_user_attributes(&self, user_id: UserId) -> Result<u64>;

    /// Update an existing attribute
    async fn update_attribute(
        &self,
        user_id: UserId,
        key: &str,
        value: UserAttributeValue,
    ) -> Result<UserAttribute>;

    /// Search attributes by filter
    async fn search_attributes(&self, filter: UserAttributeFilter) -> Result<Vec<UserAttribute>>;

    /// Find users by attribute value
    async fn find_users_by_attribute(
        &self,
        key: &str,
        value: &UserAttributeValue,
    ) -> Result<Vec<UserId>>;

    /// Bulk set attributes for a user
    async fn set_attributes(
        &self,
        user_id: UserId,
        attributes: std::collections::HashMap<String, UserAttributeValue>,
    ) -> Result<Vec<UserAttribute>>;

    // Transaction support
    /// Begin a database transaction
    async fn begin_transaction(&self) -> Result<Box<dyn DatabaseTransaction>>;
}

/// Transaction trait for atomic operations
#[async_trait]
pub trait DatabaseTransaction: Send + std::fmt::Debug {
    /// Commit the transaction
    async fn commit(self: Box<Self>) -> Result<()>;

    /// Rollback the transaction
    async fn rollback(self: Box<Self>) -> Result<()>;

    /// Create user within transaction
    async fn create_user(&mut self, user: &User) -> Result<UserId>;

    /// Create session within transaction
    async fn create_session(&mut self, session: &Session) -> Result<SessionId>;

    /// Update user within transaction
    async fn update_user(&mut self, user: &User) -> Result<()>;
}

/// Database health information
#[derive(Debug, Clone)]
pub struct DatabaseHealth {
    pub is_healthy: bool,
    pub response_time: Duration,
    pub connection_count: Option<u32>,
    pub error_message: Option<String>,
}

/// Database statistics
#[derive(Debug, Clone)]
pub struct DatabaseStats {
    pub total_users: u64,
    pub active_sessions: u64,
    pub total_sessions: u64,
    pub database_size: Option<u64>,
    pub connection_pool_size: Option<u32>,
    pub active_connections: Option<u32>,
}

/// Database provider capabilities
#[derive(Debug, Clone)]
pub struct DatabaseCapabilities {
    pub provider_type: connection::DatabaseType,
    pub supports_transactions: bool,
    pub supports_rbac: bool,
    pub supports_json: bool,
    pub supports_full_text_search: bool,
    pub max_connections: Option<u32>,
    pub version: Option<String>,
}
