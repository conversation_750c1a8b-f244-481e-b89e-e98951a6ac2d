//! Integration tests for SQLite implementation

#[cfg(test)]
mod tests {
    use chrono::{DateTime, Utc};
    use serde_json::json;
    use uuid::Uuid;

    use crate::database::native::{
        common::{
            traits::{DatabaseConnection, DatabasePool, DatabaseValue},
            types::FromValue,
        },
        sqlite::{MigrationBuilder, SqliteConnectionWrapper, SqliteMigrationManager, SqlitePool},
    };

    async fn create_test_connection() -> SqliteConnectionWrapper {
        SqliteConnectionWrapper::in_memory().expect("Failed to create in-memory database")
    }

    async fn create_test_pool() -> SqlitePool {
        SqlitePool::in_memory(5).expect("Failed to create in-memory pool")
    }

    #[tokio::test]
    async fn test_basic_connection_operations() {
        let mut conn = create_test_connection().await;

        // Test ping with a proper non-query statement
        let result = conn
            .execute("CREATE TABLE IF NOT EXISTS ping_test (id INTEGER)", &[])
            .await
            .expect("<PERSON> failed");
        assert_eq!(result, 0); // CREATE TABLE returns 0 affected rows

        // Test execute
        let result = conn
            .execute(
                "CREATE TABLE test_users (id INTEGER PRIMARY KEY, name TEXT NOT NULL)",
                &[],
            )
            .await
            .expect("Create table failed");
        assert_eq!(result, 0); // CREATE TABLE returns 0 affected rows

        // Test insert
        let result = conn
            .execute(
                "INSERT INTO test_users (name) VALUES (?)",
                &[DatabaseValue::Text("Alice".to_string())],
            )
            .await
            .expect("Insert failed");
        assert_eq!(result, 1);

        // Test query_one
        let row = conn
            .query_one(
                "SELECT id, name FROM test_users WHERE name = ?",
                &[DatabaseValue::Text("Alice".to_string())],
            )
            .await
            .expect("Query one failed");

        let id: i64 = FromValue::from_value(row.get_by_name("id").unwrap()).unwrap();
        let name: String = FromValue::from_value(row.get_by_name("name").unwrap()).unwrap();

        assert_eq!(id, 1);
        assert_eq!(name, "Alice");

        // Test query_all
        conn.execute(
            "INSERT INTO test_users (name) VALUES (?)",
            &[DatabaseValue::Text("Bob".to_string())],
        )
        .await
        .expect("Insert failed");

        let rows = conn
            .query_all("SELECT name FROM test_users ORDER BY id", &[])
            .await
            .expect("Query all failed");

        assert_eq!(rows.len(), 2);
        let name1: String = FromValue::from_value(rows[0].get_by_name("name").unwrap()).unwrap();
        let name2: String = FromValue::from_value(rows[1].get_by_name("name").unwrap()).unwrap();
        assert_eq!(name1, "Alice");
        assert_eq!(name2, "Bob");
    }

    #[tokio::test]
    async fn test_transaction_operations() {
        let mut conn = create_test_connection().await;

        // Setup table
        conn.execute(
            "CREATE TABLE test_accounts (id INTEGER PRIMARY KEY, balance INTEGER NOT NULL)",
            &[],
        )
        .await
        .expect("Create table failed");

        conn.execute(
            "INSERT INTO test_accounts (balance) VALUES (?), (?)",
            &[DatabaseValue::I64(100), DatabaseValue::I64(50)],
        )
        .await
        .expect("Insert failed");

        // Test successful transaction
        {
            let mut tx = conn
                .begin_transaction()
                .await
                .expect("Begin transaction failed");

            tx.execute(
                "UPDATE test_accounts SET balance = balance - ? WHERE id = ?",
                &[DatabaseValue::I64(30), DatabaseValue::I64(1)],
            )
            .await
            .expect("Update failed");

            tx.execute(
                "UPDATE test_accounts SET balance = balance + ? WHERE id = ?",
                &[DatabaseValue::I64(30), DatabaseValue::I64(2)],
            )
            .await
            .expect("Update failed");

            tx.commit().await.expect("Commit failed");
        }

        // Verify changes
        let row = conn
            .query_one(
                "SELECT balance FROM test_accounts WHERE id = ?",
                &[DatabaseValue::I64(1)],
            )
            .await
            .expect("Query failed");
        let balance: i64 = FromValue::from_value(row.get_by_name("balance").unwrap()).unwrap();
        assert_eq!(balance, 70);

        // Test rollback transaction
        {
            let mut tx = conn
                .begin_transaction()
                .await
                .expect("Begin transaction failed");

            tx.execute(
                "UPDATE test_accounts SET balance = ? WHERE id = ?",
                &[DatabaseValue::I64(0), DatabaseValue::I64(1)],
            )
            .await
            .expect("Update failed");

            tx.rollback().await.expect("Rollback failed");
        }

        // Verify rollback
        let row = conn
            .query_one(
                "SELECT balance FROM test_accounts WHERE id = ?",
                &[DatabaseValue::I64(1)],
            )
            .await
            .expect("Query failed");
        let balance: i64 = FromValue::from_value(row.get_by_name("balance").unwrap()).unwrap();
        assert_eq!(balance, 70); // Should still be 70
    }

    #[tokio::test]
    async fn test_type_conversions() {
        let mut conn = create_test_connection().await;

        // Create table with various types
        conn.execute(
            r#"
            CREATE TABLE test_types (
                id INTEGER PRIMARY KEY,
                bool_val INTEGER,
                int_val INTEGER,
                float_val REAL,
                text_val TEXT,
                blob_val BLOB,
                uuid_val TEXT,
                datetime_val TEXT,
                json_val TEXT
            )
            "#,
            &[],
        )
        .await
        .expect("Create table failed");

        let test_uuid = Uuid::new_v4();
        let test_datetime = Utc::now();
        let test_json = json!({"key": "value", "number": 42});

        // Insert test data
        conn.execute(
            r#"
            INSERT INTO test_types (
                bool_val, int_val, float_val, text_val, blob_val, 
                uuid_val, datetime_val, json_val
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            &[
                DatabaseValue::Bool(true),
                DatabaseValue::I64(12345),
                DatabaseValue::F64(3.14159),
                DatabaseValue::Text("Hello, World!".to_string()),
                DatabaseValue::Bytes(vec![1, 2, 3, 4, 5]),
                DatabaseValue::Uuid(test_uuid),
                DatabaseValue::DateTime(test_datetime),
                DatabaseValue::Json(test_json.clone()),
            ],
        )
        .await
        .expect("Insert failed");

        // Query and verify data
        let row = conn
            .query_one("SELECT * FROM test_types WHERE id = 1", &[])
            .await
            .expect("Query failed");

        let bool_val: bool = FromValue::from_value(row.get_by_name("bool_val").unwrap()).unwrap();
        let int_val: i64 = FromValue::from_value(row.get_by_name("int_val").unwrap()).unwrap();
        let float_val: f64 = FromValue::from_value(row.get_by_name("float_val").unwrap()).unwrap();
        let text_val: String = FromValue::from_value(row.get_by_name("text_val").unwrap()).unwrap();
        let blob_val: Vec<u8> =
            FromValue::from_value(row.get_by_name("blob_val").unwrap()).unwrap();
        let uuid_val: Uuid = FromValue::from_value(row.get_by_name("uuid_val").unwrap()).unwrap();
        let datetime_val: DateTime<Utc> =
            FromValue::from_value(row.get_by_name("datetime_val").unwrap()).unwrap();
        let json_val: serde_json::Value =
            FromValue::from_value(row.get_by_name("json_val").unwrap()).unwrap();

        assert!(bool_val);
        assert_eq!(int_val, 12345);
        assert!((float_val - 3.14159).abs() < f64::EPSILON);
        assert_eq!(text_val, "Hello, World!");
        assert_eq!(blob_val, vec![1, 2, 3, 4, 5]);
        assert_eq!(uuid_val, test_uuid);
        assert_eq!(datetime_val.timestamp(), test_datetime.timestamp());
        assert_eq!(json_val, test_json);
    }

    #[tokio::test]
    async fn test_pool_operations() {
        let pool = create_test_pool().await;

        // Test basic pool info
        assert_eq!(pool.max_connections(), 5);
        // Note: active/idle connection counts may not be immediately accurate

        // Test getting connections
        let mut conn1 = pool
            .get_connection()
            .await
            .expect("Get connection 1 failed");
        let mut conn2 = pool
            .get_connection()
            .await
            .expect("Get connection 2 failed");

        // Test that connections work
        conn1
            .execute("CREATE TABLE IF NOT EXISTS ping_test1 (id INTEGER)", &[])
            .await
            .expect("Ping conn1 failed");
        conn2
            .execute("CREATE TABLE IF NOT EXISTS ping_test2 (id INTEGER)", &[])
            .await
            .expect("Ping conn2 failed");

        // Test health check
        pool.health_check().await.expect("Health check failed");
    }

    #[tokio::test]
    async fn test_migration_system() {
        let conn = create_test_connection().await;
        let mut migrator = SqliteMigrationManager::new(conn);

        // Initialize migration system
        migrator.initialize().await.expect("Initialize failed");

        // Create test migrations
        let migration1 = MigrationBuilder::new()
            .version(1)
            .name("create_users_table")
            .up_sql("CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT NOT NULL)")
            .down_sql("DROP TABLE users")
            .build()
            .expect("Build migration 1 failed");

        let migration2 = MigrationBuilder::new()
            .version(2)
            .name("add_email_to_users")
            .up_sql("ALTER TABLE users ADD COLUMN email TEXT")
            .down_sql("ALTER TABLE users DROP COLUMN email")
            .build()
            .expect("Build migration 2 failed");

        let migrations = vec![migration1.clone(), migration2.clone()];

        // Test migration application
        let applied = migrator.migrate(&migrations).await.expect("Migrate failed");
        assert_eq!(applied, vec![1, 2]);

        // Verify migrations were applied
        let applied_migrations = migrator
            .get_applied_migrations()
            .await
            .expect("Get applied failed");
        assert_eq!(applied_migrations.len(), 2);
        assert_eq!(applied_migrations[0].version, 1);
        assert_eq!(applied_migrations[1].version, 2);

        // Test current version
        let current = migrator
            .current_version()
            .await
            .expect("Current version failed");
        assert_eq!(current, Some(2));

        // Test rollback
        let rolled_back = migrator
            .rollback_to(1, &migrations)
            .await
            .expect("Rollback failed");
        assert_eq!(rolled_back, vec![2]);

        let current = migrator
            .current_version()
            .await
            .expect("Current version failed");
        assert_eq!(current, Some(1));

        // Test pending migrations
        let pending = migrator
            .get_pending_migrations(&migrations)
            .await
            .expect("Get pending failed");
        assert_eq!(pending.len(), 1);
        assert_eq!(pending[0].version, 2);
    }

    #[tokio::test]
    async fn test_error_handling() {
        let mut conn = create_test_connection().await;

        // Test query error
        let result = conn.execute("INVALID SQL", &[]).await;
        assert!(result.is_err());

        // Test not found
        conn.execute("CREATE TABLE test (id INTEGER PRIMARY KEY)", &[])
            .await
            .expect("Create table failed");

        let result = conn
            .query_one(
                "SELECT * FROM test WHERE id = ?",
                &[DatabaseValue::I64(999)],
            )
            .await;
        assert!(result.is_err());

        // Test unique constraint violation
        conn.execute(
            "CREATE TABLE unique_test (id INTEGER PRIMARY KEY, value TEXT UNIQUE)",
            &[],
        )
        .await
        .expect("Create table failed");

        conn.execute(
            "INSERT INTO unique_test (value) VALUES (?)",
            &[DatabaseValue::Text("test".to_string())],
        )
        .await
        .expect("First insert failed");

        let result = conn
            .execute(
                "INSERT INTO unique_test (value) VALUES (?)",
                &[DatabaseValue::Text("test".to_string())],
            )
            .await;
        assert!(result.is_err());
    }
}
