//! SQLite migration support

use std::collections::HashMap;

use chrono::{DateTime, Utc};

use super::connection::SqliteConnectionWrapper;
use crate::database::native::common::{errors::DatabaseError, traits::DatabaseConnection};

/// Represents a database migration
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct Migration {
    pub version: i64,
    pub name: String,
    pub up_sql: String,
    pub down_sql: Option<String>,
    pub checksum: String,
}

/// Migration manager for SQLite
pub struct SqliteMigrationManager {
    connection: SqliteConnectionWrapper,
    migrations_table: String,
}

impl SqliteMigrationManager {
    pub fn new(connection: SqliteConnectionWrapper) -> Self {
        Self {
            connection,
            migrations_table: "schema_migrations".to_string(),
        }
    }

    pub fn with_table_name(mut self, table_name: String) -> Self {
        self.migrations_table = table_name;
        self
    }

    /// Initialize the migrations table
    pub async fn initialize(&mut self) -> Result<(), DatabaseError> {
        let create_table_sql = format!(
            r#"
            CREATE TABLE IF NOT EXISTS {} (
                version INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                checksum TEXT NOT NULL,
                applied_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
            self.migrations_table
        );

        self.connection.execute(&create_table_sql, &[]).await?;
        Ok(())
    }

    /// Get all applied migrations
    pub async fn get_applied_migrations(&mut self) -> Result<Vec<AppliedMigration>, DatabaseError> {
        let query = format!(
            "SELECT version, name, checksum, applied_at FROM {} ORDER BY version",
            self.migrations_table
        );

        let rows = self.connection.query_all(&query, &[]).await?;
        let mut migrations = Vec::new();

        for row in rows {
            let version = match row.get_by_name("version")? {
                crate::database::native::common::traits::DatabaseValue::I64(v) => v,
                _ => {
                    return Err(DatabaseError::TypeConversionError(
                        "Invalid version type".to_string(),
                    ))
                }
            };

            let name = match row.get_by_name("name")? {
                crate::database::native::common::traits::DatabaseValue::Text(n) => n,
                _ => {
                    return Err(DatabaseError::TypeConversionError(
                        "Invalid name type".to_string(),
                    ))
                }
            };

            let checksum = match row.get_by_name("checksum")? {
                crate::database::native::common::traits::DatabaseValue::Text(c) => c,
                _ => {
                    return Err(DatabaseError::TypeConversionError(
                        "Invalid checksum type".to_string(),
                    ))
                }
            };

            let applied_at = match row.get_by_name("applied_at")? {
                crate::database::native::common::traits::DatabaseValue::DateTime(dt) => dt,
                crate::database::native::common::traits::DatabaseValue::Text(s) => {
                    // Try RFC3339 first, then fallback to SQLite's default format
                    if let Ok(parsed) = DateTime::parse_from_rfc3339(&s) {
                        parsed.with_timezone(&Utc)
                    } else {
                        // SQLite CURRENT_TIMESTAMP format: "YYYY-MM-DD HH:MM:SS"
                        chrono::NaiveDateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S")
                            .map_err(|e| {
                                DatabaseError::TypeConversionError(format!("Invalid datetime: {e}"))
                            })?
                            .and_utc()
                    }
                }
                _ => {
                    return Err(DatabaseError::TypeConversionError(
                        "Invalid applied_at type".to_string(),
                    ))
                }
            };

            migrations.push(AppliedMigration {
                version,
                name,
                checksum,
                applied_at,
            });
        }

        Ok(migrations)
    }

    /// Apply a migration
    pub async fn apply_migration(&mut self, migration: &Migration) -> Result<(), DatabaseError> {
        // Start transaction
        let mut tx = self.connection.begin_transaction().await?;

        // Execute the migration SQL
        tx.execute(&migration.up_sql, &[]).await?;

        // Record the migration
        let insert_sql = format!(
            "INSERT INTO {} (version, name, checksum) VALUES (?, ?, ?)",
            self.migrations_table
        );

        tx.execute(
            &insert_sql,
            &[
                crate::database::native::common::traits::DatabaseValue::I64(migration.version),
                crate::database::native::common::traits::DatabaseValue::Text(
                    migration.name.clone(),
                ),
                crate::database::native::common::traits::DatabaseValue::Text(
                    migration.checksum.clone(),
                ),
            ],
        )
        .await?;

        // Commit transaction
        tx.commit().await?;

        Ok(())
    }

    /// Rollback a migration
    pub async fn rollback_migration(&mut self, migration: &Migration) -> Result<(), DatabaseError> {
        let down_sql = migration.down_sql.as_ref().ok_or_else(|| {
            DatabaseError::MigrationError("No down migration provided".to_string())
        })?;

        // Start transaction
        let mut tx = self.connection.begin_transaction().await?;

        // Execute the rollback SQL
        tx.execute(down_sql, &[]).await?;

        // Remove the migration record
        let delete_sql = format!("DELETE FROM {} WHERE version = ?", self.migrations_table);

        tx.execute(
            &delete_sql,
            &[crate::database::native::common::traits::DatabaseValue::I64(
                migration.version,
            )],
        )
        .await?;

        // Commit transaction
        tx.commit().await?;

        Ok(())
    }

    /// Get pending migrations
    pub async fn get_pending_migrations(
        &mut self,
        available_migrations: &[Migration],
    ) -> Result<Vec<Migration>, DatabaseError> {
        let applied = self.get_applied_migrations().await?;
        let applied_versions: HashMap<i64, &AppliedMigration> =
            applied.iter().map(|m| (m.version, m)).collect();

        let mut pending = Vec::new();

        for migration in available_migrations {
            if let Some(applied_migration) = applied_versions.get(&migration.version) {
                // Check if checksum matches
                if applied_migration.checksum != migration.checksum {
                    return Err(DatabaseError::MigrationError(format!(
                        "Migration {} checksum mismatch",
                        migration.version
                    )));
                }
            } else {
                pending.push(migration.clone());
            }
        }

        // Sort by version
        pending.sort_by_key(|m| m.version);

        Ok(pending)
    }

    /// Run all pending migrations
    pub async fn migrate(&mut self, migrations: &[Migration]) -> Result<Vec<i64>, DatabaseError> {
        self.initialize().await?;

        let pending = self.get_pending_migrations(migrations).await?;
        let mut applied_versions = Vec::new();

        for migration in pending {
            self.apply_migration(&migration).await?;
            applied_versions.push(migration.version);
        }

        Ok(applied_versions)
    }

    /// Rollback to a specific version
    pub async fn rollback_to(
        &mut self,
        target_version: i64,
        available_migrations: &[Migration],
    ) -> Result<Vec<i64>, DatabaseError> {
        let applied = self.get_applied_migrations().await?;
        let migration_map: HashMap<i64, &Migration> = available_migrations
            .iter()
            .map(|m| (m.version, m))
            .collect();

        let mut rolled_back = Vec::new();

        // Rollback migrations in reverse order
        for applied_migration in applied.iter().rev() {
            if applied_migration.version <= target_version {
                break;
            }

            let migration = migration_map
                .get(&applied_migration.version)
                .ok_or_else(|| {
                    DatabaseError::MigrationError(format!(
                        "Migration {} not found in available migrations",
                        applied_migration.version
                    ))
                })?;

            self.rollback_migration(migration).await?;
            rolled_back.push(applied_migration.version);
        }

        Ok(rolled_back)
    }

    /// Get current schema version
    pub async fn current_version(&mut self) -> Result<Option<i64>, DatabaseError> {
        let applied = self.get_applied_migrations().await?;
        Ok(applied.last().map(|m| m.version))
    }
}

/// Represents an applied migration
#[derive(Debug, Clone)]
pub struct AppliedMigration {
    pub version: i64,
    pub name: String,
    pub checksum: String,
    pub applied_at: DateTime<Utc>,
}

/// Helper function to calculate migration checksum
pub fn calculate_checksum(content: &str) -> String {
    use std::{
        collections::hash_map::DefaultHasher,
        hash::{Hash, Hasher},
    };

    let mut hasher = DefaultHasher::new();
    content.hash(&mut hasher);
    format!("{:x}", hasher.finish())
}

/// Migration builder for easier migration creation
pub struct MigrationBuilder {
    version: Option<i64>,
    name: Option<String>,
    up_sql: Option<String>,
    down_sql: Option<String>,
}

impl MigrationBuilder {
    pub fn new() -> Self {
        Self {
            version: None,
            name: None,
            up_sql: None,
            down_sql: None,
        }
    }

    pub fn version(mut self, version: i64) -> Self {
        self.version = Some(version);
        self
    }

    pub fn name<S: Into<String>>(mut self, name: S) -> Self {
        self.name = Some(name.into());
        self
    }

    pub fn up_sql<S: Into<String>>(mut self, sql: S) -> Self {
        self.up_sql = Some(sql.into());
        self
    }

    pub fn down_sql<S: Into<String>>(mut self, sql: S) -> Self {
        self.down_sql = Some(sql.into());
        self
    }

    pub fn build(self) -> Result<Migration, DatabaseError> {
        let version = self.version.ok_or_else(|| {
            DatabaseError::MigrationError("Migration version is required".to_string())
        })?;
        let name = self.name.ok_or_else(|| {
            DatabaseError::MigrationError("Migration name is required".to_string())
        })?;
        let up_sql = self.up_sql.ok_or_else(|| {
            DatabaseError::MigrationError("Migration up_sql is required".to_string())
        })?;

        let checksum = calculate_checksum(&up_sql);

        Ok(Migration {
            version,
            name,
            up_sql,
            down_sql: self.down_sql,
            checksum,
        })
    }
}

impl Default for MigrationBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_migration_builder() {
        let migration = MigrationBuilder::new()
            .version(1)
            .name("create_users_table")
            .up_sql("CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT NOT NULL)")
            .down_sql("DROP TABLE users")
            .build()
            .unwrap();

        assert_eq!(migration.version, 1);
        assert_eq!(migration.name, "create_users_table");
        assert!(!migration.checksum.is_empty());
        assert!(migration.down_sql.is_some());
    }

    #[test]
    fn test_checksum_calculation() {
        let sql = "CREATE TABLE test (id INTEGER)";
        let checksum1 = calculate_checksum(sql);
        let checksum2 = calculate_checksum(sql);

        assert_eq!(checksum1, checksum2);

        let different_sql = "CREATE TABLE test (id TEXT)";
        let checksum3 = calculate_checksum(different_sql);

        assert_ne!(checksum1, checksum3);
    }
}
