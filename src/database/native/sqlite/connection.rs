//! SQLite connection implementation

use std::sync::{Arc, Mutex};

use async_trait::async_trait;
use r2d2::PooledConnection;
use r2d2_sqlite::SqliteConnectionManager;
use rusqlite::{params_from_iter, Connection};

use super::{row::SqliteRowWrapper, transaction::SqliteTransactionWrapper};
use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseConnection, DatabaseRow, DatabaseTransaction, DatabaseValue},
};

pub enum SqliteConnectionWrapper {
    Owned(Arc<Mutex<Connection>>),
    Pooled(Option<Arc<Mutex<r2d2::PooledConnection<SqliteConnectionManager>>>>), // Option allows ownership transfer, Arc<Mutex<>> for thread safety
}

impl SqliteConnectionWrapper {
    pub fn new(connection: Connection) -> Self {
        Self::Owned(Arc::new(Mutex::new(connection)))
    }

    pub fn from_pooled(connection: PooledConnection<SqliteConnectionManager>) -> Self {
        Self::Pooled(Some(Arc::new(Mutex::new(connection))))
    }

    // Helper method to execute with either connection type
    async fn with_connection<F, R>(&self, f: F) -> Result<R, DatabaseError>
    where
        F: FnOnce(&Connection) -> Result<R, rusqlite::Error> + Send + 'static,
        R: Send + 'static,
    {
        match self {
            SqliteConnectionWrapper::Owned(connection) => {
                let connection = Arc::clone(connection);
                tokio::task::spawn_blocking(move || {
                    let conn = connection.lock().map_err(|e| {
                        DatabaseError::ConnectionError(format!("Failed to lock connection: {e}"))
                    })?;
                    f(&conn).map_err(|e| DatabaseError::QueryError(format!("SQLite error: {e}")))
                })
                .await
                .map_err(|e| DatabaseError::ConnectionError(format!("Task join error: {e}")))?
            }
            SqliteConnectionWrapper::Pooled(pooled_conn_opt) => {
                let pooled_conn = pooled_conn_opt.as_ref().ok_or_else(|| {
                    DatabaseError::ConnectionError(
                        "Connection is in use by a transaction".to_string(),
                    )
                })?;
                let pooled_conn = Arc::clone(pooled_conn);

                tokio::task::spawn_blocking(move || {
                    let conn = pooled_conn.lock().map_err(|e| {
                        DatabaseError::ConnectionError(format!("Failed to lock connection: {e}"))
                    })?;
                    f(&conn).map_err(|e| DatabaseError::QueryError(format!("SQLite error: {e}")))
                })
                .await
                .map_err(|e| DatabaseError::ConnectionError(format!("Task join error: {e}")))?
            }
        }
    }

    pub fn from_path<P: AsRef<std::path::Path>>(path: P) -> Result<Self, DatabaseError> {
        let connection = Connection::open(path).map_err(|e| {
            DatabaseError::ConnectionError(format!("Failed to open SQLite database: {e}"))
        })?;

        // Enable foreign key constraints
        connection
            .execute("PRAGMA foreign_keys = ON", [])
            .map_err(|e| {
                DatabaseError::ConnectionError(format!("Failed to enable foreign keys: {e}"))
            })?;

        Ok(Self::new(connection))
    }

    pub fn in_memory() -> Result<Self, DatabaseError> {
        let connection = Connection::open_in_memory().map_err(|e| {
            DatabaseError::ConnectionError(format!(
                "Failed to create in-memory SQLite database: {e}"
            ))
        })?;

        // Enable foreign key constraints
        connection
            .execute("PRAGMA foreign_keys = ON", [])
            .map_err(|e| {
                DatabaseError::ConnectionError(format!("Failed to enable foreign keys: {e}"))
            })?;

        Ok(Self::new(connection))
    }

    fn convert_params(
        params: &[DatabaseValue],
    ) -> Result<Vec<rusqlite::types::Value>, DatabaseError> {
        params
            .iter()
            .map(|param| convert_database_value_to_sqlite(param.clone()))
            .collect()
    }
}

#[async_trait]
impl DatabaseConnection for SqliteConnectionWrapper {
    async fn execute(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError> {
        let sqlite_params = Self::convert_params(params)?;
        let query = query.to_string();

        let result = self
            .with_connection(move |conn| {
                conn.execute(&query, params_from_iter(sqlite_params.iter()))
            })
            .await?;

        Ok(result as u64)
    }

    async fn query_one(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError> {
        let sqlite_params = Self::convert_params(params)?;
        let query = query.to_string();

        let result = self
            .with_connection(move |conn| {
                let mut stmt = conn.prepare(&query)?;
                let mut rows = stmt.query(params_from_iter(sqlite_params.iter()))?;

                if let Some(row) = rows.next()? {
                    SqliteRowWrapper::new(row).map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            0,
                            "conversion error".to_string(),
                            rusqlite::types::Type::Null,
                        )
                    })
                } else {
                    Err(rusqlite::Error::QueryReturnedNoRows)
                }
            })
            .await?;

        Ok(Box::new(result))
    }

    async fn query_all(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError> {
        let sqlite_params = Self::convert_params(params)?;
        let query = query.to_string();

        let results = self
            .with_connection(move |conn| {
                let mut stmt = conn.prepare(&query)?;
                let rows = stmt.query_map(params_from_iter(sqlite_params.iter()), |row| {
                    SqliteRowWrapper::new(row).map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            0,
                            "conversion error".to_string(),
                            rusqlite::types::Type::Null,
                        )
                    })
                })?;

                let mut results = Vec::new();
                for row_result in rows {
                    let row = row_result?;
                    results.push(row);
                }
                Ok::<Vec<SqliteRowWrapper>, rusqlite::Error>(results)
            })
            .await?;

        Ok(results
            .into_iter()
            .map(|r| Box::new(r) as Box<dyn DatabaseRow>)
            .collect())
    }

    async fn begin_transaction(&mut self) -> Result<Box<dyn DatabaseTransaction>, DatabaseError> {
        match self {
            SqliteConnectionWrapper::Owned(conn) => {
                let transaction = SqliteTransactionWrapper::new(Arc::clone(conn))?;
                Ok(Box::new(transaction))
            }
            SqliteConnectionWrapper::Pooled(pooled_conn_opt) => {
                // Take ownership of the pooled connection
                let pooled_conn = pooled_conn_opt.take().ok_or_else(|| {
                    DatabaseError::TransactionError(
                        "Connection is already in a transaction".to_string(),
                    )
                })?;

                let transaction = SqliteTransactionWrapper::new_pooled(pooled_conn)?;
                Ok(Box::new(transaction))
            }
        }
    }

    async fn ping(&mut self) -> Result<(), DatabaseError> {
        self.with_connection(|conn| {
            // Use a simple query that doesn't return results
            conn.execute("CREATE TABLE IF NOT EXISTS _ping_test (id INTEGER); DROP TABLE IF EXISTS _ping_test", [])?;
            Ok(())
        }).await
    }

    fn is_async_native(&self) -> bool {
        false // SQLite operations are sync-wrapped
    }
}

impl Clone for SqliteConnectionWrapper {
    fn clone(&self) -> Self {
        match self {
            SqliteConnectionWrapper::Owned(conn) => {
                SqliteConnectionWrapper::Owned(Arc::clone(conn))
            }
            SqliteConnectionWrapper::Pooled(_pooled_opt) => {
                // Since we can't clone the pooled connection, we return a None variant
                // This is intentional - pooled connections should not be cloned during active transactions
                SqliteConnectionWrapper::Pooled(None)
            }
        }
    }
}

// Helper function to convert DatabaseValue to SQLite value
fn convert_database_value_to_sqlite(
    value: DatabaseValue,
) -> Result<rusqlite::types::Value, DatabaseError> {
    match value {
        DatabaseValue::Null => Ok(rusqlite::types::Value::Null),
        DatabaseValue::Bool(b) => Ok(rusqlite::types::Value::Integer(if b { 1 } else { 0 })),
        DatabaseValue::I32(i) => Ok(rusqlite::types::Value::Integer(i as i64)),
        DatabaseValue::I64(i) => Ok(rusqlite::types::Value::Integer(i)),
        DatabaseValue::F64(f) => Ok(rusqlite::types::Value::Real(f)),
        DatabaseValue::Text(s) => Ok(rusqlite::types::Value::Text(s)),
        DatabaseValue::Bytes(b) => Ok(rusqlite::types::Value::Blob(b)),
        DatabaseValue::Uuid(u) => Ok(rusqlite::types::Value::Text(u.to_string())),
        DatabaseValue::DateTime(dt) => Ok(rusqlite::types::Value::Text(dt.to_rfc3339())),
        DatabaseValue::Json(j) => {
            let json_str = serde_json::to_string(&j).map_err(|e| {
                DatabaseError::TypeConversionError(format!("JSON serialization error: {e}"))
            })?;
            Ok(rusqlite::types::Value::Text(json_str))
        }
        DatabaseValue::BytesZeroCopy(b) => Ok(rusqlite::types::Value::Blob(b.to_vec())),
        DatabaseValue::JsonZeroCopy(b) => {
            let s = String::from_utf8(b.to_vec()).unwrap_or_default();
            Ok(rusqlite::types::Value::Text(s))
        }
    }
}

// Additional SQLite-specific methods
impl SqliteConnectionWrapper {
    /// Execute a batch of SQL statements (useful for migrations)
    pub async fn execute_batch(&mut self, sql: &str) -> Result<(), DatabaseError> {
        let sql = sql.to_string();

        self.with_connection(move |conn| conn.execute_batch(&sql))
            .await
    }

    /// Get the last inserted row ID
    pub async fn last_insert_rowid(&self) -> Result<i64, DatabaseError> {
        self.with_connection(|conn| Ok(conn.last_insert_rowid()))
            .await
    }

    /// Get the number of rows affected by the last statement
    pub async fn changes(&self) -> Result<u64, DatabaseError> {
        self.with_connection(|conn| Ok(conn.changes())).await
    }

    /// Set SQLite pragma values
    pub async fn set_pragma(&mut self, pragma: &str, value: &str) -> Result<(), DatabaseError> {
        // Validate pragma name to prevent injection
        if !pragma.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(DatabaseError::QueryError("Invalid pragma name".to_string()));
        }
        let query = format!("PRAGMA {pragma} = {value}");
        self.execute(&query, &[]).await?;
        Ok(())
    }

    /// Get SQLite pragma values
    pub async fn get_pragma(&mut self, pragma: &str) -> Result<String, DatabaseError> {
        // Validate pragma name to prevent injection
        if !pragma.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(DatabaseError::QueryError("Invalid pragma name".to_string()));
        }
        let query = format!("PRAGMA {pragma}");
        let row = self.query_one(&query, &[]).await?;
        let value = row.get_by_index(0)?;

        match value {
            DatabaseValue::Text(s) => Ok(s),
            DatabaseValue::I64(i) => Ok(i.to_string()),
            DatabaseValue::F64(f) => Ok(f.to_string()),
            _ => Err(DatabaseError::TypeConversionError(
                "Unexpected pragma value type".to_string(),
            )),
        }
    }
}
