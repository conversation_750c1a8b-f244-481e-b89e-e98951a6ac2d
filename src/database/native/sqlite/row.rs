//! SQLite row implementation for DatabaseRow trait

use std::{borrow::Cow, collections::HashMap, sync::Arc};

use chrono::{DateTime, Utc};
use rusqlite::{types::Value as SqliteValue, Row as SqliteRow};
use serde_json::Value as JsonValue;
use uuid::Uuid;

use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseRow, DatabaseValue},
};

pub struct SqliteRowWrapper {
    values: Vec<DatabaseValue>,
    column_names: Arc<Vec<String>>, // Share column names to reduce allocations
    column_map: HashMap<String, usize>,
}

impl SqliteRowWrapper {
    pub fn new(row: &SqliteRow) -> Result<Self, DatabaseError> {
        let column_count = row.as_ref().column_count();
        let mut column_names = Vec::with_capacity(column_count);
        let mut column_map = HashMap::new();
        let mut values = Vec::with_capacity(column_count);

        // Get column names and build the map
        for i in 0..column_count {
            let name = row
                .as_ref()
                .column_name(i)
                .map_err(|_| DatabaseError::QueryError(format!("Invalid column index: {i}")))?
                .to_string();
            column_map.insert(name.clone(), i);
            column_names.push(name);
        }

        // Extract all values
        for i in 0..column_count {
            let sqlite_value: SqliteValue = row
                .get(i)
                .map_err(|e| DatabaseError::QueryError(format!("Failed to get column {i}: {e}")))?;

            let db_value = convert_sqlite_value_to_database_value(sqlite_value)?;
            values.push(db_value);
        }

        Ok(SqliteRowWrapper {
            values,
            column_names: Arc::new(column_names),
            column_map,
        })
    }
}

impl DatabaseRow for SqliteRowWrapper {
    fn get_by_index(&self, index: usize) -> Result<DatabaseValue, DatabaseError> {
        self.values
            .get(index)
            .cloned()
            .ok_or_else(|| DatabaseError::QueryError(format!("Column index {index} out of bounds")))
    }

    fn get_by_name(&self, name: &str) -> Result<DatabaseValue, DatabaseError> {
        let index = self
            .column_map
            .get(name)
            .ok_or_else(|| DatabaseError::QueryError(format!("Column '{name}' not found")))?;
        self.get_by_index(*index)
    }

    fn column_count(&self) -> usize {
        self.column_names.len()
    }

    fn column_names(&self) -> Cow<'_, [String]> {
        Cow::Borrowed(&self.column_names)
    }
}

impl std::fmt::Debug for SqliteRowWrapper {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("SqliteRowWrapper")
            .field("column_names", &self.column_names)
            .field("values", &self.values)
            .finish()
    }
}

fn convert_sqlite_value_to_database_value(
    value: SqliteValue,
) -> Result<DatabaseValue, DatabaseError> {
    match value {
        SqliteValue::Null => Ok(DatabaseValue::Null),
        SqliteValue::Integer(i) => {
            // SQLite integers are always i64
            Ok(DatabaseValue::I64(i))
        }
        SqliteValue::Real(f) => Ok(DatabaseValue::F64(f)),
        SqliteValue::Text(s) => {
            // Try to parse as UUID first, then DateTime, then JSON, finally keep as text
            if let Ok(uuid) = Uuid::parse_str(&s) {
                Ok(DatabaseValue::Uuid(uuid))
            } else if let Ok(dt) = DateTime::parse_from_rfc3339(&s) {
                Ok(DatabaseValue::DateTime(dt.with_timezone(&Utc)))
            } else if s.starts_with('{') || s.starts_with('[') {
                // Try to parse as JSON
                match serde_json::from_str::<JsonValue>(&s) {
                    Ok(json) => Ok(DatabaseValue::Json(json)),
                    Err(_) => Ok(DatabaseValue::Text(s)),
                }
            } else {
                Ok(DatabaseValue::Text(s))
            }
        }
        SqliteValue::Blob(b) => {
            // Keep blob data as bytes - don't try to convert to UUID automatically
            // UUID conversion should be explicit when needed
            Ok(DatabaseValue::Bytes(b))
        }
    }
}
