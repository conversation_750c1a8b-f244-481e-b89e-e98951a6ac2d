//! SQLite connection pool implementation using r2d2

use std::time::Duration;

use async_trait::async_trait;
use r2d2::Pool;
use r2d2_sqlite::SqliteConnectionManager;

use super::connection::SqliteConnectionWrapper;
use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseConnection, DatabasePool},
};

pub struct SqlitePool {
    pool: Pool<SqliteConnectionManager>,
}

impl SqlitePool {
    /// Create a new SQLite connection pool from a database path
    pub fn new<P: AsRef<std::path::Path>>(
        database_path: P,
        max_connections: u32,
    ) -> Result<Self, DatabaseError> {
        let manager = SqliteConnectionManager::file(database_path);
        let pool = Pool::builder()
            .max_size(max_connections)
            .connection_timeout(Duration::from_secs(30))
            .build(manager)
            .map_err(|e| DatabaseError::PoolError(format!("Failed to create SQLite pool: {e}")))?;

        Ok(Self { pool })
    }

    /// Create a new in-memory SQLite connection pool
    /// Note: All connections will share the same in-memory database
    pub fn in_memory(max_connections: u32) -> Result<Self, DatabaseError> {
        let manager = SqliteConnectionManager::memory();
        let pool = Pool::builder()
            .max_size(max_connections)
            .connection_timeout(Duration::from_secs(30))
            .build(manager)
            .map_err(|e| {
                DatabaseError::PoolError(format!("Failed to create in-memory SQLite pool: {e}"))
            })?;

        Ok(Self { pool })
    }

    /// Create a new SQLite connection pool with custom configuration
    pub fn with_config<P: AsRef<std::path::Path>>(
        database_path: P,
        config: SqlitePoolConfig,
    ) -> Result<Self, DatabaseError> {
        let manager = SqliteConnectionManager::file(database_path);
        let pool = Pool::builder()
            .max_size(config.max_connections)
            .min_idle(config.min_idle)
            .connection_timeout(config.connection_timeout)
            .idle_timeout(config.idle_timeout)
            .max_lifetime(config.max_lifetime)
            .build(manager)
            .map_err(|e| DatabaseError::PoolError(format!("Failed to create SQLite pool: {e}")))?;

        Ok(Self { pool })
    }

    /// Get the underlying r2d2 pool (for advanced usage)
    pub fn inner(&self) -> &Pool<SqliteConnectionManager> {
        &self.pool
    }
}

#[async_trait]
impl DatabasePool for SqlitePool {
    type Connection = SqliteConnectionWrapper;

    async fn get_connection(&self) -> Result<Self::Connection, DatabaseError> {
        // Get connection from pool in blocking task
        let pool = self.pool.clone();
        let pooled_conn = tokio::task::spawn_blocking(move || pool.get())
            .await
            .map_err(|e| DatabaseError::PoolError(format!("Task join error: {e}")))?
            .map_err(|e| {
                DatabaseError::PoolError(format!("Failed to get connection from pool: {e}"))
            })?;

        // Configure the connection
        pooled_conn
            .execute("PRAGMA foreign_keys = ON", [])
            .map_err(|e| {
                DatabaseError::ConnectionError(format!("Failed to enable foreign keys: {e}"))
            })?;

        // Create a wrapper that holds the pooled connection
        Ok(SqliteConnectionWrapper::from_pooled(pooled_conn))
    }

    async fn close(&self) -> Result<(), DatabaseError> {
        // r2d2 doesn't have an explicit close method, connections are closed when dropped
        // We could implement a custom close by storing a flag and rejecting new connections
        Ok(())
    }

    fn max_connections(&self) -> u32 {
        self.pool.max_size()
    }

    fn active_connections(&self) -> u32 {
        self.pool.state().connections - self.pool.state().idle_connections
    }

    fn idle_connections(&self) -> u32 {
        self.pool.state().idle_connections
    }

    async fn health_check(&self) -> Result<(), DatabaseError> {
        let mut conn = self.get_connection().await?;
        conn.ping().await
    }
}

impl Clone for SqlitePool {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
        }
    }
}

/// Configuration for SQLite connection pool
#[derive(Debug, Clone)]
pub struct SqlitePoolConfig {
    /// Maximum number of connections in the pool
    pub max_connections: u32,
    /// Minimum number of idle connections to maintain
    pub min_idle: Option<u32>,
    /// Timeout when getting a connection from the pool
    pub connection_timeout: Duration,
    /// How long a connection can be idle before being closed
    pub idle_timeout: Option<Duration>,
    /// Maximum lifetime of a connection
    pub max_lifetime: Option<Duration>,
}

impl Default for SqlitePoolConfig {
    fn default() -> Self {
        Self {
            max_connections: 10,
            min_idle: None,
            connection_timeout: Duration::from_secs(30),
            idle_timeout: Some(Duration::from_secs(600)), // 10 minutes
            max_lifetime: Some(Duration::from_secs(1800)), // 30 minutes
        }
    }
}

impl SqlitePoolConfig {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn max_connections(mut self, max: u32) -> Self {
        self.max_connections = max;
        self
    }

    pub fn min_idle(mut self, min: u32) -> Self {
        self.min_idle = Some(min);
        self
    }

    pub fn connection_timeout(mut self, timeout: Duration) -> Self {
        self.connection_timeout = timeout;
        self
    }

    pub fn idle_timeout(mut self, timeout: Duration) -> Self {
        self.idle_timeout = Some(timeout);
        self
    }

    pub fn max_lifetime(mut self, lifetime: Duration) -> Self {
        self.max_lifetime = Some(lifetime);
        self
    }
}

// We need to update the connection wrapper to handle pooled connections
