//! SQLite transaction implementation

use std::sync::{Arc, Mutex};

use async_trait::async_trait;
use r2d2_sqlite::SqliteConnectionManager;
use rusqlite::{params_from_iter, Connection};

use super::row::SqliteRowWrapper;
use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseRow, DatabaseTransaction, DatabaseValue},
};

pub enum SqliteTransactionType {
    // For owned connections (existing)
    Owned(Arc<Mutex<Connection>>),
    // For pooled connections (NEW) - takes full ownership with thread safety
    Pooled(Arc<Mutex<r2d2::PooledConnection<SqliteConnectionManager>>>),
}

// Since SQLite transactions are not Send/Sync, we'll use a different approach
// We'll wrap the connection and handle transactions through it
pub struct SqliteTransactionWrapper {
    connection: Option<SqliteTransactionType>,
    is_committed: bool,
    is_rolled_back: bool,
}

impl SqliteTransactionWrapper {
    pub fn new(connection: Arc<Mutex<Connection>>) -> Result<Self, DatabaseError> {
        // Start transaction on owned connection
        {
            let conn = connection.lock().map_err(|e| {
                DatabaseError::TransactionError(format!("Failed to lock connection: {e}"))
            })?;
            conn.execute("BEGIN TRANSACTION", []).map_err(|e| {
                DatabaseError::TransactionError(format!("Failed to begin transaction: {e}"))
            })?;
        }

        Ok(Self {
            connection: Some(SqliteTransactionType::Owned(connection)),
            is_committed: false,
            is_rolled_back: false,
        })
    }

    // NEW constructor for pooled connections - takes full ownership with thread safety
    pub fn new_pooled(
        pooled_conn: Arc<Mutex<r2d2::PooledConnection<SqliteConnectionManager>>>,
    ) -> Result<Self, DatabaseError> {
        // Start transaction on pooled connection
        {
            let conn = pooled_conn.lock().map_err(|e| {
                DatabaseError::TransactionError(format!("Failed to lock connection: {e}"))
            })?;
            conn.execute("BEGIN TRANSACTION", []).map_err(|e| {
                DatabaseError::TransactionError(format!("Failed to begin pooled transaction: {e}"))
            })?;
        }

        Ok(Self {
            connection: Some(SqliteTransactionType::Pooled(pooled_conn)),
            is_committed: false,
            is_rolled_back: false,
        })
    }

    fn convert_params(
        params: &[DatabaseValue],
    ) -> Result<Vec<rusqlite::types::Value>, DatabaseError> {
        params
            .iter()
            .map(|param| convert_database_value_to_sqlite(param.clone()))
            .collect()
    }
}

#[async_trait]
impl DatabaseTransaction for SqliteTransactionWrapper {
    async fn execute(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let sqlite_params = Self::convert_params(params)?;
        let query = query.to_string();

        let result = match &self.connection {
            Some(SqliteTransactionType::Owned(connection)) => {
                let connection = Arc::clone(connection);
                // Execute in blocking task since rusqlite is synchronous
                tokio::task::spawn_blocking(move || {
                    let conn = connection.lock().map_err(|e| {
                        DatabaseError::TransactionError(format!("Failed to lock connection: {e}"))
                    })?;
                    conn.execute(&query, params_from_iter(sqlite_params.iter()))
                        .map_err(|e| {
                            DatabaseError::QueryError(format!("SQLite execute error: {e}"))
                        })
                })
                .await
                .map_err(|e| DatabaseError::TransactionError(format!("Task join error: {e}")))??
            }
            Some(SqliteTransactionType::Pooled(connection)) => {
                let connection = Arc::clone(connection);
                // Execute in blocking task since rusqlite is synchronous
                tokio::task::spawn_blocking(move || {
                    let conn = connection.lock().map_err(|e| {
                        DatabaseError::TransactionError(format!("Failed to lock connection: {e}"))
                    })?;
                    conn.execute(&query, params_from_iter(sqlite_params.iter()))
                        .map_err(|e| {
                            DatabaseError::QueryError(format!("SQLite execute error: {e}"))
                        })
                })
                .await
                .map_err(|e| DatabaseError::TransactionError(format!("Task join error: {e}")))??
            }
            None => {
                return Err(DatabaseError::TransactionError(
                    "Transaction already completed".to_string(),
                ))
            }
        };

        Ok(result as u64)
    }

    async fn query_one(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let sqlite_params = Self::convert_params(params)?;
        let query = query.to_string();

        let result = match &self.connection {
            Some(SqliteTransactionType::Owned(connection)) => {
                let connection = Arc::clone(connection);
                // Execute in blocking task since rusqlite is synchronous
                tokio::task::spawn_blocking(move || {
                    let conn = connection.lock().map_err(|e| {
                        DatabaseError::TransactionError(format!("Failed to lock connection: {e}"))
                    })?;
                    let mut stmt = conn.prepare(&query).map_err(|e| {
                        DatabaseError::QueryError(format!("SQLite prepare error: {e}"))
                    })?;
                    let mut rows =
                        stmt.query(params_from_iter(sqlite_params.iter()))
                            .map_err(|e| {
                                DatabaseError::QueryError(format!("SQLite query error: {e}"))
                            })?;

                    if let Some(row) = rows
                        .next()
                        .map_err(|e| DatabaseError::QueryError(format!("SQLite row error: {e}")))?
                    {
                        SqliteRowWrapper::new(row)
                    } else {
                        Err(DatabaseError::NotFound)
                    }
                })
                .await
                .map_err(|e| DatabaseError::TransactionError(format!("Task join error: {e}")))??
            }
            Some(SqliteTransactionType::Pooled(connection)) => {
                let connection = Arc::clone(connection);
                // Execute in blocking task since rusqlite is synchronous
                tokio::task::spawn_blocking(move || {
                    let conn = connection.lock().map_err(|e| {
                        DatabaseError::TransactionError(format!("Failed to lock connection: {e}"))
                    })?;
                    let mut stmt = conn.prepare(&query).map_err(|e| {
                        DatabaseError::QueryError(format!("SQLite prepare error: {e}"))
                    })?;
                    let mut rows =
                        stmt.query(params_from_iter(sqlite_params.iter()))
                            .map_err(|e| {
                                DatabaseError::QueryError(format!("SQLite query error: {e}"))
                            })?;

                    if let Some(row) = rows
                        .next()
                        .map_err(|e| DatabaseError::QueryError(format!("SQLite row error: {e}")))?
                    {
                        SqliteRowWrapper::new(row)
                    } else {
                        Err(DatabaseError::NotFound)
                    }
                })
                .await
                .map_err(|e| DatabaseError::TransactionError(format!("Task join error: {e}")))??
            }
            None => {
                return Err(DatabaseError::TransactionError(
                    "Transaction already completed".to_string(),
                ))
            }
        };

        Ok(Box::new(result))
    }

    async fn query_all(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let sqlite_params = Self::convert_params(params)?;
        let query = query.to_string();

        let results = match &self.connection {
            Some(SqliteTransactionType::Owned(connection)) => {
                let connection = Arc::clone(connection);
                // Execute in blocking task since rusqlite is synchronous
                tokio::task::spawn_blocking(move || {
                    let conn = connection.lock().map_err(|e| {
                        DatabaseError::TransactionError(format!("Failed to lock connection: {e}"))
                    })?;
                    let mut stmt = conn.prepare(&query).map_err(|e| {
                        DatabaseError::QueryError(format!("SQLite prepare error: {e}"))
                    })?;
                    let rows = stmt
                        .query_map(params_from_iter(sqlite_params.iter()), |row| {
                            SqliteRowWrapper::new(row).map_err(|_| {
                                rusqlite::Error::InvalidColumnType(
                                    0,
                                    "conversion error".to_string(),
                                    rusqlite::types::Type::Null,
                                )
                            })
                        })
                        .map_err(|e| {
                            DatabaseError::QueryError(format!("SQLite query error: {e}"))
                        })?;

                    let mut results = Vec::new();
                    for row_result in rows {
                        let row = row_result.map_err(|e| {
                            DatabaseError::QueryError(format!("SQLite row error: {e}"))
                        })?;
                        results.push(row);
                    }
                    Ok::<Vec<SqliteRowWrapper>, DatabaseError>(results)
                })
                .await
                .map_err(|e| DatabaseError::TransactionError(format!("Task join error: {e}")))??
            }
            Some(SqliteTransactionType::Pooled(connection)) => {
                let connection = Arc::clone(connection);
                // Execute in blocking task since rusqlite is synchronous
                tokio::task::spawn_blocking(move || {
                    let conn = connection.lock().map_err(|e| {
                        DatabaseError::TransactionError(format!("Failed to lock connection: {e}"))
                    })?;
                    let mut stmt = conn.prepare(&query).map_err(|e| {
                        DatabaseError::QueryError(format!("SQLite prepare error: {e}"))
                    })?;
                    let rows = stmt
                        .query_map(params_from_iter(sqlite_params.iter()), |row| {
                            SqliteRowWrapper::new(row).map_err(|_| {
                                rusqlite::Error::InvalidColumnType(
                                    0,
                                    "conversion error".to_string(),
                                    rusqlite::types::Type::Null,
                                )
                            })
                        })
                        .map_err(|e| {
                            DatabaseError::QueryError(format!("SQLite query error: {e}"))
                        })?;

                    let mut results = Vec::new();
                    for row_result in rows {
                        let row = row_result.map_err(|e| {
                            DatabaseError::QueryError(format!("SQLite row error: {e}"))
                        })?;
                        results.push(row);
                    }
                    Ok::<Vec<SqliteRowWrapper>, DatabaseError>(results)
                })
                .await
                .map_err(|e| DatabaseError::TransactionError(format!("Task join error: {e}")))??
            }
            None => {
                return Err(DatabaseError::TransactionError(
                    "Transaction already completed".to_string(),
                ))
            }
        };

        Ok(results
            .into_iter()
            .map(|r| Box::new(r) as Box<dyn DatabaseRow>)
            .collect())
    }

    async fn commit(mut self: Box<Self>) -> Result<(), DatabaseError> {
        if self.is_committed {
            return Err(DatabaseError::TransactionError(
                "Transaction already committed".to_string(),
            ));
        }
        if self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already rolled back".to_string(),
            ));
        }

        if let Some(connection) = self.connection.take() {
            match connection {
                SqliteTransactionType::Owned(connection) => {
                    tokio::task::spawn_blocking(move || {
                        let conn = connection.lock().map_err(|e| {
                            DatabaseError::TransactionError(format!(
                                "Failed to lock connection: {e}"
                            ))
                        })?;
                        conn.execute("COMMIT", []).map_err(|e| {
                            DatabaseError::TransactionError(format!("SQLite commit error: {e}"))
                        })
                    })
                    .await
                    .map_err(|e| {
                        DatabaseError::TransactionError(format!("Task join error: {e}"))
                    })??;
                }
                SqliteTransactionType::Pooled(connection) => {
                    tokio::task::spawn_blocking(move || {
                        let conn = connection.lock().map_err(|e| {
                            DatabaseError::TransactionError(format!(
                                "Failed to lock connection: {e}"
                            ))
                        })?;
                        conn.execute("COMMIT", []).map_err(|e| {
                            DatabaseError::TransactionError(format!("SQLite commit error: {e}"))
                        })
                    })
                    .await
                    .map_err(|e| {
                        DatabaseError::TransactionError(format!("Task join error: {e}"))
                    })??;
                    // Connection automatically returned to pool when connection is dropped
                }
            }
            self.is_committed = true;
        }
        Ok(())
    }

    async fn rollback(mut self: Box<Self>) -> Result<(), DatabaseError> {
        if self.is_committed {
            return Err(DatabaseError::TransactionError(
                "Transaction already committed".to_string(),
            ));
        }
        if self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already rolled back".to_string(),
            ));
        }

        if let Some(connection) = self.connection.take() {
            match connection {
                SqliteTransactionType::Owned(connection) => {
                    tokio::task::spawn_blocking(move || {
                        let conn = connection.lock().map_err(|e| {
                            DatabaseError::TransactionError(format!(
                                "Failed to lock connection: {e}"
                            ))
                        })?;
                        conn.execute("ROLLBACK", []).map_err(|e| {
                            DatabaseError::TransactionError(format!("SQLite rollback error: {e}"))
                        })
                    })
                    .await
                    .map_err(|e| {
                        DatabaseError::TransactionError(format!("Task join error: {e}"))
                    })??;
                }
                SqliteTransactionType::Pooled(connection) => {
                    tokio::task::spawn_blocking(move || {
                        let conn = connection.lock().map_err(|e| {
                            DatabaseError::TransactionError(format!(
                                "Failed to lock connection: {e}"
                            ))
                        })?;
                        conn.execute("ROLLBACK", []).map_err(|e| {
                            DatabaseError::TransactionError(format!("SQLite rollback error: {e}"))
                        })
                    })
                    .await
                    .map_err(|e| {
                        DatabaseError::TransactionError(format!("Task join error: {e}"))
                    })??;
                    // Connection automatically returned to pool when connection is dropped
                }
            }
            self.is_rolled_back = true;
        }
        Ok(())
    }

    fn is_async_native(&self) -> bool {
        false // SQLite operations are sync-wrapped
    }
}

// Critical: Implement Drop trait for SqliteTransactionWrapper
// SAFETY NET: Ensures transactions are rolled back if they go out of scope without explicit commit
impl Drop for SqliteTransactionWrapper {
    fn drop(&mut self) {
        if !self.is_committed && !self.is_rolled_back {
            if let Some(SqliteTransactionType::Pooled(_conn)) = self.connection.take() {
                // Connection is automatically returned to pool when `conn` is dropped
                // The r2d2 PooledConnection will handle connection cleanup
                // Log the uncommitted transaction for debugging
                eprintln!(
                    "WARNING: SQLite transaction dropped without explicit commit or rollback"
                );

                // Note: We cannot call async operations from sync Drop
                // The connection's return to pool will handle cleanup
                // Best practice: Always explicitly commit or rollback transactions
            }
        }
    }
}

fn convert_database_value_to_sqlite(
    value: DatabaseValue,
) -> Result<rusqlite::types::Value, DatabaseError> {
    match value {
        DatabaseValue::Null => Ok(rusqlite::types::Value::Null),
        DatabaseValue::Bool(b) => Ok(rusqlite::types::Value::Integer(if b { 1 } else { 0 })),
        DatabaseValue::I32(i) => Ok(rusqlite::types::Value::Integer(i as i64)),
        DatabaseValue::I64(i) => Ok(rusqlite::types::Value::Integer(i)),
        DatabaseValue::F64(f) => Ok(rusqlite::types::Value::Real(f)),
        DatabaseValue::Text(s) => Ok(rusqlite::types::Value::Text(s)),
        DatabaseValue::Bytes(b) => Ok(rusqlite::types::Value::Blob(b)),
        DatabaseValue::Uuid(u) => Ok(rusqlite::types::Value::Text(u.to_string())),
        DatabaseValue::DateTime(dt) => Ok(rusqlite::types::Value::Text(dt.to_rfc3339())),
        DatabaseValue::Json(j) => {
            let json_str = serde_json::to_string(&j).map_err(|e| {
                DatabaseError::TypeConversionError(format!("JSON serialization error: {e}"))
            })?;
            Ok(rusqlite::types::Value::Text(json_str))
        }
        DatabaseValue::BytesZeroCopy(b) => Ok(rusqlite::types::Value::Blob(b.to_vec())),
        DatabaseValue::JsonZeroCopy(b) => {
            let s = String::from_utf8(b.to_vec()).unwrap_or_default();
            Ok(rusqlite::types::Value::Text(s))
        }
    }
}
