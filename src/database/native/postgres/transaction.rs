//! PostgreSQL transaction implementation

use std::sync::Arc;

use async_trait::async_trait;
use tokio::sync::Mutex;
use tokio_postgres::{types::ToSql, Client};

use super::row::PostgresRowWrapper;
use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseRow, DatabaseTransaction, DatabaseValue},
};

pub enum PostgresTransactionType {
    // For owned connections (existing)
    Owned(Arc<Mutex<Client>>),
    // For pooled connections - store the client and manage transaction state
    Pooled(deadpool_postgres::Object),
}

pub struct PostgresTransactionWrapper {
    connection: Option<PostgresTransactionType>,
    is_committed: bool,
    is_rolled_back: bool,
}

impl PostgresTransactionWrapper {
    pub async fn new(client: Arc<Mutex<Client>>) -> Result<Self, DatabaseError> {
        // Begin transaction on owned connection
        {
            let client_guard = client.lock().await;
            client_guard.execute("BEGIN", &[]).await.map_err(|e| {
                DatabaseError::TransactionError(format!("Failed to begin transaction: {e}"))
            })?;
        }

        Ok(Self {
            connection: Some(PostgresTransactionType::Owned(client)),
            is_committed: false,
            is_rolled_back: false,
        })
    }

    // NEW constructor for pooled connections - stores client and begins transaction
    pub async fn new_pooled(
        pooled_client: deadpool_postgres::Object,
    ) -> Result<Self, DatabaseError> {
        // Begin transaction using SQL BEGIN command
        pooled_client.execute("BEGIN", &[]).await.map_err(|e| {
            DatabaseError::TransactionError(format!("Failed to begin pooled transaction: {e}"))
        })?;

        Ok(Self {
            connection: Some(PostgresTransactionType::Pooled(pooled_client)),
            is_committed: false,
            is_rolled_back: false,
        })
    }

    fn convert_params(
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn ToSql + Sync + Send>>, DatabaseError> {
        let mut converted = Vec::new();

        for param in params {
            let boxed_param: Box<dyn ToSql + Sync + Send> = match param {
                DatabaseValue::Null => Box::new(None::<i32>),
                DatabaseValue::Bool(b) => Box::new(*b),
                DatabaseValue::I32(i) => Box::new(*i),
                DatabaseValue::I64(i) => Box::new(*i),
                DatabaseValue::F64(f) => Box::new(*f),
                DatabaseValue::Text(s) => Box::new(s.clone()),
                DatabaseValue::Bytes(b) => Box::new(b.clone()),
                DatabaseValue::Uuid(u) => Box::new(*u),
                DatabaseValue::DateTime(dt) => Box::new(*dt),
                DatabaseValue::Json(j) => Box::new(j.clone()),
                DatabaseValue::BytesZeroCopy(b) => Box::new(b.to_vec()),
                DatabaseValue::JsonZeroCopy(b) => {
                    let json: serde_json::Value = serde_json::from_slice(b).unwrap_or_default();
                    Box::new(json)
                }
            };
            converted.push(boxed_param);
        }

        Ok(converted)
    }
}

#[async_trait]
impl DatabaseTransaction for PostgresTransactionWrapper {
    async fn execute(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let converted_params = Self::convert_params(params)?;
        let param_refs: Vec<&(dyn ToSql + Sync)> = converted_params
            .iter()
            .map(|p| {
                let r: &(dyn ToSql + Sync + Send) = p.as_ref();
                r as &(dyn ToSql + Sync)
            })
            .collect();

        match &mut self.connection {
            Some(PostgresTransactionType::Owned(client)) => {
                let client_guard = client.lock().await;
                client_guard.execute(query, &param_refs).await.map_err(|e| {
                    DatabaseError::QueryError(format!("PostgreSQL execute error: {e}"))
                })
            }
            Some(PostgresTransactionType::Pooled(client)) => client
                .execute(query, &param_refs)
                .await
                .map_err(|e| DatabaseError::QueryError(format!("PostgreSQL execute error: {e}"))),
            None => Err(DatabaseError::TransactionError(
                "Transaction already completed".to_string(),
            )),
        }
    }

    async fn query_one(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let converted_params = Self::convert_params(params)?;
        let param_refs: Vec<&(dyn ToSql + Sync)> = converted_params
            .iter()
            .map(|p| {
                let r: &(dyn ToSql + Sync + Send) = p.as_ref();
                r as &(dyn ToSql + Sync)
            })
            .collect();

        let row = match &mut self.connection {
            Some(PostgresTransactionType::Owned(client)) => {
                let client_guard = client.lock().await;
                client_guard.query_one(query, &param_refs).await
            }
            Some(PostgresTransactionType::Pooled(client)) => {
                client.query_one(query, &param_refs).await
            }
            None => {
                return Err(DatabaseError::TransactionError(
                    "Transaction already completed".to_string(),
                ))
            }
        }
        .map_err(|e| {
            if e.to_string().contains("no rows") {
                DatabaseError::NotFound
            } else {
                DatabaseError::QueryError(format!("PostgreSQL query error: {e}"))
            }
        })?;

        let wrapped_row = PostgresRowWrapper::new(row)?;
        Ok(Box::new(wrapped_row))
    }

    async fn query_all(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let converted_params = Self::convert_params(params)?;
        let param_refs: Vec<&(dyn ToSql + Sync)> = converted_params
            .iter()
            .map(|p| {
                let r: &(dyn ToSql + Sync + Send) = p.as_ref();
                r as &(dyn ToSql + Sync)
            })
            .collect();

        let rows = match &mut self.connection {
            Some(PostgresTransactionType::Owned(client)) => {
                let client_guard = client.lock().await;
                client_guard.query(query, &param_refs).await
            }
            Some(PostgresTransactionType::Pooled(client)) => client.query(query, &param_refs).await,
            None => {
                return Err(DatabaseError::TransactionError(
                    "Transaction already completed".to_string(),
                ))
            }
        }
        .map_err(|e| DatabaseError::QueryError(format!("PostgreSQL query error: {e}")))?;

        let mut results = Vec::new();
        for row in rows {
            let wrapped_row = PostgresRowWrapper::new(row)?;
            results.push(Box::new(wrapped_row) as Box<dyn DatabaseRow>);
        }

        Ok(results)
    }

    async fn commit(mut self: Box<Self>) -> Result<(), DatabaseError> {
        if self.is_committed {
            return Err(DatabaseError::TransactionError(
                "Transaction already committed".to_string(),
            ));
        }
        if self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already rolled back".to_string(),
            ));
        }

        if let Some(connection) = self.connection.take() {
            match connection {
                PostgresTransactionType::Owned(client) => {
                    let client_guard = client.lock().await;
                    client_guard.execute("COMMIT", &[]).await.map_err(|e| {
                        DatabaseError::TransactionError(format!("PostgreSQL commit error: {e}"))
                    })?;
                }
                PostgresTransactionType::Pooled(client) => {
                    client.execute("COMMIT", &[]).await.map_err(|e| {
                        DatabaseError::TransactionError(format!("PostgreSQL commit error: {e}"))
                    })?;
                    // Connection automatically returned to pool when client is dropped
                }
            }
            self.is_committed = true;
        }
        Ok(())
    }

    async fn rollback(mut self: Box<Self>) -> Result<(), DatabaseError> {
        if self.is_committed {
            return Err(DatabaseError::TransactionError(
                "Transaction already committed".to_string(),
            ));
        }
        if self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already rolled back".to_string(),
            ));
        }

        if let Some(connection) = self.connection.take() {
            match connection {
                PostgresTransactionType::Owned(client) => {
                    let client_guard = client.lock().await;
                    client_guard.execute("ROLLBACK", &[]).await.map_err(|e| {
                        DatabaseError::TransactionError(format!("PostgreSQL rollback error: {e}"))
                    })?;
                }
                PostgresTransactionType::Pooled(client) => {
                    client.execute("ROLLBACK", &[]).await.map_err(|e| {
                        DatabaseError::TransactionError(format!("PostgreSQL rollback error: {e}"))
                    })?;
                    // Connection automatically returned to pool when client is dropped
                }
            }
            self.is_rolled_back = true;
        }
        Ok(())
    }

    fn is_async_native(&self) -> bool {
        true // PostgreSQL operations are truly async
    }
}
