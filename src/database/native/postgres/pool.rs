//! PostgreSQL connection pool implementation using deadpool-postgres

use async_trait::async_trait;
use deadpool_postgres::{Config, ManagerConfig, Pool, RecyclingMethod, Runtime};
use tokio_postgres::NoTls;
use url;

use super::connection::PostgresConnectionWrapper;
use crate::database::native::common::{errors::DatabaseError, traits::DatabasePool};

pub struct PostgresPool {
    pool: Pool,
    connection_string: String,
}

impl PostgresPool {
    pub fn new(database_url: &str, max_connections: u32) -> Result<Self, DatabaseError> {
        let mut config = Config::new();

        // Parse the database URL
        let url = database_url
            .parse::<url::Url>()
            .map_err(|e| DatabaseError::PoolError(format!("Invalid database URL: {e}")))?;

        config.host = url.host_str().map(|s| s.to_string());
        config.port = url.port();
        config.user = Some(url.username().to_string());
        config.password = url.password().map(|s| s.to_string());
        config.dbname = url.path().strip_prefix('/').map(|s| s.to_string());

        // Set pool configuration
        config.manager = Some(ManagerConfig {
            recycling_method: RecyclingMethod::Fast,
        });
        config.pool = Some(deadpool_postgres::PoolConfig::new(max_connections as usize));

        let pool = config
            .create_pool(Some(Runtime::Tokio1), NoTls)
            .map_err(|e| {
                DatabaseError::PoolError(format!("Failed to create PostgreSQL pool: {e}"))
            })?;

        Ok(Self {
            pool,
            connection_string: database_url.to_string(),
        })
    }

    pub fn with_config(config: PostgresPoolConfig) -> Result<Self, DatabaseError> {
        let connection_string = config.to_connection_string();
        let mut pg_config = Config::new();

        pg_config.host = Some(config.host);
        pg_config.port = Some(config.port);
        pg_config.user = Some(config.user);
        pg_config.password = config.password;
        pg_config.dbname = Some(config.database);

        // Set pool configuration
        pg_config.manager = Some(ManagerConfig {
            recycling_method: RecyclingMethod::Fast,
        });
        pg_config.pool = Some(deadpool_postgres::PoolConfig::new(
            config.max_connections as usize,
        ));

        let pool = pg_config
            .create_pool(Some(Runtime::Tokio1), NoTls)
            .map_err(|e| {
                DatabaseError::PoolError(format!("Failed to create PostgreSQL pool: {e}"))
            })?;

        Ok(Self {
            pool,
            connection_string,
        })
    }

    /// Get the underlying deadpool for advanced usage
    pub fn inner(&self) -> &Pool {
        &self.pool
    }
}

#[async_trait]
impl DatabasePool for PostgresPool {
    type Connection = PostgresConnectionWrapper;

    async fn get_connection(&self) -> Result<Self::Connection, DatabaseError> {
        let client = self.pool.get().await.map_err(|e| {
            DatabaseError::PoolError(format!("Failed to get connection from pool: {e}"))
        })?;

        Ok(PostgresConnectionWrapper::from_pooled(client))
    }

    async fn close(&self) -> Result<(), DatabaseError> {
        // deadpool doesn't have an explicit close method
        // Connections are closed when the pool is dropped
        Ok(())
    }

    fn max_connections(&self) -> u32 {
        self.pool.status().max_size as u32
    }

    fn active_connections(&self) -> u32 {
        let status = self.pool.status();
        (status.size - status.available) as u32
    }

    fn idle_connections(&self) -> u32 {
        self.pool.status().available as u32
    }

    async fn health_check(&self) -> Result<(), DatabaseError> {
        use crate::database::native::common::traits::DatabaseConnection;
        let mut conn = self.get_connection().await?;
        conn.ping().await
    }
}

impl Clone for PostgresPool {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
            connection_string: self.connection_string.clone(),
        }
    }
}

// impl PostgresPool {
//     fn get_connection_string(&self) -> String {
//         self.connection_string.clone()
//     }
// }

/// Configuration for PostgreSQL connection pool
#[derive(Debug, Clone)]
pub struct PostgresPoolConfig {
    pub host: String,
    pub port: u16,
    pub user: String,
    pub password: Option<String>,
    pub database: String,
    pub max_connections: u32,
    pub connection_timeout: std::time::Duration,
}

impl Default for PostgresPoolConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 5432,
            user: "postgres".to_string(),
            password: None,
            database: "postgres".to_string(),
            max_connections: 10,
            connection_timeout: std::time::Duration::from_secs(30),
        }
    }
}

impl PostgresPoolConfig {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn host<S: Into<String>>(mut self, host: S) -> Self {
        self.host = host.into();
        self
    }

    pub fn port(mut self, port: u16) -> Self {
        self.port = port;
        self
    }

    pub fn user<S: Into<String>>(mut self, user: S) -> Self {
        self.user = user.into();
        self
    }

    pub fn password<S: Into<String>>(mut self, password: S) -> Self {
        self.password = Some(password.into());
        self
    }

    pub fn database<S: Into<String>>(mut self, database: S) -> Self {
        self.database = database.into();
        self
    }

    pub fn max_connections(mut self, max: u32) -> Self {
        self.max_connections = max;
        self
    }

    pub fn connection_timeout(mut self, timeout: std::time::Duration) -> Self {
        self.connection_timeout = timeout;
        self
    }

    pub fn to_connection_string(&self) -> String {
        let mut url = format!(
            "postgresql://{}@{}:{}/{}",
            self.user, self.host, self.port, self.database
        );

        if let Some(ref password) = self.password {
            url = format!(
                "postgresql://{}:{}@{}:{}/{}",
                self.user, password, self.host, self.port, self.database
            );
        }

        url
    }
}
