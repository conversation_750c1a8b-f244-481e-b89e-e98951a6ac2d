//! PostgreSQL row implementation for DatabaseRow trait

use std::{borrow::Cow, collections::HashMap};

use chrono::{DateTime, Utc};
use serde_json::Value as JsonValue;
use tokio_postgres::{types::Type as PostgresType, Row as PostgresRow};
use uuid::Uuid;

use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseRow, DatabaseValue},
};

pub struct PostgresRowWrapper {
    values: Vec<DatabaseValue>,
    column_names: Vec<String>,
    column_map: HashMap<String, usize>,
}

impl PostgresRowWrapper {
    pub fn new(row: PostgresRow) -> Result<Self, DatabaseError> {
        let column_count = row.len();
        let mut column_names = Vec::with_capacity(column_count);
        let mut column_map = HashMap::new();
        let mut values = Vec::with_capacity(column_count);

        // Get column names and build the map
        for i in 0..column_count {
            let column = row
                .columns()
                .get(i)
                .ok_or_else(|| DatabaseError::QueryError(format!("Invalid column index: {i}")))?;
            let name = column.name().to_string();
            column_map.insert(name.clone(), i);
            column_names.push(name);
        }

        // Extract all values
        for i in 0..column_count {
            let column = row
                .columns()
                .get(i)
                .ok_or_else(|| DatabaseError::QueryError(format!("Invalid column index: {i}")))?;

            let db_value = convert_postgres_value_to_database_value(&row, i, column.type_())?;
            values.push(db_value);
        }

        Ok(PostgresRowWrapper {
            values,
            column_names,
            column_map,
        })
    }
}

impl DatabaseRow for PostgresRowWrapper {
    fn get_by_index(&self, index: usize) -> Result<DatabaseValue, DatabaseError> {
        self.values
            .get(index)
            .cloned()
            .ok_or_else(|| DatabaseError::QueryError(format!("Column index {index} out of bounds")))
    }

    fn get_by_name(&self, name: &str) -> Result<DatabaseValue, DatabaseError> {
        let index = self
            .column_map
            .get(name)
            .ok_or_else(|| DatabaseError::QueryError(format!("Column '{name}' not found")))?;
        self.get_by_index(*index)
    }

    fn column_count(&self) -> usize {
        self.column_names.len()
    }

    fn column_names(&self) -> Cow<'_, [String]> {
        Cow::Borrowed(&self.column_names)
    }
}

impl std::fmt::Debug for PostgresRowWrapper {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("PostgresRowWrapper")
            .field("column_names", &self.column_names)
            .field("values", &self.values)
            .finish()
    }
}

fn convert_postgres_value_to_database_value(
    row: &PostgresRow,
    index: usize,
    pg_type: &PostgresType,
) -> Result<DatabaseValue, DatabaseError> {
    // NULL values are handled by individual type conversions using Option<T>

    match *pg_type {
        // Boolean
        PostgresType::BOOL => {
            let value: Option<bool> = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("Boolean conversion error: {e}"))
            })?;
            match value {
                Some(v) => Ok(DatabaseValue::Bool(v)),
                None => Ok(DatabaseValue::Null),
            }
        }

        // Integer types
        PostgresType::INT2 => {
            let value: Option<i16> = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("i16 conversion error: {e}"))
            })?;
            match value {
                Some(v) => Ok(DatabaseValue::I32(v as i32)),
                None => Ok(DatabaseValue::Null),
            }
        }
        PostgresType::INT4 => {
            let value: Option<i32> = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("i32 conversion error: {e}"))
            })?;
            match value {
                Some(v) => Ok(DatabaseValue::I32(v)),
                None => Ok(DatabaseValue::Null),
            }
        }
        PostgresType::INT8 => {
            let value: Option<i64> = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("i64 conversion error: {e}"))
            })?;
            match value {
                Some(v) => Ok(DatabaseValue::I64(v)),
                None => Ok(DatabaseValue::Null),
            }
        }

        // Float types
        PostgresType::FLOAT4 => {
            let value: f32 = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("f32 conversion error: {e}"))
            })?;
            Ok(DatabaseValue::F64(value as f64))
        }
        PostgresType::FLOAT8 => {
            let value: f64 = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("f64 conversion error: {e}"))
            })?;
            Ok(DatabaseValue::F64(value))
        }

        // Text types
        PostgresType::TEXT | PostgresType::VARCHAR | PostgresType::CHAR | PostgresType::NAME => {
            let value: String = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("String conversion error: {e}"))
            })?;
            Ok(DatabaseValue::Text(value))
        }

        // Binary data
        PostgresType::BYTEA => {
            let value: Vec<u8> = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("Bytes conversion error: {e}"))
            })?;
            Ok(DatabaseValue::Bytes(value))
        }

        // UUID
        PostgresType::UUID => {
            let value: Uuid = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("UUID conversion error: {e}"))
            })?;
            Ok(DatabaseValue::Uuid(value))
        }

        // Timestamp types
        PostgresType::TIMESTAMP => {
            let value: chrono::NaiveDateTime = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("NaiveDateTime conversion error: {e}"))
            })?;
            Ok(DatabaseValue::DateTime(value.and_utc()))
        }
        PostgresType::TIMESTAMPTZ => {
            let value: DateTime<Utc> = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("DateTime<Utc> conversion error: {e}"))
            })?;
            Ok(DatabaseValue::DateTime(value))
        }

        // JSON types
        PostgresType::JSON | PostgresType::JSONB => {
            let value: JsonValue = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("JSON conversion error: {e}"))
            })?;
            Ok(DatabaseValue::Json(value))
        }

        // Numeric/Decimal types - convert to f64 for simplicity
        PostgresType::NUMERIC => {
            // PostgreSQL NUMERIC can be very large, but we'll try to convert to f64
            // In a production system, you might want to use a decimal library
            let value: String = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!(
                    "Numeric as string conversion error: {e}"
                ))
            })?;
            let parsed: f64 = value.parse().map_err(|e| {
                DatabaseError::TypeConversionError(format!("Numeric to f64 conversion error: {e}"))
            })?;
            Ok(DatabaseValue::F64(parsed))
        }

        // Array types - convert to JSON for simplicity
        _ if matches!(pg_type.kind(), tokio_postgres::types::Kind::Array(_)) => {
            // For arrays, we'll try to get them as JSON
            let value: JsonValue = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!("Array to JSON conversion error: {e}"))
            })?;
            Ok(DatabaseValue::Json(value))
        }

        // Fallback: try to get as string
        _ => {
            let value: String = row.try_get(index).map_err(|e| {
                DatabaseError::TypeConversionError(format!(
                    "Unsupported type {pg_type:?}, string conversion error: {e}"
                ))
            })?;
            Ok(DatabaseValue::Text(value))
        }
    }
}
