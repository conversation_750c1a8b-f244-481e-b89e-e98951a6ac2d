//! PostgreSQL connection implementation

use std::sync::Arc;

use async_trait::async_trait;
use tokio::sync::Mutex;
use tokio_postgres::{types::ToSql, Client};

use super::{row::PostgresRowWrapper, transaction::PostgresTransactionWrapper};
use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseConnection, DatabaseRow, DatabaseTransaction, DatabaseValue},
};

pub enum PostgresConnectionWrapper {
    Owned(Arc<Mutex<Client>>),
    Pooled(Box<Option<deadpool_postgres::Object>>), // Option allows ownership transfer
}

impl PostgresConnectionWrapper {
    pub fn new(client: Client) -> Self {
        Self::Owned(Arc::new(Mutex::new(client)))
    }

    pub fn from_pooled(client: deadpool_postgres::Object) -> Self {
        Self::Pooled(Box::new(Some(client)))
    }

    fn convert_params(
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn ToSql + Sync + Send>>, DatabaseError> {
        let mut converted = Vec::new();

        for param in params {
            let boxed_param: Box<dyn ToSql + Sync + Send> = match param {
                DatabaseValue::Null => Box::new(None::<i32>),
                DatabaseValue::Bool(b) => Box::new(*b),
                DatabaseValue::I32(i) => Box::new(*i),
                DatabaseValue::I64(i) => Box::new(*i),
                DatabaseValue::F64(f) => Box::new(*f as f32), // Convert f64 to f32 for PostgreSQL REAL type
                DatabaseValue::Text(s) => Box::new(s.clone()),
                DatabaseValue::Bytes(b) => Box::new(b.clone()),
                DatabaseValue::Uuid(u) => Box::new(*u),
                DatabaseValue::DateTime(dt) => Box::new(*dt),
                DatabaseValue::Json(j) => Box::new(j.clone()),
                DatabaseValue::BytesZeroCopy(b) => Box::new(b.to_vec()),
                DatabaseValue::JsonZeroCopy(b) => {
                    let json: serde_json::Value = serde_json::from_slice(b).unwrap_or_default();
                    Box::new(json)
                }
            };
            converted.push(boxed_param);
        }

        Ok(converted)
    }
}

#[async_trait]
impl DatabaseConnection for PostgresConnectionWrapper {
    async fn execute(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError> {
        let converted_params = Self::convert_params(params)?;
        let param_refs: Vec<&(dyn ToSql + Sync)> = converted_params
            .iter()
            .map(|p| p.as_ref() as &(dyn ToSql + Sync))
            .collect();

        match self {
            PostgresConnectionWrapper::Owned(client) => {
                let client_guard = client.lock().await;
                client_guard.execute(query, &param_refs).await.map_err(|e| {
                    DatabaseError::QueryError(format!("PostgreSQL execute error: {e}"))
                })
            }
            PostgresConnectionWrapper::Pooled(client_opt) => {
                let client = client_opt.as_deref().ok_or_else(|| {
                    DatabaseError::ConnectionError(
                        "Connection is in use by a transaction".to_string(),
                    )
                })?;
                client.execute(query, &param_refs).await.map_err(|e| {
                    DatabaseError::QueryError(format!("PostgreSQL execute error: {e}"))
                })
            }
        }
    }

    async fn query_one(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError> {
        let converted_params = Self::convert_params(params)?;
        let param_refs: Vec<&(dyn ToSql + Sync)> = converted_params
            .iter()
            .map(|p| p.as_ref() as &(dyn ToSql + Sync))
            .collect();

        let row = match self {
            PostgresConnectionWrapper::Owned(client) => {
                let client_guard = client.lock().await;
                client_guard.query_one(query, &param_refs).await
            }
            PostgresConnectionWrapper::Pooled(client_opt) => {
                let client = client_opt.as_deref().ok_or_else(|| {
                    DatabaseError::ConnectionError(
                        "Connection is in use by a transaction".to_string(),
                    )
                })?;
                client.query_one(query, &param_refs).await
            }
        }
        .map_err(|e| {
            if e.to_string().contains("no rows") {
                DatabaseError::NotFound
            } else {
                DatabaseError::QueryError(format!("PostgreSQL query error: {e}"))
            }
        })?;

        let wrapped_row = PostgresRowWrapper::new(row)?;
        Ok(Box::new(wrapped_row))
    }

    async fn query_all(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError> {
        let converted_params = Self::convert_params(params)?;
        let param_refs: Vec<&(dyn ToSql + Sync)> = converted_params
            .iter()
            .map(|p| p.as_ref() as &(dyn ToSql + Sync))
            .collect();

        let rows = match self {
            PostgresConnectionWrapper::Owned(client) => {
                let client_guard = client.lock().await;
                client_guard.query(query, &param_refs).await
            }
            PostgresConnectionWrapper::Pooled(client_opt) => {
                let client = client_opt.as_deref().ok_or_else(|| {
                    DatabaseError::ConnectionError(
                        "Connection is in use by a transaction".to_string(),
                    )
                })?;
                client.query(query, &param_refs).await
            }
        }
        .map_err(|e| DatabaseError::QueryError(format!("PostgreSQL query error: {e}")))?;

        let mut results = Vec::new();
        for row in rows {
            let wrapped_row = PostgresRowWrapper::new(row)?;
            results.push(Box::new(wrapped_row) as Box<dyn DatabaseRow>);
        }

        Ok(results)
    }

    async fn begin_transaction(&mut self) -> Result<Box<dyn DatabaseTransaction>, DatabaseError> {
        match self {
            PostgresConnectionWrapper::Owned(client) => {
                let transaction = PostgresTransactionWrapper::new(Arc::clone(client)).await?;
                Ok(Box::new(transaction))
            }
            PostgresConnectionWrapper::Pooled(pooled_client_opt) => {
                // Take ownership of the pooled connection
                let pooled_client = pooled_client_opt.take().ok_or_else(|| {
                    DatabaseError::TransactionError(
                        "Connection is already in a transaction".to_string(),
                    )
                })?;

                let transaction = PostgresTransactionWrapper::new_pooled(pooled_client).await?;
                Ok(Box::new(transaction))
            }
        }
    }

    async fn ping(&mut self) -> Result<(), DatabaseError> {
        match self {
            PostgresConnectionWrapper::Owned(client) => {
                let client_guard = client.lock().await;
                client_guard.execute("SELECT 1", &[]).await.map_err(|e| {
                    DatabaseError::ConnectionError(format!("PostgreSQL ping failed: {e}"))
                })?;
            }
            PostgresConnectionWrapper::Pooled(client_opt) => {
                let client = client_opt.as_deref().ok_or_else(|| {
                    DatabaseError::ConnectionError(
                        "Connection is in use by a transaction".to_string(),
                    )
                })?;
                client.execute("SELECT 1", &[]).await.map_err(|e| {
                    DatabaseError::ConnectionError(format!("PostgreSQL ping failed: {e}"))
                })?;
            }
        }
        Ok(())
    }

    fn is_async_native(&self) -> bool {
        true // PostgreSQL operations are truly async
    }
}

// Note: Clone is not implemented for PostgresConnectionWrapper because
// deadpool::managed::Object doesn't implement Clone. This is intentional
// as pooled connections should not be cloned arbitrarily.
