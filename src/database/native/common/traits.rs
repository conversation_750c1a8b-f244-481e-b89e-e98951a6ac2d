//! Core database abstraction traits for native drivers

use std::{borrow::Cow, fmt::Debug};

use async_trait::async_trait;
use bytes::Bytes;
use chrono::{DateTime, Utc};
use serde_json::Value as JsonValue;
use uuid::Uuid;

use super::errors::DatabaseError;

// Core database value type
#[derive(Debug, Clone)]
pub enum DatabaseValue {
    Null,
    Bool(bool),
    I32(i32),
    I64(i64),
    F64(f64),
    Text(String),
    Bytes(Vec<u8>),
    /// Zero-copy bytes for efficient handling of large binary data
    BytesZeroCopy(Bytes),
    Uuid(Uuid),
    DateTime(DateTime<Utc>),
    Json(JsonValue),
    /// Zero-copy JSON for efficient parsing of large JSON documents
    JsonZeroCopy(Bytes),
}

impl DatabaseValue {
    /// Convert to bytes efficiently, avoiding copies when possible
    pub fn as_bytes(&self) -> Cow<'_, [u8]> {
        match self {
            DatabaseValue::Bytes(b) => Cow::Borrowed(b),
            DatabaseValue::BytesZeroCopy(b) => Cow::Borrowed(b),
            DatabaseValue::Text(s) => Cow::Borrowed(s.as_bytes()),
            DatabaseValue::Json(j) => {
                // This requires allocation, but it's the best we can do for JSON
                Cow::Owned(serde_json::to_vec(j).unwrap_or_default())
            }
            DatabaseValue::JsonZeroCopy(b) => Cow::Borrowed(b),
            _ => Cow::Owned(self.to_string().into_bytes()),
        }
    }

    /// Parse JSON efficiently from zero-copy bytes
    pub fn parse_json_zerocopy(bytes: Bytes) -> Result<JsonValue, serde_json::Error> {
        serde_json::from_slice(&bytes)
    }

    /// Convert to string efficiently, avoiding allocations when possible
    pub fn as_string(&self) -> Cow<'_, str> {
        match self {
            DatabaseValue::Text(s) => Cow::Borrowed(s),
            DatabaseValue::Bytes(b) => match std::str::from_utf8(b) {
                Ok(s) => Cow::Borrowed(s),
                Err(_) => Cow::Owned(format!("Binary data ({} bytes)", b.len())),
            },
            DatabaseValue::BytesZeroCopy(b) => match std::str::from_utf8(b) {
                Ok(s) => Cow::Borrowed(s),
                Err(_) => Cow::Owned(format!("Binary data ({} bytes)", b.len())),
            },
            _ => Cow::Owned(self.to_string()),
        }
    }
}

impl std::fmt::Display for DatabaseValue {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DatabaseValue::Null => write!(f, "NULL"),
            DatabaseValue::Bool(b) => write!(f, "{b}"),
            DatabaseValue::I32(i) => write!(f, "{i}"),
            DatabaseValue::I64(i) => write!(f, "{i}"),
            DatabaseValue::F64(fl) => write!(f, "{fl}"),
            DatabaseValue::Text(s) => write!(f, "{s}"),
            DatabaseValue::Bytes(b) => write!(f, "Binary data ({} bytes)", b.len()),
            DatabaseValue::BytesZeroCopy(b) => write!(f, "Binary data ({} bytes)", b.len()),
            DatabaseValue::Uuid(u) => write!(f, "{u}"),
            DatabaseValue::DateTime(dt) => write!(f, "{}", dt.to_rfc3339()),
            DatabaseValue::Json(j) => write!(f, "{j}"),
            DatabaseValue::JsonZeroCopy(b) => match std::str::from_utf8(b) {
                Ok(s) => write!(f, "{s}"),
                Err(_) => write!(f, "Invalid JSON bytes"),
            },
        }
    }
}

// Row trait for unified row access
pub trait DatabaseRow: Send + Sync + Debug {
    fn get_by_index(&self, index: usize) -> Result<DatabaseValue, DatabaseError>;
    fn get_by_name(&self, name: &str) -> Result<DatabaseValue, DatabaseError>;
    fn column_count(&self) -> usize;
    /// Return column names using Copy-on-Write to avoid unnecessary allocations
    fn column_names(&self) -> Cow<'_, [String]>;
    /// Legacy method for backward compatibility
    fn column_names_vec(&self) -> Vec<String> {
        self.column_names().into_owned()
    }
}

// Transaction trait - Mixed sync/async to support both rusqlite and async drivers
#[async_trait]
pub trait DatabaseTransaction: Send + Sync {
    async fn execute(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError>;
    async fn query_one(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError>;
    async fn query_all(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError>;
    async fn commit(self: Box<Self>) -> Result<(), DatabaseError>;
    async fn rollback(self: Box<Self>) -> Result<(), DatabaseError>;

    // Indicates if this transaction is async-native or sync-wrapped
    fn is_async_native(&self) -> bool;
}

// Connection trait - Mixed sync/async to support both rusqlite and async drivers
#[async_trait]
pub trait DatabaseConnection: Send + Sync {
    async fn execute(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError>;
    async fn query_one(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError>;
    async fn query_all(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError>;
    async fn begin_transaction(&mut self) -> Result<Box<dyn DatabaseTransaction>, DatabaseError>;
    async fn ping(&mut self) -> Result<(), DatabaseError>;

    // Indicates if this connection is async-native or sync-wrapped
    fn is_async_native(&self) -> bool;
}

// Pool trait
#[async_trait]
pub trait DatabasePool: Send + Sync + Clone {
    type Connection: DatabaseConnection;

    async fn get_connection(&self) -> Result<Self::Connection, DatabaseError>;
    async fn close(&self) -> Result<(), DatabaseError>;
    fn max_connections(&self) -> u32;
    fn active_connections(&self) -> u32;
    fn idle_connections(&self) -> u32;

    // Health check
    async fn health_check(&self) -> Result<(), DatabaseError>;
}
