//! Error types for native database drivers

use std::{error::Error, fmt};

#[derive(Debug)]
pub enum DatabaseError {
    ConnectionError(String),
    QueryError(String),
    TransactionError(String),
    TypeConversionError(String),
    PoolError(String),
    MigrationError(String),
    NotFound,
    UniqueConstraintViolation(String),
    ForeignKeyViolation(String),
    Other(String),
}

impl fmt::Display for DatabaseError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DatabaseError::ConnectionError(msg) => write!(f, "Connection error: {msg}"),
            DatabaseError::QueryError(msg) => write!(f, "Query error: {msg}"),
            DatabaseError::TransactionError(msg) => write!(f, "Transaction error: {msg}"),
            DatabaseError::TypeConversionError(msg) => write!(f, "Type conversion error: {msg}"),
            DatabaseError::PoolError(msg) => write!(f, "Pool error: {msg}"),
            DatabaseError::MigrationError(msg) => write!(f, "Migration error: {msg}"),
            DatabaseError::NotFound => write!(f, "Record not found"),
            DatabaseError::UniqueConstraintViolation(msg) => {
                write!(f, "Unique constraint violation: {msg}")
            }
            DatabaseError::ForeignKeyViolation(msg) => write!(f, "Foreign key violation: {msg}"),
            DatabaseError::Other(msg) => write!(f, "Database error: {msg}"),
        }
    }
}

impl Error for DatabaseError {}

// Conversion from AuthError to DatabaseError
impl From<crate::error::AuthError> for DatabaseError {
    fn from(err: crate::error::AuthError) -> Self {
        DatabaseError::Other(err.to_string())
    }
}

// Conversion from DatabaseError to AuthError
impl From<DatabaseError> for crate::error::AuthError {
    fn from(err: DatabaseError) -> Self {
        match err {
            DatabaseError::NotFound => crate::error::AuthError::database("Record not found"),
            DatabaseError::UniqueConstraintViolation(msg) => {
                crate::error::AuthError::validation(msg)
            }
            DatabaseError::ConnectionError(msg) => crate::error::AuthError::database(msg),
            DatabaseError::QueryError(msg) => crate::error::AuthError::database(msg),
            DatabaseError::TransactionError(msg) => crate::error::AuthError::database(msg),
            DatabaseError::TypeConversionError(msg) => crate::error::AuthError::validation(msg),
            DatabaseError::PoolError(msg) => crate::error::AuthError::database(msg),
            DatabaseError::MigrationError(msg) => crate::error::AuthError::database(msg),
            DatabaseError::ForeignKeyViolation(msg) => crate::error::AuthError::validation(msg),
            DatabaseError::Other(msg) => crate::error::AuthError::database(msg),
        }
    }
}

// Database-specific error conversions
#[cfg(feature = "sqlite-native")]
impl From<rusqlite::Error> for DatabaseError {
    fn from(err: rusqlite::Error) -> Self {
        match err {
            rusqlite::Error::SqliteFailure(sqlite_err, ref msg) => match sqlite_err.extended_code {
                rusqlite::ffi::SQLITE_CONSTRAINT_UNIQUE => {
                    DatabaseError::UniqueConstraintViolation(
                        msg.clone()
                            .unwrap_or_else(|| "Unique constraint violation".to_string()),
                    )
                }
                rusqlite::ffi::SQLITE_CONSTRAINT_FOREIGNKEY => DatabaseError::ForeignKeyViolation(
                    msg.clone()
                        .unwrap_or_else(|| "Foreign key constraint violation".to_string()),
                ),
                _ => DatabaseError::QueryError(format!("SQLite error: {err}")),
            },
            rusqlite::Error::QueryReturnedNoRows => DatabaseError::NotFound,
            _ => DatabaseError::QueryError(format!("SQLite error: {err}")),
        }
    }
}

#[cfg(feature = "postgres-native")]
impl From<tokio_postgres::Error> for DatabaseError {
    fn from(err: tokio_postgres::Error) -> Self {
        if let Some(db_err) = err.as_db_error() {
            match db_err.code().code() {
                "23505" => DatabaseError::UniqueConstraintViolation(db_err.message().to_string()),
                "23503" => DatabaseError::ForeignKeyViolation(db_err.message().to_string()),
                _ => DatabaseError::QueryError(format!("PostgreSQL error: {err}")),
            }
        } else {
            DatabaseError::ConnectionError(format!("PostgreSQL error: {err}"))
        }
    }
}

#[cfg(feature = "mysql-native")]
impl From<mysql_async::Error> for DatabaseError {
    fn from(err: mysql_async::Error) -> Self {
        match &err {
            mysql_async::Error::Server(server_err) => match server_err.code {
                1062 => DatabaseError::UniqueConstraintViolation(server_err.message.clone()),
                1452 => DatabaseError::ForeignKeyViolation(server_err.message.clone()),
                _ => DatabaseError::QueryError(format!("MySQL error: {}", err)),
            },
            _ => DatabaseError::ConnectionError(format!("MySQL error: {}", err)),
        }
    }
}

// Pool error conversions
#[cfg(feature = "sqlite-native")]
impl From<r2d2::Error> for DatabaseError {
    fn from(err: r2d2::Error) -> Self {
        DatabaseError::PoolError(format!("R2D2 pool error: {err}"))
    }
}

#[cfg(feature = "postgres-native")]
impl From<deadpool_postgres::PoolError> for DatabaseError {
    fn from(err: deadpool_postgres::PoolError) -> Self {
        DatabaseError::PoolError(format!("Deadpool PostgreSQL error: {err}"))
    }
}

#[cfg(feature = "mysql-native")]
impl From<deadpool::managed::PoolError<mysql_async::Error>> for DatabaseError {
    fn from(err: deadpool::managed::PoolError<mysql_async::Error>) -> Self {
        DatabaseError::PoolError(format!("Deadpool MySQL error: {}", err))
    }
}
