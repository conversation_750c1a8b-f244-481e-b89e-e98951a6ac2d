//! Common types for native database drivers

use chrono::{DateTime, Utc};
use serde_json::Value as JsonValue;
use uuid::Uuid;

use super::{errors::DatabaseError, traits::DatabaseValue};

/// Trait for converting database-specific types to DatabaseValue
pub trait ToValue {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError>;
}

/// Trait for converting DatabaseValue to specific types
pub trait FromValue: Sized {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError>;
}

// Basic type conversions
impl ToValue for bool {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::Bool(self))
    }
}

impl FromValue for bool {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::Bool(b) => Ok(b),
            DatabaseValue::I32(i) => Ok(i != 0),
            DatabaseValue::I64(i) => Ok(i != 0),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to bool"
            ))),
        }
    }
}

impl ToValue for i32 {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::I32(self))
    }
}

impl FromValue for i32 {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::I32(i) => Ok(i),
            DatabaseValue::I64(i) => i.try_into().map_err(|_| {
                DatabaseError::TypeConversionError("i64 value too large for i32".to_string())
            }),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to i32"
            ))),
        }
    }
}

impl ToValue for i64 {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::I64(self))
    }
}

impl FromValue for i64 {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::I64(i) => Ok(i),
            DatabaseValue::I32(i) => Ok(i as i64),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to i64"
            ))),
        }
    }
}

impl ToValue for f64 {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::F64(self))
    }
}

impl FromValue for f64 {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::F64(f) => Ok(f),
            DatabaseValue::I32(i) => Ok(i as f64),
            DatabaseValue::I64(i) => Ok(i as f64),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to f64"
            ))),
        }
    }
}

impl ToValue for String {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::Text(self))
    }
}

impl FromValue for String {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::Text(s) => Ok(s),
            DatabaseValue::Uuid(u) => Ok(u.to_string()),
            DatabaseValue::DateTime(dt) => Ok(dt.to_rfc3339()),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to String"
            ))),
        }
    }
}

impl ToValue for &str {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::Text(self.to_string()))
    }
}

impl ToValue for Vec<u8> {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::Bytes(self))
    }
}

impl FromValue for Vec<u8> {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::Bytes(b) => Ok(b),
            DatabaseValue::Text(s) => Ok(s.into_bytes()),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to Vec<u8>"
            ))),
        }
    }
}

impl ToValue for Uuid {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::Uuid(self))
    }
}

impl FromValue for Uuid {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::Uuid(u) => Ok(u),
            DatabaseValue::Text(s) => Uuid::parse_str(&s).map_err(|e| {
                DatabaseError::TypeConversionError(format!("Invalid UUID string: {e}"))
            }),
            DatabaseValue::Bytes(b) => Uuid::from_slice(&b).map_err(|e| {
                DatabaseError::TypeConversionError(format!("Invalid UUID bytes: {e}"))
            }),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to Uuid"
            ))),
        }
    }
}

impl ToValue for DateTime<Utc> {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::DateTime(self))
    }
}

impl FromValue for DateTime<Utc> {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::DateTime(dt) => Ok(dt),
            DatabaseValue::Text(s) => DateTime::parse_from_rfc3339(&s)
                .map(|dt| dt.with_timezone(&Utc))
                .map_err(|e| {
                    DatabaseError::TypeConversionError(format!("Invalid datetime string: {e}"))
                }),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to DateTime<Utc>"
            ))),
        }
    }
}

impl ToValue for JsonValue {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        Ok(DatabaseValue::Json(self))
    }
}

impl FromValue for JsonValue {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::Json(j) => Ok(j),
            DatabaseValue::Text(s) => serde_json::from_str(&s).map_err(|e| {
                DatabaseError::TypeConversionError(format!("Invalid JSON string: {e}"))
            }),
            _ => Err(DatabaseError::TypeConversionError(format!(
                "Cannot convert {value:?} to JsonValue"
            ))),
        }
    }
}

// Option support
impl<T: ToValue> ToValue for Option<T> {
    fn to_value(self) -> Result<DatabaseValue, DatabaseError> {
        match self {
            Some(value) => value.to_value(),
            None => Ok(DatabaseValue::Null),
        }
    }
}

impl<T: FromValue> FromValue for Option<T> {
    fn from_value(value: DatabaseValue) -> Result<Self, DatabaseError> {
        match value {
            DatabaseValue::Null => Ok(None),
            _ => T::from_value(value).map(Some),
        }
    }
}
