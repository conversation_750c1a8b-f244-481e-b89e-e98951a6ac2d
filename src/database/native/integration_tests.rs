//! Integration tests for the unified database provider

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use serde_json::json;
    use uuid::Uuid;

    use super::super::{DatabaseProvider, UnifiedConnection};
    use crate::{
        database::{
            native::common::{
                traits::{DatabaseConnection, DatabasePool, DatabaseValue},
                types::FromValue,
            },
            DatabaseProvider as DbProvider,
        },
        types::{CreatePermissionRequest, CreateRoleRequest, User, UserId, UserRole, UserStatus},
    };

    async fn create_test_table(
        conn: &mut UnifiedConnection,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let create_sql = r#"
            CREATE TABLE IF NOT EXISTS test_users (
                id TEXT PRIMARY KEY,
                username TEXT NOT NULL,
                email TEXT NOT NULL,
                age INTEGER,
                balance REAL,
                is_active INTEGER,
                metadata TEXT,
                created_at TEXT
            )
        "#;

        conn.execute(create_sql, &[]).await?;
        Ok(())
    }

    async fn insert_test_data(
        conn: &mut UnifiedConnection,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let test_uuid = Uuid::new_v4();
        let test_time = Utc::now();
        let test_metadata = json!({"role": "admin", "preferences": {"theme": "dark"}});

        // Use database-specific parameter placeholders
        let insert_sql = match conn.database_type() {
            "postgresql" => {
                r#"
                INSERT INTO test_users (id, username, email, age, balance, is_active, metadata, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            "#
            }
            _ => {
                r#"
                INSERT INTO test_users (id, username, email, age, balance, is_active, metadata, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#
            }
        };

        // Use database-specific parameter types
        let params = match conn.database_type() {
            "postgresql" => [
                DatabaseValue::Text(test_uuid.to_string()), // PostgreSQL expects TEXT for UUID in this schema
                DatabaseValue::Text("testuser".to_string()),
                DatabaseValue::Text("<EMAIL>".to_string()),
                DatabaseValue::I32(25),
                DatabaseValue::F64(100.50), // Now properly converted to f32 in connection wrapper
                DatabaseValue::I32(1),      // PostgreSQL expects INTEGER for boolean in this schema
                DatabaseValue::Text(test_metadata.to_string()), // PostgreSQL expects TEXT for JSON in this schema
                DatabaseValue::Text(test_time.to_rfc3339()), // PostgreSQL expects TEXT for datetime in this schema
            ],
            "mysql" => [
                DatabaseValue::Text(test_uuid.to_string()), // MySQL expects TEXT for UUID in this schema
                DatabaseValue::Text("testuser".to_string()),
                DatabaseValue::Text("<EMAIL>".to_string()),
                DatabaseValue::I32(25),
                DatabaseValue::F64(100.50),
                DatabaseValue::Bool(true), // MySQL handles boolean conversion in wrapper
                DatabaseValue::Text(test_metadata.to_string()), // MySQL expects TEXT for JSON in this schema
                DatabaseValue::Text(test_time.to_rfc3339()), // MySQL expects TEXT for datetime in this schema
            ],
            _ => [
                DatabaseValue::Uuid(test_uuid),
                DatabaseValue::Text("testuser".to_string()),
                DatabaseValue::Text("<EMAIL>".to_string()),
                DatabaseValue::I32(25),
                DatabaseValue::F64(100.50),
                DatabaseValue::Bool(true),
                DatabaseValue::Json(test_metadata),
                DatabaseValue::DateTime(test_time),
            ],
        };

        conn.execute(insert_sql, &params).await?;
        Ok(())
    }

    #[cfg(feature = "sqlite-native")]
    #[tokio::test]
    async fn test_sqlite_unified_provider() {
        let provider =
            DatabaseProvider::sqlite_memory(5).expect("Failed to create SQLite provider");

        // Test provider info
        assert_eq!(provider.database_type(), "sqlite");
        assert_eq!(provider.max_connections(), 5);

        // Test connection
        let mut conn = provider
            .get_connection()
            .await
            .expect("Failed to get connection");

        // Test basic operations
        create_test_table(&mut conn)
            .await
            .expect("Failed to create table");

        insert_test_data(&mut conn)
            .await
            .expect("Failed to insert data");

        // Test query
        let row = conn
            .query_one(
                "SELECT * FROM test_users WHERE username = ?",
                &[DatabaseValue::Text("testuser".to_string())],
            )
            .await
            .expect("Failed to query data");

        let username: String = FromValue::from_value(row.get_by_name("username").unwrap()).unwrap();
        assert_eq!(username, "testuser");

        // Test direct update on main connection
        conn.execute(
            "UPDATE test_users SET age = ? WHERE username = ?",
            &[
                DatabaseValue::I32(26),
                DatabaseValue::Text("testuser".to_string()),
            ],
        )
        .await
        .expect("Failed to update data");

        // Verify update
        let row = conn
            .query_one(
                "SELECT age FROM test_users WHERE username = ?",
                &[DatabaseValue::Text("testuser".to_string())],
            )
            .await
            .expect("Failed to query updated data");

        let age: i32 = FromValue::from_value(row.get_by_name("age").unwrap()).unwrap();
        assert_eq!(age, 26);

        // Test health check
        provider.health_check().await.expect("Health check failed");
    }

    #[cfg(feature = "postgres-native")]
    #[tokio::test]
    async fn test_postgresql_unified_provider() {
        // Skip if no PostgreSQL available
        let database_url = std::env::var("DATABASE_URL_POSTGRES").unwrap_or_else(|_| {
            "postgres://postgres:postgres@localhost:5433/indidus_auth_test".to_string()
        });

        let provider = match DatabaseProvider::postgresql(&database_url, 5) {
            Ok(p) => p,
            Err(_) => {
                println!("Skipping PostgreSQL test - database not available");
                return;
            }
        };

        // Test provider info
        assert_eq!(provider.database_type(), "postgresql");
        assert_eq!(provider.max_connections(), 5);

        // Test connection
        let mut conn = provider
            .get_connection()
            .await
            .expect("Failed to get connection");

        // Test basic operations
        create_test_table(&mut conn)
            .await
            .expect("Failed to create table");

        insert_test_data(&mut conn)
            .await
            .expect("Failed to insert data");

        // Test query - use query_all and take first result to handle multiple rows
        let rows = conn
            .query_all(
                "SELECT * FROM test_users WHERE username = $1 LIMIT 1",
                &[DatabaseValue::Text("testuser".to_string())],
            )
            .await
            .expect("Failed to query data");

        if rows.is_empty() {
            panic!("No data found for username 'testuser'");
        }

        let row = &rows[0];

        let username: String = FromValue::from_value(row.get_by_name("username").unwrap()).unwrap();
        assert_eq!(username, "testuser");

        // Test health check
        provider.health_check().await.expect("Health check failed");
    }

    #[cfg(feature = "mysql-native")]
    #[tokio::test]
    async fn test_mysql_unified_provider() {
        // Skip if no MySQL available - try multiple common configurations
        let database_urls = vec![
            std::env::var("MYSQL_URL").unwrap_or_default(),
            "mysql://root:@localhost/test".to_string(),
            "mysql://root:root@localhost/test".to_string(),
            "mysql://admin:admin@localhost/test".to_string(),
            "mysql://root:@localhost/mysql".to_string(),
        ];

        let mut provider = None;
        for url in database_urls {
            if !url.is_empty() {
                if let Ok(p) = DatabaseProvider::mysql(&url, 5) {
                    provider = Some(p);
                    break;
                }
            }
        }

        let provider = match provider {
            Some(p) => p,
            None => {
                println!(
                    "Skipping MySQL test - database not available with any common configuration"
                );
                return;
            }
        };

        // Test provider info
        assert_eq!(provider.database_type(), "mysql");
        assert_eq!(provider.max_connections(), 5);

        // Test connection
        let mut conn = match provider.get_connection().await {
            Ok(conn) => conn,
            Err(e) => {
                println!("Skipping MySQL test - failed to get connection: {}", e);
                return;
            }
        };

        // Test basic operations
        create_test_table(&mut conn)
            .await
            .expect("Failed to create table");

        insert_test_data(&mut conn)
            .await
            .expect("Failed to insert data");

        // Test query
        let row = conn
            .query_one(
                "SELECT * FROM test_users WHERE username = ?",
                &[DatabaseValue::Text("testuser".to_string())],
            )
            .await
            .expect("Failed to query data");

        let username: String = FromValue::from_value(row.get_by_name("username").unwrap()).unwrap();
        assert_eq!(username, "testuser");

        // Test health check
        provider.health_check().await.expect("Health check failed");
    }

    #[tokio::test]
    async fn test_provider_from_url() {
        // Test SQLite URL detection
        #[cfg(feature = "sqlite-native")]
        {
            let temp_dir = std::env::temp_dir();
            let test_db_path = temp_dir.join(format!("test_{}.db", uuid::Uuid::new_v4()));
            let provider =
                DatabaseProvider::from_url(&format!("sqlite://{}", test_db_path.display()), 5)
                    .expect("Failed to create SQLite provider from URL");
            assert_eq!(provider.database_type(), "sqlite");

            // Clean up test database
            let _ = std::fs::remove_file(&test_db_path);
        }

        // Test PostgreSQL URL detection
        #[cfg(feature = "postgres-native")]
        {
            let provider = DatabaseProvider::from_url("postgresql://user:pass@localhost/db", 5);
            // May fail due to connection, but should detect the type correctly
            match provider {
                Ok(p) => assert_eq!(p.database_type(), "postgresql"),
                Err(_) => {} // Connection failed, but type detection worked
            }
        }

        // Test MySQL URL detection
        #[cfg(feature = "mysql-native")]
        {
            let provider = DatabaseProvider::from_url("mysql://user:pass@localhost/db", 5);
            // May fail due to connection, but should detect the type correctly
            match provider {
                Ok(p) => assert_eq!(p.database_type(), "mysql"),
                Err(_) => {} // Connection failed, but type detection worked
            }
        }

        // Test unsupported URL
        let result = DatabaseProvider::from_url("unsupported://localhost/db", 5);
        assert!(result.is_err());
    }

    #[cfg(feature = "sqlite-native")]
    #[tokio::test]
    async fn test_concurrent_connections() {
        // Use a unique file-based database in temp directory to avoid in-memory locking issues
        let temp_dir = std::env::temp_dir();
        let db_file = temp_dir.join(format!("test_concurrent_{}.db", uuid::Uuid::new_v4()));
        let provider =
            DatabaseProvider::sqlite(&db_file, 10).expect("Failed to create SQLite provider");

        // Test multiple concurrent connections
        let mut handles = Vec::new();

        for i in 0..5 {
            let provider_clone = provider.clone();
            let handle = tokio::spawn(async move {
                let mut conn = provider_clone
                    .get_connection()
                    .await
                    .expect("Failed to get connection");

                // Create a unique table for this connection
                let table_name = format!("test_table_{i}");
                let create_sql = format!("CREATE TABLE {table_name} (id INTEGER, value TEXT)");
                conn.execute(&create_sql, &[])
                    .await
                    .expect("Failed to create table");

                // Insert some data
                let insert_sql = format!("INSERT INTO {table_name} (id, value) VALUES (?, ?)");
                conn.execute(
                    &insert_sql,
                    &[
                        DatabaseValue::I32(i),
                        DatabaseValue::Text(format!("value_{i}")),
                    ],
                )
                .await
                .expect("Failed to insert data");

                // Query the data back
                let query_sql = format!("SELECT value FROM {table_name} WHERE id = ?");
                let row = conn
                    .query_one(&query_sql, &[DatabaseValue::I32(i)])
                    .await
                    .expect("Failed to query data");

                let value: String =
                    FromValue::from_value(row.get_by_name("value").unwrap()).unwrap();
                assert_eq!(value, format!("value_{i}"));

                i
            });
            handles.push(handle);
        }

        // Wait for all tasks to complete
        for handle in handles {
            handle.await.expect("Task failed");
        }

        // Verify pool statistics
        assert!(provider.active_connections() <= 10);
        assert!(provider.idle_connections() <= 10);

        // Clean up test database
        let _ = std::fs::remove_file(&db_file);
    }

    #[cfg(feature = "sqlite-native")]
    #[tokio::test]
    async fn test_error_handling() {
        let provider =
            DatabaseProvider::sqlite_memory(5).expect("Failed to create SQLite provider");

        let mut conn = provider
            .get_connection()
            .await
            .expect("Failed to get connection");

        // Test invalid SQL
        let result = conn.execute("INVALID SQL STATEMENT", &[]).await;
        assert!(result.is_err());

        // Test query on non-existent table
        let result = conn
            .query_one("SELECT * FROM non_existent_table", &[])
            .await;
        assert!(result.is_err());

        // Test not found
        conn.execute("CREATE TABLE empty_table (id INTEGER)", &[])
            .await
            .expect("Failed to create table");

        let result = conn
            .query_one(
                "SELECT * FROM empty_table WHERE id = ?",
                &[DatabaseValue::I32(999)],
            )
            .await;
        assert!(result.is_err());
    }

    async fn create_test_provider() -> (Box<dyn DbProvider>, Option<std::path::PathBuf>) {
        #[cfg(feature = "sqlite-native")]
        {
            let temp_dir = std::env::temp_dir();
            let db_file = temp_dir.join(format!("test_db_{}.db", uuid::Uuid::new_v4()));
            let provider = crate::database::native_sqlite::NativeSqliteProvider::new(
                &db_file.to_string_lossy(),
            )
            .expect("Failed to create SQLite provider");
            provider
                .initialize()
                .await
                .expect("Failed to initialize provider");
            (Box::new(provider), Some(db_file))
        }
        #[cfg(all(feature = "postgres-native", not(feature = "sqlite-native")))]
        {
            let provider = crate::database::native_postgres::NativePostgresProvider::new(
                "postgresql://test:test@localhost/test",
            )
            .await
            .expect("Failed to create PostgreSQL provider");
            provider
                .initialize()
                .await
                .expect("Failed to initialize provider");
            (Box::new(provider), None)
        }
        #[cfg(all(
            feature = "mysql-native",
            not(feature = "sqlite-native"),
            not(feature = "postgres-native")
        ))]
        {
            let provider = crate::database::native_mysql::NativeMysqlProvider::new(
                "mysql://test:test@localhost/test",
            )
            .await
            .expect("Failed to create MySQL provider");
            provider
                .initialize()
                .await
                .expect("Failed to initialize provider");
            (Box::new(provider), None)
        }
    }

    #[tokio::test]
    async fn test_role_hierarchy_and_effective_permissions() {
        let (provider, db_file_path) = create_test_provider().await;

        // Create test permissions
        let delete_permission = provider
            .create_permission(CreatePermissionRequest {
                name: "delete_posts".to_string(),
                description: Some("Delete posts permission".to_string()),
                resource: "posts".to_string(),
                action: "delete".to_string(),
                metadata: Some(serde_json::json!({})),
            })
            .await
            .expect("Failed to create delete permission");

        let edit_permission = provider
            .create_permission(CreatePermissionRequest {
                name: "edit_posts".to_string(),
                description: Some("Edit posts permission".to_string()),
                resource: "posts".to_string(),
                action: "edit".to_string(),
                metadata: Some(serde_json::json!({})),
            })
            .await
            .expect("Failed to create edit permission");

        let create_permission = provider
            .create_permission(CreatePermissionRequest {
                name: "create_posts".to_string(),
                description: Some("Create posts permission".to_string()),
                resource: "posts".to_string(),
                action: "create".to_string(),
                metadata: Some(serde_json::json!({})),
            })
            .await
            .expect("Failed to create create permission");

        // Create three-level role hierarchy: super-admin -> editor -> contributor
        let contributor_role = provider
            .create_role(CreateRoleRequest {
                name: "contributor".to_string(),
                description: Some("Contributor role".to_string()),
                parent_id: None,
                permissions: None,
                metadata: Some(serde_json::json!({})),
            })
            .await
            .expect("Failed to create contributor role");

        let editor_role = provider
            .create_role(CreateRoleRequest {
                name: "editor".to_string(),
                description: Some("Editor role".to_string()),
                parent_id: Some(contributor_role.id),
                permissions: None,
                metadata: Some(serde_json::json!({})),
            })
            .await
            .expect("Failed to create editor role");

        let super_admin_role = provider
            .create_role(CreateRoleRequest {
                name: "super-admin".to_string(),
                description: Some("Super admin role".to_string()),
                parent_id: Some(editor_role.id),
                permissions: None,
                metadata: Some(serde_json::json!({})),
            })
            .await
            .expect("Failed to create super-admin role");

        // Assign permissions to roles
        provider
            .assign_permission_to_role(contributor_role.id, create_permission.id, None)
            .await
            .expect("Failed to assign create permission to contributor");

        provider
            .assign_permission_to_role(editor_role.id, edit_permission.id, None)
            .await
            .expect("Failed to assign edit permission to editor");

        provider
            .assign_permission_to_role(super_admin_role.id, delete_permission.id, None)
            .await
            .expect("Failed to assign delete permission to super-admin");

        // Create a test user
        let test_user = User {
            id: UserId::new(),
            username: "test_user".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hash".to_string(),
            salt: "salt".to_string(),
            role: UserRole::User,
            status: UserStatus::Active,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_login: None,
            is_active: true,
            is_verified: true,
            failed_login_attempts: 0,
            login_attempts: 0,
            locked_until: None,
            email_verified: true,
            email_verification_token: None,
            password_reset_token: None,
            password_reset_expires: None,
            metadata: serde_json::Value::Null,
        };

        let user_id = provider
            .create_user(&test_user)
            .await
            .expect("Failed to create test user");

        // Assign only the contributor role to the test user
        provider
            .assign_role_to_user(user_id, contributor_role.id, None)
            .await
            .expect("Failed to assign contributor role to user");

        // Test get_role_hierarchy for contributor role
        let hierarchy = provider
            .get_role_hierarchy(contributor_role.id)
            .await
            .expect("Failed to get role hierarchy");

        // Should return contributor role only (no parents)
        assert_eq!(
            hierarchy.len(),
            1,
            "Contributor role hierarchy should contain only itself"
        );
        assert_eq!(hierarchy[0].id, contributor_role.id);
        assert_eq!(hierarchy[0].name, "contributor");

        // Assign only the contributor role to the test user
        provider
            .assign_role_to_user(user_id, super_admin_role.id, None)
            .await
            .expect("Failed to assign contributor role to user");

        // Test get_role_hierarchy for super-admin role
        let super_admin_hierarchy = provider
            .get_role_hierarchy(super_admin_role.id)
            .await
            .expect("Failed to get super-admin role hierarchy");

        // Should return all three roles: super-admin -> editor -> contributor
        assert_eq!(
            super_admin_hierarchy.len(),
            3,
            "Super-admin hierarchy should contain 3 roles"
        );

        // Verify the hierarchy order (should be ordered by depth: super-admin first, then editor, then contributor)
        assert_eq!(super_admin_hierarchy[0].name, "super-admin");
        assert_eq!(super_admin_hierarchy[1].name, "editor");
        assert_eq!(super_admin_hierarchy[2].name, "contributor");

        // Test get_effective_permissions for the test user
        let effective_permissions = provider
            .get_effective_permissions(user_id)
            .await
            .expect("Failed to get effective permissions");

        // User should have all three permissions through inheritance:
        // - create:posts (direct from contributor role)
        // - edit:posts (inherited from editor role through contributor's parent)
        // - delete:posts (inherited from super-admin role through editor's parent)
        assert_eq!(
            effective_permissions.len(),
            3,
            "User should have 3 effective permissions"
        );

        // Verify all expected permissions are present
        let permission_actions: std::collections::HashSet<String> = effective_permissions
            .iter()
            .map(|p| p.action.clone())
            .collect();

        assert!(
            permission_actions.contains("create"),
            "Should have create permission"
        );
        assert!(
            permission_actions.contains("edit"),
            "Should have edit permission"
        );
        assert!(
            permission_actions.contains("delete"),
            "Should have delete permission"
        );

        // Verify all permissions are for the "posts" resource
        for permission in &effective_permissions {
            assert_eq!(
                permission.resource, "posts",
                "All permissions should be for posts resource"
            );
        }

        // Test that the user can perform all actions
        assert!(provider
            .has_permission(user_id, "posts", "create")
            .await
            .expect("Failed to check create permission"));
        assert!(provider
            .has_permission(user_id, "posts", "edit")
            .await
            .expect("Failed to check edit permission"));
        assert!(provider
            .has_permission(user_id, "posts", "delete")
            .await
            .expect("Failed to check delete permission"));

        // Test that the user cannot perform actions they don't have permission for
        assert!(!provider
            .has_permission(user_id, "posts", "admin")
            .await
            .expect("Failed to check admin permission"));
        assert!(!provider
            .has_permission(user_id, "users", "delete")
            .await
            .expect("Failed to check users delete permission"));

        // Clean up test database file if it exists
        if let Some(db_file) = db_file_path {
            let _ = std::fs::remove_file(&db_file);
        }

        println!("Role hierarchy and effective permissions test passed!");
    }
}
