//! Test utilities for database testing
//!
//! This module provides common utilities for creating test databases in temporary directories
//! to ensure tests don't create files in the local directory and properly clean up after themselves.

#[cfg(test)]
pub mod test_helpers {
    use std::path::PathBuf;

    use uuid::Uuid;

    use crate::database::native::DatabaseProvider;

    /// Configuration for test database creation
    #[derive(Debug, Clone)]
    pub struct TestDbConfig {
        /// Name prefix for the test database
        pub name_prefix: String,
        /// Maximum number of connections for the pool
        pub max_connections: u32,
        /// Whether to clean up the database file after use
        pub auto_cleanup: bool,
    }

    impl Default for TestDbConfig {
        fn default() -> Self {
            Self {
                name_prefix: "test".to_string(),
                max_connections: 5,
                auto_cleanup: true,
            }
        }
    }

    /// Test database handle that automatically cleans up the database file when dropped
    pub struct TestDatabase {
        pub provider: DatabaseProvider,
        pub path: Option<PathBuf>,
        auto_cleanup: bool,
    }

    impl TestDatabase {
        /// Create a new in-memory SQLite test database
        pub fn in_memory(max_connections: u32) -> Result<Self, Box<dyn std::error::Error>> {
            let provider = DatabaseProvider::sqlite_memory(max_connections)?;
            Ok(Self {
                provider,
                path: None,
                auto_cleanup: false, // No cleanup needed for in-memory
            })
        }

        /// Create a new file-based SQLite test database in the system temp directory
        pub fn file_based(config: TestDbConfig) -> Result<Self, Box<dyn std::error::Error>> {
            let temp_dir = std::env::temp_dir();
            let db_filename = format!("{}_{}.db", config.name_prefix, Uuid::new_v4());
            let db_path = temp_dir.join(db_filename);

            let provider = DatabaseProvider::sqlite(&db_path, config.max_connections)?;

            Ok(Self {
                provider,
                path: Some(db_path),
                auto_cleanup: config.auto_cleanup,
            })
        }

        /// Create a test database from a URL, automatically using temp directory for file-based databases
        pub fn from_url(
            url: &str,
            max_connections: u32,
        ) -> Result<Self, Box<dyn std::error::Error>> {
            // If it's a local file URL, redirect to temp directory
            if url.starts_with("sqlite://") && !url.contains("memory") && !url.contains(":memory:")
            {
                let temp_dir = std::env::temp_dir();
                let db_filename = format!("test_from_url_{}.db", Uuid::new_v4());
                let db_path = temp_dir.join(db_filename);
                let temp_url = format!("sqlite://{}", db_path.display());

                let provider = DatabaseProvider::from_url(&temp_url, max_connections)?;
                return Ok(Self {
                    provider,
                    path: Some(db_path),
                    auto_cleanup: true,
                });
            }

            // For other URLs (including memory), use as-is
            let provider = DatabaseProvider::from_url(url, max_connections)?;
            Ok(Self {
                provider,
                path: None,
                auto_cleanup: false,
            })
        }

        /// Get the database file path (if any)
        pub fn path(&self) -> Option<&PathBuf> {
            self.path.as_ref()
        }

        /// Disable automatic cleanup (useful for debugging)
        pub fn disable_cleanup(&mut self) {
            self.auto_cleanup = false;
        }

        /// Manually clean up the database file
        pub fn cleanup(&mut self) -> Result<(), std::io::Error> {
            if let Some(path) = &self.path {
                if path.exists() {
                    std::fs::remove_file(path)?;
                }
                self.path = None;
            }
            Ok(())
        }
    }

    impl Drop for TestDatabase {
        fn drop(&mut self) {
            if self.auto_cleanup {
                if let Err(e) = self.cleanup() {
                    eprintln!("Warning: Failed to cleanup test database: {}", e);
                }
            }
        }
    }

    /// Create a quick in-memory test database for simple tests
    pub fn create_memory_db() -> Result<DatabaseProvider, Box<dyn std::error::Error>> {
        Ok(DatabaseProvider::sqlite_memory(5)?)
    }

    /// Create a quick file-based test database in temp directory
    pub fn create_temp_file_db() -> Result<TestDatabase, Box<dyn std::error::Error>> {
        TestDatabase::file_based(TestDbConfig::default())
    }

    /// Create a test database with custom configuration
    pub fn create_test_db(
        config: TestDbConfig,
    ) -> Result<TestDatabase, Box<dyn std::error::Error>> {
        TestDatabase::file_based(config)
    }

    #[cfg(test)]
    mod tests {
        use super::*;
        use crate::database::native::common::traits::{DatabaseConnection, DatabasePool};

        #[tokio::test]
        async fn test_in_memory_database() {
            let test_db = TestDatabase::in_memory(3).expect("Failed to create in-memory database");
            assert_eq!(test_db.provider.database_type(), "sqlite");
            assert_eq!(test_db.provider.max_connections(), 3);
            assert!(test_db.path().is_none());

            // Test that we can get a connection
            let mut conn = test_db
                .provider
                .get_connection()
                .await
                .expect("Failed to get connection");
            conn.ping().await.expect("Ping failed");
        }

        #[tokio::test]
        async fn test_file_based_database() {
            let test_db = create_temp_file_db().expect("Failed to create test database");
            assert_eq!(test_db.provider.database_type(), "sqlite");
            assert_eq!(test_db.provider.max_connections(), 5);

            let path = test_db.path().expect("Expected file path");
            assert!(path.starts_with(std::env::temp_dir()));
            assert!(path.to_string_lossy().contains("test_"));
            assert!(path.to_string_lossy().ends_with(".db"));

            // Test that we can get a connection
            let mut conn = test_db
                .provider
                .get_connection()
                .await
                .expect("Failed to get connection");
            conn.ping().await.expect("Ping failed");
        }

        #[tokio::test]
        async fn test_from_url_memory() {
            let test_db = TestDatabase::from_url("sqlite://memory", 4)
                .expect("Failed to create database from memory URL");
            assert_eq!(test_db.provider.database_type(), "sqlite");
            assert_eq!(test_db.provider.max_connections(), 4);
            assert!(test_db.path().is_none());
        }

        #[tokio::test]
        async fn test_from_url_file_redirected_to_temp() {
            let test_db = TestDatabase::from_url("sqlite://some_local_file.db", 6)
                .expect("Failed to create database from file URL");
            assert_eq!(test_db.provider.database_type(), "sqlite");
            assert_eq!(test_db.provider.max_connections(), 6);

            let path = test_db.path().expect("Expected file path");
            assert!(path.starts_with(std::env::temp_dir()));
            assert!(path.to_string_lossy().contains("test_from_url_"));
            assert!(path.to_string_lossy().ends_with(".db"));
        }

        #[tokio::test]
        async fn test_custom_config() {
            let config = TestDbConfig {
                name_prefix: "custom_test".to_string(),
                max_connections: 10,
                auto_cleanup: true,
            };

            let test_db = create_test_db(config).expect("Failed to create custom test database");
            assert_eq!(test_db.provider.max_connections(), 10);

            let path = test_db.path().expect("Expected file path");
            assert!(path.to_string_lossy().contains("custom_test_"));
        }

        #[tokio::test]
        async fn test_manual_cleanup() {
            let mut test_db = create_temp_file_db().expect("Failed to create test database");
            let _path = test_db.path().unwrap().clone();

            // Ensure file exists (by creating a connection)
            let _conn = test_db
                .provider
                .get_connection()
                .await
                .expect("Failed to get connection");

            // Manually clean up
            test_db.cleanup().expect("Failed to cleanup");
            assert!(test_db.path().is_none());
        }
    }
}
