//! Examples demonstrating how to use the native database providers

#[cfg(test)]
mod examples {
    use chrono::Utc;
    use serde_json::json;
    use uuid::Uuid;

    use super::super::DatabaseProvider;
    use crate::database::native::common::{
        traits::{DatabaseConnection, DatabasePool, DatabaseValue},
        types::FromValue,
    };

    /// Example: Basic CRUD operations with the unified provider
    #[tokio::test]
    async fn example_basic_crud_operations() {
        // Create a provider (auto-detects database type from URL)
        let provider = DatabaseProvider::sqlite_memory(5).expect("Failed to create provider");

        println!("Database type: {}", provider.database_type());
        println!("Max connections: {}", provider.max_connections());

        // Get a connection
        let mut conn = provider
            .get_connection()
            .await
            .expect("Failed to get connection");

        // Create a users table
        conn.execute(
            r#"
            CREATE TABLE users (
                id TEXT PRIMARY KEY,
                username TEXT NOT NULL UNIQUE,
                email TEXT NOT NULL UNIQUE,
                age INTEGER,
                balance REAL,
                is_active INTEGER DEFAULT 1,
                metadata TEXT,
                created_at TEXT NOT NULL
            )
        "#,
            &[],
        )
        .await
        .expect("Failed to create table");

        // Insert a user
        let user_id = Uuid::new_v4();
        let created_at = Utc::now();
        let metadata = json!({"role": "admin", "preferences": {"theme": "dark"}});

        let insert_result = conn
            .execute(
                r#"
            INSERT INTO users (id, username, email, age, balance, is_active, metadata, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        "#,
                &[
                    DatabaseValue::Uuid(user_id),
                    DatabaseValue::Text("john_doe".to_string()),
                    DatabaseValue::Text("<EMAIL>".to_string()),
                    DatabaseValue::I32(30),
                    DatabaseValue::F64(1000.50),
                    DatabaseValue::Bool(true),
                    DatabaseValue::Json(metadata.clone()),
                    DatabaseValue::DateTime(created_at),
                ],
            )
            .await
            .expect("Failed to insert user");

        println!("Inserted {insert_result} row(s)");

        // Query the user
        let row = conn
            .query_one(
                "SELECT * FROM users WHERE username = ?",
                &[DatabaseValue::Text("john_doe".to_string())],
            )
            .await
            .expect("Failed to query user");

        // Extract values with type conversion
        let retrieved_id: Uuid = FromValue::from_value(row.get_by_name("id").unwrap()).unwrap();
        let username: String = FromValue::from_value(row.get_by_name("username").unwrap()).unwrap();
        let email: String = FromValue::from_value(row.get_by_name("email").unwrap()).unwrap();
        let age: i32 = FromValue::from_value(row.get_by_name("age").unwrap()).unwrap();
        let balance: f64 = FromValue::from_value(row.get_by_name("balance").unwrap()).unwrap();
        let is_active: bool = FromValue::from_value(row.get_by_name("is_active").unwrap()).unwrap();
        let retrieved_metadata: serde_json::Value =
            FromValue::from_value(row.get_by_name("metadata").unwrap()).unwrap();

        println!("Retrieved user:");
        println!("  ID: {retrieved_id}");
        println!("  Username: {username}");
        println!("  Email: {email}");
        println!("  Age: {age}");
        println!("  Balance: ${balance:.2}");
        println!("  Active: {is_active}");
        println!("  Metadata: {retrieved_metadata}");

        assert_eq!(retrieved_id, user_id);
        assert_eq!(username, "john_doe");
        assert_eq!(email, "<EMAIL>");
        assert_eq!(age, 30);
        assert_eq!(balance, 1000.50);
        assert!(is_active);
        assert_eq!(retrieved_metadata, metadata);

        // Update the user
        let update_result = conn
            .execute(
                "UPDATE users SET age = ?, balance = ? WHERE id = ?",
                &[
                    DatabaseValue::I32(31),
                    DatabaseValue::F64(1500.75),
                    DatabaseValue::Uuid(user_id),
                ],
            )
            .await
            .expect("Failed to update user");

        println!("Updated {update_result} row(s)");

        // Query all users
        let rows = conn
            .query_all("SELECT username, email FROM users", &[])
            .await
            .expect("Failed to query all users");

        println!("All users:");
        for row in rows {
            let username: String =
                FromValue::from_value(row.get_by_name("username").unwrap()).unwrap();
            let email: String = FromValue::from_value(row.get_by_name("email").unwrap()).unwrap();
            println!("  {username} - {email}");
        }

        // Delete the user
        let delete_result = conn
            .execute(
                "DELETE FROM users WHERE id = ?",
                &[DatabaseValue::Uuid(user_id)],
            )
            .await
            .expect("Failed to delete user");

        println!("Deleted {delete_result} row(s)");
    }

    /// Example: Using transactions for atomic operations
    #[tokio::test]
    async fn example_transaction_usage() {
        let provider = DatabaseProvider::sqlite_memory(5).expect("Failed to create provider");

        let mut conn = provider
            .get_connection()
            .await
            .expect("Failed to get connection");

        // Create accounts table
        conn.execute(
            r#"
            CREATE TABLE accounts (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                balance REAL NOT NULL
            )
        "#,
            &[],
        )
        .await
        .expect("Failed to create table");

        // Insert initial accounts
        let account1_id = Uuid::new_v4();
        let account2_id = Uuid::new_v4();

        conn.execute(
            "INSERT INTO accounts (id, name, balance) VALUES (?, ?, ?)",
            &[
                DatabaseValue::Uuid(account1_id),
                DatabaseValue::Text("Alice".to_string()),
                DatabaseValue::F64(1000.0),
            ],
        )
        .await
        .expect("Failed to insert account 1");

        conn.execute(
            "INSERT INTO accounts (id, name, balance) VALUES (?, ?, ?)",
            &[
                DatabaseValue::Uuid(account2_id),
                DatabaseValue::Text("Bob".to_string()),
                DatabaseValue::F64(500.0),
            ],
        )
        .await
        .expect("Failed to insert account 2");

        println!("Initial balances:");
        let rows = conn
            .query_all("SELECT name, balance FROM accounts ORDER BY name", &[])
            .await
            .expect("Failed to query accounts");
        for row in rows {
            let name: String = FromValue::from_value(row.get_by_name("name").unwrap()).unwrap();
            let balance: f64 = FromValue::from_value(row.get_by_name("balance").unwrap()).unwrap();
            println!("  {name}: ${balance:.2}");
        }

        // Transfer money using a transaction
        let transfer_amount = 200.0;
        println!("\nTransferring ${transfer_amount:.2} from Alice to Bob...");

        let mut tx = conn
            .begin_transaction()
            .await
            .expect("Failed to begin transaction");

        // Debit Alice
        tx.execute(
            "UPDATE accounts SET balance = balance - ? WHERE id = ?",
            &[
                DatabaseValue::F64(transfer_amount),
                DatabaseValue::Uuid(account1_id),
            ],
        )
        .await
        .expect("Failed to debit Alice");

        // Credit Bob
        tx.execute(
            "UPDATE accounts SET balance = balance + ? WHERE id = ?",
            &[
                DatabaseValue::F64(transfer_amount),
                DatabaseValue::Uuid(account2_id),
            ],
        )
        .await
        .expect("Failed to credit Bob");

        // Commit the transaction
        tx.commit().await.expect("Failed to commit transaction");

        println!("Transaction completed successfully!");

        let mut conn = provider
            .get_connection()
            .await
            .expect("Failed to get connection");

        // Verify final balances
        println!("\nFinal balances:");
        let rows = conn
            .query_all("SELECT name, balance FROM accounts ORDER BY name", &[])
            .await
            .expect("Failed to query accounts");
        for row in rows {
            let name: String = FromValue::from_value(row.get_by_name("name").unwrap()).unwrap();
            let balance: f64 = FromValue::from_value(row.get_by_name("balance").unwrap()).unwrap();
            println!("  {name}: ${balance:.2}");
        }

        // Verify the transfer worked
        let alice_row = conn
            .query_one(
                "SELECT balance FROM accounts WHERE id = ?",
                &[DatabaseValue::Uuid(account1_id)],
            )
            .await
            .expect("Failed to query Alice's balance");
        let alice_balance: f64 =
            FromValue::from_value(alice_row.get_by_name("balance").unwrap()).unwrap();

        let bob_row = conn
            .query_one(
                "SELECT balance FROM accounts WHERE id = ?",
                &[DatabaseValue::Uuid(account2_id)],
            )
            .await
            .expect("Failed to query Bob's balance");
        let bob_balance: f64 =
            FromValue::from_value(bob_row.get_by_name("balance").unwrap()).unwrap();

        assert_eq!(alice_balance, 800.0);
        assert_eq!(bob_balance, 700.0);
    }

    /// Example: Working with different database types
    #[tokio::test]
    async fn example_database_type_detection() {
        // Test URL-based database type detection
        let temp_dir = std::env::temp_dir();
        let test_db_file = temp_dir.join(format!("test_{}.db", uuid::Uuid::new_v4()));
        let test_urls = vec![
            (format!("sqlite://{}", test_db_file.display()), "sqlite"),
            ("sqlite://memory".to_string(), "sqlite"),
            (format!("{}", test_db_file.display()), "sqlite"),
            (
                "postgresql://user:pass@localhost/db".to_string(),
                "postgresql",
            ),
            (
                "postgres://user:pass@localhost/db".to_string(),
                "postgresql",
            ),
            ("mysql://user:pass@localhost/db".to_string(), "mysql"),
        ];

        for (url, expected_type) in test_urls {
            match DatabaseProvider::from_url(&url, 5) {
                Ok(provider) => {
                    println!("URL '{}' detected as: {}", url, provider.database_type());
                    assert_eq!(provider.database_type(), expected_type);
                }
                Err(e) => {
                    println!("URL '{url}' failed (expected for non-SQLite): {e}");
                    // Non-SQLite URLs will fail due to connection issues, but type detection works
                }
            }
        }

        // Test direct provider creation
        let sqlite_provider =
            DatabaseProvider::sqlite_memory(5).expect("Failed to create SQLite provider");
        assert_eq!(sqlite_provider.database_type(), "sqlite");
        println!("SQLite provider created successfully");

        // Test provider capabilities
        println!("Provider info:");
        println!("  Type: {}", sqlite_provider.database_type());
        println!("  Max connections: {}", sqlite_provider.max_connections());
        println!(
            "  Active connections: {}",
            sqlite_provider.active_connections()
        );
        println!("  Idle connections: {}", sqlite_provider.idle_connections());

        // Test health check
        sqlite_provider
            .health_check()
            .await
            .expect("Health check failed");
        println!("  Health check: ✓");
    }

    /// Example: Advanced type conversions and JSON handling
    #[tokio::test]
    async fn example_advanced_type_handling() {
        let provider = DatabaseProvider::sqlite_memory(5).expect("Failed to create provider");

        let mut conn = provider
            .get_connection()
            .await
            .expect("Failed to get connection");

        // Create a table with various data types
        conn.execute(
            r#"
            CREATE TABLE data_types_demo (
                id INTEGER PRIMARY KEY,
                uuid_field TEXT,
                datetime_field TEXT,
                json_field TEXT,
                bool_field INTEGER,
                float_field REAL,
                text_field TEXT,
                blob_field BLOB
            )
        "#,
            &[],
        )
        .await
        .expect("Failed to create table");

        // Insert data with various types
        let test_uuid = Uuid::new_v4();
        let test_datetime = Utc::now();
        let test_json = json!({
            "user": {
                "name": "John Doe",
                "age": 30,
                "preferences": {
                    "theme": "dark",
                    "notifications": true,
                    "languages": ["en", "es", "fr"]
                }
            },
            "metadata": {
                "created": "2024-01-01T00:00:00Z",
                "version": 1.0
            }
        });

        conn.execute(r#"
            INSERT INTO data_types_demo 
            (uuid_field, datetime_field, json_field, bool_field, float_field, text_field, blob_field)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        "#, &[
            DatabaseValue::Uuid(test_uuid),
            DatabaseValue::DateTime(test_datetime),
            DatabaseValue::Json(test_json.clone()),
            DatabaseValue::Bool(true),
            DatabaseValue::F64(3.14159),
            DatabaseValue::Text("Hello, World! 🌍".to_string()),
            DatabaseValue::Bytes(b"Binary data \x00\x01\x02\x03".to_vec()),
        ]).await.expect("Failed to insert data");

        // Query and convert back
        let row = conn
            .query_one("SELECT * FROM data_types_demo WHERE id = 1", &[])
            .await
            .expect("Failed to query data");

        // Extract and verify each type
        let retrieved_uuid: Uuid =
            FromValue::from_value(row.get_by_name("uuid_field").unwrap()).unwrap();
        let retrieved_datetime: chrono::DateTime<Utc> =
            FromValue::from_value(row.get_by_name("datetime_field").unwrap()).unwrap();
        let retrieved_json: serde_json::Value =
            FromValue::from_value(row.get_by_name("json_field").unwrap()).unwrap();
        let retrieved_bool: bool =
            FromValue::from_value(row.get_by_name("bool_field").unwrap()).unwrap();
        let retrieved_float: f64 =
            FromValue::from_value(row.get_by_name("float_field").unwrap()).unwrap();
        let retrieved_text: String =
            FromValue::from_value(row.get_by_name("text_field").unwrap()).unwrap();
        let retrieved_blob: Vec<u8> =
            FromValue::from_value(row.get_by_name("blob_field").unwrap()).unwrap();

        println!("Type conversion results:");
        println!("  UUID: {test_uuid} -> {retrieved_uuid}");
        println!(
            "  DateTime: {} -> {}",
            test_datetime.to_rfc3339(),
            retrieved_datetime.to_rfc3339()
        );
        println!("  JSON: {} characters", retrieved_json.to_string().len());
        println!("  Bool: {retrieved_bool}");
        println!("  Float: {retrieved_float:.5}");
        println!("  Text: {retrieved_text}");
        println!("  Blob: {} bytes", retrieved_blob.len());

        // Verify conversions
        assert_eq!(retrieved_uuid, test_uuid);
        assert_eq!(retrieved_datetime.timestamp(), test_datetime.timestamp());
        assert_eq!(retrieved_json, test_json);
        assert!(retrieved_bool);
        assert!((retrieved_float - 3.14159).abs() < f64::EPSILON);
        assert_eq!(retrieved_text, "Hello, World! 🌍");
        assert_eq!(retrieved_blob, b"Binary data \x00\x01\x02\x03");

        // Demonstrate JSON querying (SQLite JSON1 extension would be needed for real JSON queries)
        println!("\nJSON content:");
        if let serde_json::Value::Object(obj) = &retrieved_json {
            if let Some(user) = obj.get("user") {
                if let Some(name) = user.get("name") {
                    println!("  User name: {name}");
                }
                if let Some(preferences) = user.get("preferences") {
                    if let Some(languages) = preferences.get("languages") {
                        println!("  Languages: {languages}");
                    }
                }
            }
        }
    }
}
