//! src/database/native/integration_tests/table_prefix_tests.rs

use crate::database::factory::DatabaseProviderFactory;
use crate::indidus_auth::IndidusAuth;
use crate::types::{Credentials, Permission, Role, User};
use std::sync::Arc;

async fn setup_auth_with_prefix(
    provider_type: &str,
    prefix: &str,
) -> Result<IndidusAuth, Box<dyn std::error::Error>> {
    let db_url = match provider_type {
        "sqlite" => "sqlite::memory:",
        "postgres" => "postgres://user:password@localhost/test_db",
        "mysql" => "mysql://user:password@localhost/test_db",
        _ => panic!("Unsupported provider"),
    };

    let provider =
        DatabaseProviderFactory::create_provider(db_url, Some(prefix.to_string())).await?;
    let auth = IndidusAuth::new_with_provider(Arc::new(provider)).await?;
    auth.initialize().await?;
    Ok(auth)
}

#[tokio::test]
async fn test_user_lifecycle_with_table_prefix_sqlite() {
    let auth = setup_auth_with_prefix("sqlite", "indidus_test_")
        .await
        .unwrap();
    test_user_lifecycle(auth).await;
}

#[tokio::test]
#[cfg(feature = "postgres-native")]
async fn test_user_lifecycle_with_table_prefix_postgres() {
    let auth = setup_auth_with_prefix("postgres", "indidus_test_")
        .await
        .unwrap();
    test_user_lifecycle(auth).await;
}

#[tokio::test]
#[cfg(feature = "mysql-native")]
async fn test_user_lifecycle_with_table_prefix_mysql() {
    let auth = setup_auth_with_prefix("mysql", "indidus_test_")
        .await
        .unwrap();
    test_user_lifecycle(auth).await;
}

async fn test_user_lifecycle(auth: IndidusAuth) {
    // 1. Register a new user
    let user_id = auth
        .register("test_user", "<EMAIL>", "password123")
        .await
        .unwrap();

    // 2. Login with the new user
    let session = auth.login("test_user", "password123").await.unwrap();
    assert_eq!(session.user_id, user_id);

    // 3. RBAC Setup
    let role = auth.create_role("admin").await.unwrap();
    let permission = auth
        .create_permission("read:users")
        .resource("users")
        .action("read")
        .await
        .unwrap();

    auth.assign_permission_to_role(permission.id, role.id)
        .await
        .unwrap();
    auth.assign_role_to_user(user_id, role.id).await.unwrap();

    // 4. Permission Verification
    let has_permission = auth
        .check_permission(user_id, "users", "read")
        .await
        .unwrap();
    assert!(has_permission);
}
