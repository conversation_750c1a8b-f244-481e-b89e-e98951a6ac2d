//! Unified database provider interface
//!
//! This module provides a unified interface for accessing different database implementations
//! through a single DatabaseProvider enum that can be configured at runtime.

use std::fmt;

use async_trait::async_trait;

use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseConnection, DatabasePool, DatabaseRow, DatabaseTransaction, DatabaseValue},
};
#[cfg(feature = "mysql-native")]
use crate::database::native::mysql::{MySqlConnectionWrapper, MySqlPool};
#[cfg(feature = "postgres-native")]
use crate::database::native::postgres::{PostgresConnectionWrapper, PostgresPool};
#[cfg(feature = "sqlite-native")]
use crate::database::native::sqlite::{SqliteConnectionWrapper, SqlitePool};

/// Unified database provider that can work with any of the supported databases
#[derive(Clone)]
pub enum DatabaseProvider {
    #[cfg(feature = "sqlite-native")]
    SQLite(SqlitePool),

    #[cfg(feature = "postgres-native")]
    PostgreSQL(PostgresPool),

    #[cfg(feature = "mysql-native")]
    MySQL(MySqlPool),

    // Fallback variant when no native features are enabled
    #[cfg(not(any(
        feature = "sqlite-native",
        feature = "postgres-native",
        feature = "mysql-native"
    )))]
    #[doc(hidden)]
    _Phantom(std::marker::PhantomData<()>),
}

/// Unified connection wrapper that can hold any database connection
pub enum UnifiedConnection {
    #[cfg(feature = "sqlite-native")]
    SQLite(SqliteConnectionWrapper),

    #[cfg(feature = "postgres-native")]
    PostgreSQL(Box<PostgresConnectionWrapper>),

    #[cfg(feature = "mysql-native")]
    MySQL(MySqlConnectionWrapper),

    // Fallback variant when no native features are enabled
    #[cfg(not(any(
        feature = "sqlite-native",
        feature = "postgres-native",
        feature = "mysql-native"
    )))]
    #[doc(hidden)]
    _Phantom(std::marker::PhantomData<()>),
}

impl DatabaseProvider {
    /// Create a new SQLite provider
    #[cfg(feature = "sqlite-native")]
    pub fn sqlite<P: AsRef<std::path::Path>>(
        database_path: P,
        max_connections: u32,
    ) -> Result<Self, DatabaseError> {
        let pool = SqlitePool::new(database_path, max_connections)?;
        Ok(DatabaseProvider::SQLite(pool))
    }

    /// Create a new in-memory SQLite provider
    #[cfg(feature = "sqlite-native")]
    pub fn sqlite_memory(max_connections: u32) -> Result<Self, DatabaseError> {
        let pool = SqlitePool::in_memory(max_connections)?;
        Ok(DatabaseProvider::SQLite(pool))
    }

    /// Create a new PostgreSQL provider
    #[cfg(feature = "postgres-native")]
    pub fn postgresql(database_url: &str, max_connections: u32) -> Result<Self, DatabaseError> {
        let pool = PostgresPool::new(database_url, max_connections)?;
        Ok(DatabaseProvider::PostgreSQL(pool))
    }

    /// Create a new MySQL provider
    #[cfg(feature = "mysql-native")]
    pub fn mysql(database_url: &str, max_connections: u32) -> Result<Self, DatabaseError> {
        let pool = MySqlPool::new(database_url, max_connections)?;
        Ok(DatabaseProvider::MySQL(pool))
    }

    /// Create a provider from a database URL (auto-detects the database type)
    pub fn from_url(database_url: &str, _max_connections: u32) -> Result<Self, DatabaseError> {
        if database_url.starts_with("sqlite://")
            || database_url.ends_with(".db")
            || database_url.ends_with(".sqlite")
        {
            #[cfg(feature = "sqlite-native")]
            {
                // Handle special case for in-memory database
                if database_url == "sqlite://memory" || database_url == "sqlite://:memory:" {
                    return Self::sqlite_memory(_max_connections);
                }

                let path = database_url
                    .strip_prefix("sqlite://")
                    .unwrap_or(database_url);
                return Self::sqlite(path, _max_connections);
            }
            #[cfg(not(feature = "sqlite-native"))]
            return Err(DatabaseError::ConnectionError(
                "SQLite support not enabled".to_string(),
            ));
        }

        if database_url.starts_with("postgresql://") || database_url.starts_with("postgres://") {
            #[cfg(feature = "postgres-native")]
            return Self::postgresql(database_url, _max_connections);
            #[cfg(not(feature = "postgres-native"))]
            return Err(DatabaseError::ConnectionError(
                "PostgreSQL support not enabled".to_string(),
            ));
        }

        if database_url.starts_with("mysql://") {
            #[cfg(feature = "mysql-native")]
            return Self::mysql(database_url, _max_connections);
            #[cfg(not(feature = "mysql-native"))]
            return Err(DatabaseError::ConnectionError(
                "MySQL support not enabled".to_string(),
            ));
        }

        Err(DatabaseError::ConnectionError(format!(
            "Unsupported database URL format: {database_url}"
        )))
    }

    /// Get the database type as a string
    pub fn database_type(&self) -> &'static str {
        match self {
            #[cfg(feature = "sqlite-native")]
            DatabaseProvider::SQLite(_) => "sqlite",
            #[cfg(feature = "postgres-native")]
            DatabaseProvider::PostgreSQL(_) => "postgresql",
            #[cfg(feature = "mysql-native")]
            DatabaseProvider::MySQL(_) => "mysql",
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            DatabaseProvider::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }
}

#[async_trait]
impl DatabasePool for DatabaseProvider {
    type Connection = UnifiedConnection;

    async fn get_connection(&self) -> Result<Self::Connection, DatabaseError> {
        match self {
            #[cfg(feature = "sqlite-native")]
            DatabaseProvider::SQLite(pool) => {
                let conn = pool.get_connection().await?;
                Ok(UnifiedConnection::SQLite(conn))
            }
            #[cfg(feature = "postgres-native")]
            DatabaseProvider::PostgreSQL(pool) => {
                let conn = pool.get_connection().await?;
                Ok(UnifiedConnection::PostgreSQL(Box::new(conn)))
            }
            #[cfg(feature = "mysql-native")]
            DatabaseProvider::MySQL(pool) => {
                let conn = pool.get_connection().await?;
                Ok(UnifiedConnection::MySQL(conn))
            }
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            DatabaseProvider::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    async fn close(&self) -> Result<(), DatabaseError> {
        match self {
            #[cfg(feature = "sqlite-native")]
            DatabaseProvider::SQLite(pool) => pool.close().await,
            #[cfg(feature = "postgres-native")]
            DatabaseProvider::PostgreSQL(pool) => pool.close().await,
            #[cfg(feature = "mysql-native")]
            DatabaseProvider::MySQL(pool) => pool.close().await,
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            DatabaseProvider::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    fn max_connections(&self) -> u32 {
        match self {
            #[cfg(feature = "sqlite-native")]
            DatabaseProvider::SQLite(pool) => pool.max_connections(),
            #[cfg(feature = "postgres-native")]
            DatabaseProvider::PostgreSQL(pool) => pool.max_connections(),
            #[cfg(feature = "mysql-native")]
            DatabaseProvider::MySQL(pool) => pool.max_connections(),
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            DatabaseProvider::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    fn active_connections(&self) -> u32 {
        match self {
            #[cfg(feature = "sqlite-native")]
            DatabaseProvider::SQLite(pool) => pool.active_connections(),
            #[cfg(feature = "postgres-native")]
            DatabaseProvider::PostgreSQL(pool) => pool.active_connections(),
            #[cfg(feature = "mysql-native")]
            DatabaseProvider::MySQL(pool) => pool.active_connections(),
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            DatabaseProvider::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    fn idle_connections(&self) -> u32 {
        match self {
            #[cfg(feature = "sqlite-native")]
            DatabaseProvider::SQLite(pool) => pool.idle_connections(),
            #[cfg(feature = "postgres-native")]
            DatabaseProvider::PostgreSQL(pool) => pool.idle_connections(),
            #[cfg(feature = "mysql-native")]
            DatabaseProvider::MySQL(pool) => pool.idle_connections(),
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            DatabaseProvider::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    async fn health_check(&self) -> Result<(), DatabaseError> {
        match self {
            #[cfg(feature = "sqlite-native")]
            DatabaseProvider::SQLite(pool) => pool.health_check().await,
            #[cfg(feature = "postgres-native")]
            DatabaseProvider::PostgreSQL(pool) => pool.health_check().await,
            #[cfg(feature = "mysql-native")]
            DatabaseProvider::MySQL(pool) => pool.health_check().await,
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            DatabaseProvider::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }
}

#[async_trait]
impl DatabaseConnection for UnifiedConnection {
    async fn execute(
        &mut self,
        _query: &str,
        _params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError> {
        match self {
            #[cfg(feature = "sqlite-native")]
            UnifiedConnection::SQLite(conn) => conn.execute(_query, _params).await,
            #[cfg(feature = "postgres-native")]
            UnifiedConnection::PostgreSQL(conn) => conn.execute(_query, _params).await,
            #[cfg(feature = "mysql-native")]
            UnifiedConnection::MySQL(conn) => conn.execute(_query, _params).await,
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            UnifiedConnection::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    async fn query_one(
        &mut self,
        _query: &str,
        _params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError> {
        match self {
            #[cfg(feature = "sqlite-native")]
            UnifiedConnection::SQLite(conn) => conn.query_one(_query, _params).await,
            #[cfg(feature = "postgres-native")]
            UnifiedConnection::PostgreSQL(conn) => conn.query_one(_query, _params).await,
            #[cfg(feature = "mysql-native")]
            UnifiedConnection::MySQL(conn) => conn.query_one(_query, _params).await,
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            UnifiedConnection::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    async fn query_all(
        &mut self,
        _query: &str,
        _params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError> {
        match self {
            #[cfg(feature = "sqlite-native")]
            UnifiedConnection::SQLite(conn) => conn.query_all(_query, _params).await,
            #[cfg(feature = "postgres-native")]
            UnifiedConnection::PostgreSQL(conn) => conn.query_all(_query, _params).await,
            #[cfg(feature = "mysql-native")]
            UnifiedConnection::MySQL(conn) => conn.query_all(_query, _params).await,
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            UnifiedConnection::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    async fn begin_transaction(&mut self) -> Result<Box<dyn DatabaseTransaction>, DatabaseError> {
        match self {
            #[cfg(feature = "sqlite-native")]
            UnifiedConnection::SQLite(conn) => conn.begin_transaction().await,
            #[cfg(feature = "postgres-native")]
            UnifiedConnection::PostgreSQL(conn) => conn.begin_transaction().await,
            #[cfg(feature = "mysql-native")]
            UnifiedConnection::MySQL(conn) => conn.begin_transaction().await,
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            UnifiedConnection::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    async fn ping(&mut self) -> Result<(), DatabaseError> {
        match self {
            #[cfg(feature = "sqlite-native")]
            UnifiedConnection::SQLite(conn) => conn.ping().await,
            #[cfg(feature = "postgres-native")]
            UnifiedConnection::PostgreSQL(conn) => conn.ping().await,
            #[cfg(feature = "mysql-native")]
            UnifiedConnection::MySQL(conn) => conn.ping().await,
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            UnifiedConnection::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }

    fn is_async_native(&self) -> bool {
        match self {
            #[cfg(feature = "sqlite-native")]
            UnifiedConnection::SQLite(conn) => conn.is_async_native(),
            #[cfg(feature = "postgres-native")]
            UnifiedConnection::PostgreSQL(conn) => conn.is_async_native(),
            #[cfg(feature = "mysql-native")]
            UnifiedConnection::MySQL(conn) => conn.is_async_native(),
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            UnifiedConnection::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }
}

impl UnifiedConnection {
    /// Get the database type as a string
    pub fn database_type(&self) -> &'static str {
        match self {
            #[cfg(feature = "sqlite-native")]
            UnifiedConnection::SQLite(_) => "sqlite",
            #[cfg(feature = "postgres-native")]
            UnifiedConnection::PostgreSQL(_) => "postgresql",
            #[cfg(feature = "mysql-native")]
            UnifiedConnection::MySQL(_) => "mysql",
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            UnifiedConnection::_Phantom(_) => unreachable!("No native database features enabled"),
        }
    }
}

impl fmt::Debug for DatabaseProvider {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            #[cfg(feature = "sqlite-native")]
            DatabaseProvider::SQLite(_) => write!(f, "DatabaseProvider::SQLite"),
            #[cfg(feature = "postgres-native")]
            DatabaseProvider::PostgreSQL(_) => write!(f, "DatabaseProvider::PostgreSQL"),
            #[cfg(feature = "mysql-native")]
            DatabaseProvider::MySQL(_) => write!(f, "DatabaseProvider::MySQL"),
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            DatabaseProvider::_Phantom(_) => write!(f, "DatabaseProvider::_Phantom"),
        }
    }
}

impl fmt::Debug for UnifiedConnection {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            #[cfg(feature = "sqlite-native")]
            UnifiedConnection::SQLite(_) => write!(f, "UnifiedConnection::SQLite"),
            #[cfg(feature = "postgres-native")]
            UnifiedConnection::PostgreSQL(_) => write!(f, "UnifiedConnection::PostgreSQL"),
            #[cfg(feature = "mysql-native")]
            UnifiedConnection::MySQL(_) => write!(f, "UnifiedConnection::MySQL"),
            #[cfg(not(any(
                feature = "sqlite-native",
                feature = "postgres-native",
                feature = "mysql-native"
            )))]
            UnifiedConnection::_Phantom(_) => write!(f, "UnifiedConnection::_Phantom"),
        }
    }
}
