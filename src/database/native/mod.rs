//! Native database drivers implementation
//!
//! This module provides native database driver implementations for:
//! - SQLite (rusqlite + r2d2) - Synchronous
//! - PostgreSQL (tokio-postgres + deadpool-postgres) - Async
//! - MySQL (mysql_async + deadpool) - Async

pub mod common;
pub mod provider;

#[cfg(feature = "sqlite-native")]
pub mod sqlite;

#[cfg(feature = "postgres-native")]
pub mod postgres;

#[cfg(feature = "mysql-native")]
pub mod mysql;

#[cfg(test)]
pub mod integration_tests;

#[cfg(test)]
pub mod benchmarks;

#[cfg(test)]
pub mod examples;

#[cfg(test)]
pub mod auth_integration_example;

#[cfg(test)]
pub mod test_utils;

// Re-export common types and traits
pub use common::*;
pub use provider::*;
