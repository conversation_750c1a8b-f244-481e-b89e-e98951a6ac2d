//! MySQL row implementation for DatabaseRow trait

use std::{borrow::Cow, collections::HashMap};

use chrono::{NaiveDate, NaiveDateTime};
use mysql_async::{Row as MySqlRow, Value as MySqlValue};
use serde_json::Value as JsonValue;
use uuid::Uuid;

use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseRow, DatabaseValue},
};

pub struct MySqlRowWrapper {
    values: Vec<DatabaseValue>,
    column_names: Vec<String>,
    column_map: HashMap<String, usize>,
}

impl MySqlRowWrapper {
    pub fn new(row: MySqlRow) -> Result<Self, DatabaseError> {
        let column_count = row.len();
        let mut column_names = Vec::with_capacity(column_count);
        let mut column_map = HashMap::new();
        let mut values = Vec::with_capacity(column_count);

        // Get column names and build the map
        for i in 0..column_count {
            let name = row
                .columns_ref()
                .get(i)
                .ok_or_else(|| DatabaseError::QueryError(format!("Invalid column index: {}", i)))?
                .name_str()
                .to_string();
            column_map.insert(name.clone(), i);
            column_names.push(name);
        }

        // Extract all values
        for i in 0..column_count {
            let mysql_value = row
                .as_ref(i)
                .ok_or_else(|| DatabaseError::QueryError(format!("Invalid column index: {}", i)))?;

            let db_value = convert_mysql_value_to_database_value(mysql_value)?;
            values.push(db_value);
        }

        Ok(MySqlRowWrapper {
            values,
            column_names,
            column_map,
        })
    }
}

impl DatabaseRow for MySqlRowWrapper {
    fn get_by_index(&self, index: usize) -> Result<DatabaseValue, DatabaseError> {
        self.values.get(index).cloned().ok_or_else(|| {
            DatabaseError::QueryError(format!("Column index {} out of bounds", index))
        })
    }

    fn get_by_name(&self, name: &str) -> Result<DatabaseValue, DatabaseError> {
        let index = self
            .column_map
            .get(name)
            .ok_or_else(|| DatabaseError::QueryError(format!("Column '{}' not found", name)))?;
        self.get_by_index(*index)
    }

    fn column_count(&self) -> usize {
        self.column_names.len()
    }

    fn column_names(&self) -> Cow<'_, [String]> {
        Cow::Borrowed(&self.column_names)
    }
}

impl std::fmt::Debug for MySqlRowWrapper {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("MySqlRowWrapper")
            .field("column_names", &self.column_names)
            .field("values", &self.values)
            .finish()
    }
}

fn convert_mysql_value_to_database_value(
    value: &MySqlValue,
) -> Result<DatabaseValue, DatabaseError> {
    match value {
        MySqlValue::NULL => Ok(DatabaseValue::Null),

        // Direct type mappings (more efficient and type-safe)
        MySqlValue::Int(i) => Ok(DatabaseValue::I64(*i)),
        MySqlValue::UInt(u) => Ok(DatabaseValue::I64(*u as i64)),
        MySqlValue::Float(f) => Ok(DatabaseValue::F64(*f as f64)),
        MySqlValue::Double(d) => Ok(DatabaseValue::F64(*d)),

        // Bytes - handle with better type detection
        MySqlValue::Bytes(bytes) => convert_mysql_bytes_to_database_value(bytes),

        MySqlValue::Date(year, month, day, hour, minute, second, _micro) => {
            // Convert MySQL date to DateTime
            let naive_date = NaiveDate::from_ymd_opt(*year as i32, *month as u32, *day as u32)
                .ok_or_else(|| {
                    DatabaseError::TypeConversionError("Invalid MySQL date".to_string())
                })?;
            let naive_dt = naive_date
                .and_hms_opt(*hour as u32, *minute as u32, *second as u32)
                .ok_or_else(|| {
                    DatabaseError::TypeConversionError("Invalid MySQL time".to_string())
                })?;
            Ok(DatabaseValue::DateTime(naive_dt.and_utc()))
        }

        MySqlValue::Time(neg, days, hours, minutes, seconds, _micros) => {
            // Convert MySQL time to a string representation
            let sign = if *neg { "-" } else { "" };
            let time_str = format!(
                "{}{:02}:{:02}:{:02}",
                sign,
                days * 24 + *hours as u32,
                minutes,
                seconds
            );
            Ok(DatabaseValue::Text(time_str))
        }
    }
}

fn convert_mysql_bytes_to_database_value(bytes: &[u8]) -> Result<DatabaseValue, DatabaseError> {
    // First try to convert to UTF-8 string
    let s = match String::from_utf8(bytes.to_vec()) {
        Ok(s) => s,
        Err(_) => {
            // If it's not valid UTF-8, treat as binary data
            return Ok(DatabaseValue::Bytes(bytes.to_vec()));
        }
    };

    // If the string is empty, return it as text
    if s.is_empty() {
        return Ok(DatabaseValue::Text(s));
    }

    // Try to parse as different types in order of likelihood

    // 1. Try integer (most common for numeric columns stored as text)
    if let Ok(i) = s.parse::<i64>() {
        return Ok(DatabaseValue::I64(i));
    }

    // 2. Try float
    if let Ok(f) = s.parse::<f64>() {
        return Ok(DatabaseValue::F64(f));
    }

    // 3. Try boolean (MySQL often stores as "0"/"1" or "true"/"false")
    match s.to_lowercase().as_str() {
        "true" | "1" => return Ok(DatabaseValue::Bool(true)),
        "false" | "0" => return Ok(DatabaseValue::Bool(false)),
        _ => {}
    }

    // 4. Try UUID (check format first to avoid expensive parsing)
    if s.len() == 36 && s.chars().nth(8) == Some('-') && s.chars().nth(13) == Some('-') {
        if let Ok(uuid) = Uuid::parse_str(&s) {
            return Ok(DatabaseValue::Uuid(uuid));
        }
    }

    // 5. Try DateTime (MySQL format: YYYY-MM-DD HH:MM:SS)
    if s.len() >= 19 && s.chars().nth(4) == Some('-') && s.chars().nth(7) == Some('-') {
        if let Ok(dt) = NaiveDateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S") {
            return Ok(DatabaseValue::DateTime(dt.and_utc()));
        }
        // Try date only format
        if let Ok(date) = NaiveDate::parse_from_str(&s, "%Y-%m-%d") {
            return Ok(DatabaseValue::DateTime(
                date.and_hms_opt(0, 0, 0).unwrap().and_utc(),
            ));
        }
    }

    // 6. Try JSON (check for JSON-like structure)
    if (s.starts_with('{') && s.ends_with('}')) || (s.starts_with('[') && s.ends_with(']')) {
        if let Ok(json) = serde_json::from_str::<JsonValue>(&s) {
            return Ok(DatabaseValue::Json(json));
        }
    }

    // 7. Default to text
    Ok(DatabaseValue::Text(s))
}
