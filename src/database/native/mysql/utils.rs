//! MySQL utility functions

use mysql_async::Value as MySqlValue;

use crate::database::native::common::{errors::DatabaseError, traits::DatabaseValue};

/// Convert a DatabaseValue to a MySQL value
pub fn convert_database_value_to_mysql(value: DatabaseValue) -> Result<MySqlValue, DatabaseError> {
    match value {
        DatabaseValue::Null => Ok(MySqlValue::NULL),
        DatabaseValue::Bool(b) => Ok(MySqlValue::Int(if b { 1 } else { 0 })),
        DatabaseValue::I32(i) => Ok(MySqlValue::Int(i as i64)),
        DatabaseValue::I64(i) => Ok(MySqlValue::Int(i)),
        DatabaseValue::F64(f) => Ok(MySqlValue::Double(f)),
        DatabaseValue::Text(s) => Ok(MySqlValue::Bytes(s.into_bytes())),
        DatabaseValue::Bytes(b) => Ok(MySqlValue::Bytes(b)),
        DatabaseValue::Uuid(u) => Ok(MySqlValue::Bytes(u.to_string().into_bytes())),
        DatabaseValue::DateTime(dt) => Ok(MySqlValue::Bytes(dt.to_rfc3339().into_bytes())),
        DatabaseValue::Json(j) => {
            let json_str = serde_json::to_string(&j).map_err(|e| {
                DatabaseError::TypeConversionError(format!("JSON serialization error: {}", e))
            })?;
            Ok(MySqlValue::Bytes(json_str.into_bytes()))
        }
        DatabaseValue::BytesZeroCopy(b) => Ok(MySqlValue::Bytes(b.to_vec())),
        DatabaseValue::JsonZeroCopy(b) => Ok(MySqlValue::Bytes(b.to_vec())),
    }
}
