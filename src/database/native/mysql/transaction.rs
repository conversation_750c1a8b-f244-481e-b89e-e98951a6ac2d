//! MySQL transaction implementation

use std::sync::Arc;

use async_trait::async_trait;
use mysql_async::{prelude::Queryable, Conn, TxOpts, Value as MySqlValue};
use tokio::sync::Mutex;

use super::{row::MySqlRowWrapper, utils::convert_database_value_to_mysql};
use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseRow, DatabaseTransaction, DatabaseValue},
};

pub enum MySqlTransactionType {
    // For owned connections (existing)
    Owned(Arc<Mutex<Conn>>),
    // For pooled connections (NEW) - takes full ownership
    Pooled(deadpool::managed::Object<super::pool::MySqlConnectionManager>),
}

pub struct MySqlTransactionWrapper {
    connection: Option<MySqlTransactionType>,
    is_committed: bool,
    is_rolled_back: bool,
}

impl MySqlTransactionWrapper {
    pub async fn new(connection: Arc<Mutex<Conn>>) -> Result<Self, DatabaseError> {
        // Begin transaction on owned connection
        {
            let mut conn = connection.lock().await;
            conn.start_transaction(TxOpts::default())
                .await
                .map_err(|e| {
                    DatabaseError::TransactionError(format!("Failed to begin transaction: {}", e))
                })?;
        }

        Ok(Self {
            connection: Some(MySqlTransactionType::Owned(connection)),
            is_committed: false,
            is_rolled_back: false,
        })
    }

    // NEW constructor for pooled connections - takes full ownership
    pub async fn new_pooled(
        mut pooled_conn: deadpool::managed::Object<super::pool::MySqlConnectionManager>,
    ) -> Result<Self, DatabaseError> {
        // Begin transaction on pooled connection
        pooled_conn
            .start_transaction(TxOpts::default())
            .await
            .map_err(|e| {
                DatabaseError::TransactionError(format!(
                    "Failed to begin pooled transaction: {}",
                    e
                ))
            })?;

        Ok(Self {
            connection: Some(MySqlTransactionType::Pooled(pooled_conn)),
            is_committed: false,
            is_rolled_back: false,
        })
    }

    fn convert_params(params: &[DatabaseValue]) -> Result<Vec<MySqlValue>, DatabaseError> {
        params
            .iter()
            .map(|param| convert_database_value_to_mysql(param.clone()))
            .collect()
    }
}

#[async_trait]
impl DatabaseTransaction for MySqlTransactionWrapper {
    async fn execute(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let mysql_params = Self::convert_params(params)?;

        match &mut self.connection {
            Some(MySqlTransactionType::Owned(conn_arc)) => {
                let mut conn = conn_arc.lock().await;
                let _result = conn.exec_drop(query, mysql_params).await.map_err(|e| {
                    DatabaseError::QueryError(format!("MySQL execute error: {}", e))
                })?;
                Ok(0) // MySQL exec_drop doesn't return affected rows directly
            }
            Some(MySqlTransactionType::Pooled(conn)) => {
                let _result = conn.exec_drop(query, mysql_params).await.map_err(|e| {
                    DatabaseError::QueryError(format!("MySQL execute error: {}", e))
                })?;
                Ok(0) // MySQL exec_drop doesn't return affected rows directly
            }
            None => Err(DatabaseError::TransactionError(
                "Transaction already completed".to_string(),
            )),
        }
    }

    async fn query_one(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let mysql_params = Self::convert_params(params)?;

        match &mut self.connection {
            Some(MySqlTransactionType::Owned(conn_arc)) => {
                let mut conn = conn_arc.lock().await;
                let mut result = conn
                    .exec_iter(query, mysql_params)
                    .await
                    .map_err(|e| DatabaseError::QueryError(format!("MySQL query error: {}", e)))?;

                match result.next().await {
                    Ok(Some(row)) => {
                        let wrapped_row = MySqlRowWrapper::new(row)?;
                        Ok(Box::new(wrapped_row))
                    }
                    Ok(None) => Err(DatabaseError::NotFound),
                    Err(e) => Err(DatabaseError::QueryError(format!("MySQL row error: {}", e))),
                }
            }
            Some(MySqlTransactionType::Pooled(conn)) => {
                let mut result = conn
                    .exec_iter(query, mysql_params)
                    .await
                    .map_err(|e| DatabaseError::QueryError(format!("MySQL query error: {}", e)))?;

                match result.next().await {
                    Ok(Some(row)) => {
                        let wrapped_row = MySqlRowWrapper::new(row)?;
                        Ok(Box::new(wrapped_row))
                    }
                    Ok(None) => Err(DatabaseError::NotFound),
                    Err(e) => Err(DatabaseError::QueryError(format!("MySQL row error: {}", e))),
                }
            }
            None => Err(DatabaseError::TransactionError(
                "Transaction already completed".to_string(),
            )),
        }
    }

    async fn query_all(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError> {
        if self.is_committed || self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already finished".to_string(),
            ));
        }

        let mysql_params = Self::convert_params(params)?;

        let result = match &mut self.connection {
            Some(MySqlTransactionType::Owned(conn_arc)) => {
                let mut conn = conn_arc.lock().await;
                conn.exec(query, mysql_params)
                    .await
                    .map_err(|e| DatabaseError::QueryError(format!("MySQL query error: {}", e)))?
            }
            Some(MySqlTransactionType::Pooled(conn)) => conn
                .exec(query, mysql_params)
                .await
                .map_err(|e| DatabaseError::QueryError(format!("MySQL query error: {}", e)))?,
            None => {
                return Err(DatabaseError::TransactionError(
                    "Transaction already completed".to_string(),
                ))
            }
        };

        let mut results = Vec::new();
        for row in result {
            let wrapped_row = MySqlRowWrapper::new(row)?;
            results.push(Box::new(wrapped_row) as Box<dyn DatabaseRow>);
        }

        Ok(results)
    }

    async fn commit(mut self: Box<Self>) -> Result<(), DatabaseError> {
        if self.is_committed {
            return Err(DatabaseError::TransactionError(
                "Transaction already committed".to_string(),
            ));
        }
        if self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already rolled back".to_string(),
            ));
        }

        if let Some(connection) = self.connection.take() {
            match connection {
                MySqlTransactionType::Owned(conn_arc) => {
                    let mut conn = conn_arc.lock().await;
                    conn.exec_drop("COMMIT", ()).await.map_err(|e| {
                        DatabaseError::TransactionError(format!("MySQL commit error: {}", e))
                    })?;
                }
                MySqlTransactionType::Pooled(mut conn) => {
                    conn.exec_drop("COMMIT", ()).await.map_err(|e| {
                        DatabaseError::TransactionError(format!("MySQL commit error: {}", e))
                    })?;
                    // Connection automatically returned to pool when conn is dropped
                }
            }
            self.is_committed = true;
        }
        Ok(())
    }

    async fn rollback(mut self: Box<Self>) -> Result<(), DatabaseError> {
        if self.is_committed {
            return Err(DatabaseError::TransactionError(
                "Transaction already committed".to_string(),
            ));
        }
        if self.is_rolled_back {
            return Err(DatabaseError::TransactionError(
                "Transaction already rolled back".to_string(),
            ));
        }

        if let Some(connection) = self.connection.take() {
            match connection {
                MySqlTransactionType::Owned(conn_arc) => {
                    let mut conn = conn_arc.lock().await;
                    conn.exec_drop("ROLLBACK", ()).await.map_err(|e| {
                        DatabaseError::TransactionError(format!("MySQL rollback error: {}", e))
                    })?;
                }
                MySqlTransactionType::Pooled(mut conn) => {
                    conn.exec_drop("ROLLBACK", ()).await.map_err(|e| {
                        DatabaseError::TransactionError(format!("MySQL rollback error: {}", e))
                    })?;
                    // Connection automatically returned to pool when conn is dropped
                }
            }
            self.is_rolled_back = true;
        }
        Ok(())
    }

    fn is_async_native(&self) -> bool {
        true // MySQL operations are truly async
    }
}

// Critical: Implement Drop trait for MySqlTransactionWrapper
// SAFETY NET: Ensures transactions are rolled back if they go out of scope without explicit commit
impl Drop for MySqlTransactionWrapper {
    fn drop(&mut self) {
        if !self.is_committed && !self.is_rolled_back {
            if let Some(MySqlTransactionType::Pooled(_conn)) = self.connection.take() {
                // Connection is automatically returned to pool when `conn` is dropped
                // The deadpool Object will handle connection cleanup
                // Log the uncommitted transaction for debugging
                eprintln!("WARNING: MySQL transaction dropped without explicit commit or rollback");

                // Note: We cannot call async rollback() from sync Drop
                // The connection's return to pool will handle cleanup
                // Best practice: Always explicitly commit or rollback transactions
            }
        }
    }
}
