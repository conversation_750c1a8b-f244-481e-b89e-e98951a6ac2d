//! MySQL connection implementation

use std::sync::Arc;

use async_trait::async_trait;
use mysql_async::{prelude::Queryable, Conn, Value as MySqlValue};
use tokio::sync::Mutex;

use super::{
    row::MySqlRowWrapper, transaction::MySqlTransactionWrapper,
    utils::convert_database_value_to_mysql,
};
use crate::database::native::common::{
    errors::DatabaseError,
    traits::{DatabaseConnection, DatabaseRow, DatabaseTransaction, DatabaseValue},
};

pub enum MySqlConnectionWrapper {
    Owned(Arc<Mutex<Conn>>),
    Pooled(Option<deadpool::managed::Object<super::pool::MySqlConnectionManager>>), // Option allows ownership transfer
}

impl MySqlConnectionWrapper {
    pub fn new(connection: Conn) -> Self {
        Self::Owned(Arc::new(Mutex::new(connection)))
    }

    pub fn from_pooled(
        connection: deadpool::managed::Object<super::pool::MySqlConnectionManager>,
    ) -> Self {
        Self::Pooled(Some(connection))
    }

    fn convert_params(params: &[DatabaseValue]) -> Result<Vec<MySqlValue>, DatabaseError> {
        params
            .iter()
            .map(|param| convert_database_value_to_mysql(param.clone()))
            .collect()
    }
}

#[async_trait]
impl DatabaseConnection for MySqlConnectionWrapper {
    async fn execute(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<u64, DatabaseError> {
        let mysql_params = Self::convert_params(params)?;

        match self {
            MySqlConnectionWrapper::Owned(connection) => {
                let mut conn = connection.lock().await;
                conn.exec_drop(query, mysql_params).await.map_err(|e| {
                    DatabaseError::QueryError(format!("MySQL execute error: {}", e))
                })?;
                Ok(0) // MySQL exec_drop doesn't return affected rows directly
            }
            MySqlConnectionWrapper::Pooled(pooled_conn_opt) => {
                let pooled = pooled_conn_opt.as_mut().ok_or_else(|| {
                    DatabaseError::ConnectionError(
                        "Connection is in use by a transaction".to_string(),
                    )
                })?;
                pooled.exec_drop(query, mysql_params).await.map_err(|e| {
                    DatabaseError::QueryError(format!("MySQL execute error: {}", e))
                })?;
                Ok(0) // MySQL exec_drop doesn't return affected rows directly
            }
        }
    }

    async fn query_one(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Box<dyn DatabaseRow>, DatabaseError> {
        let mysql_params = Self::convert_params(params)?;

        let row =
            match self {
                MySqlConnectionWrapper::Owned(connection) => {
                    let mut conn = connection.lock().await;
                    let mut result = conn.exec_iter(query, mysql_params).await.map_err(|e| {
                        DatabaseError::QueryError(format!("MySQL query error: {}", e))
                    })?;

                    match result.next().await {
                        Ok(Some(row)) => Ok(row),
                        Ok(None) => Err(DatabaseError::NotFound),
                        Err(e) => Err(DatabaseError::QueryError(format!("MySQL row error: {}", e))),
                    }
                }
                MySqlConnectionWrapper::Pooled(pooled_conn_opt) => {
                    let pooled = pooled_conn_opt.as_mut().ok_or_else(|| {
                        DatabaseError::ConnectionError(
                            "Connection is in use by a transaction".to_string(),
                        )
                    })?;
                    let mut result = pooled.exec_iter(query, mysql_params).await.map_err(|e| {
                        DatabaseError::QueryError(format!("MySQL query error: {}", e))
                    })?;

                    match result.next().await {
                        Ok(Some(row)) => Ok(row),
                        Ok(None) => Err(DatabaseError::NotFound),
                        Err(e) => Err(DatabaseError::QueryError(format!("MySQL row error: {}", e))),
                    }
                }
            }?;

        let wrapped_row = MySqlRowWrapper::new(row)?;
        Ok(Box::new(wrapped_row))
    }

    async fn query_all(
        &mut self,
        query: &str,
        params: &[DatabaseValue],
    ) -> Result<Vec<Box<dyn DatabaseRow>>, DatabaseError> {
        let mysql_params = Self::convert_params(params)?;

        let result = match self {
            MySqlConnectionWrapper::Owned(connection) => {
                let mut conn = connection.lock().await;
                conn.exec(query, mysql_params)
                    .await
                    .map_err(|e| DatabaseError::QueryError(format!("MySQL query error: {}", e)))?
            }
            MySqlConnectionWrapper::Pooled(pooled_conn_opt) => {
                let pooled = pooled_conn_opt.as_mut().ok_or_else(|| {
                    DatabaseError::ConnectionError(
                        "Connection is in use by a transaction".to_string(),
                    )
                })?;
                pooled
                    .exec(query, mysql_params)
                    .await
                    .map_err(|e| DatabaseError::QueryError(format!("MySQL query error: {}", e)))?
            }
        };

        let mut results = Vec::new();
        for row in result {
            let wrapped_row = MySqlRowWrapper::new(row)?;
            results.push(Box::new(wrapped_row) as Box<dyn DatabaseRow>);
        }

        Ok(results)
    }

    async fn begin_transaction(&mut self) -> Result<Box<dyn DatabaseTransaction>, DatabaseError> {
        match self {
            MySqlConnectionWrapper::Owned(connection) => {
                let transaction = MySqlTransactionWrapper::new(Arc::clone(connection)).await?;
                Ok(Box::new(transaction))
            }
            MySqlConnectionWrapper::Pooled(pooled_conn_opt) => {
                // Take ownership of the pooled connection
                let pooled_conn = pooled_conn_opt.take().ok_or_else(|| {
                    DatabaseError::TransactionError(
                        "Connection is already in a transaction".to_string(),
                    )
                })?;

                let transaction = MySqlTransactionWrapper::new_pooled(pooled_conn).await?;
                Ok(Box::new(transaction))
            }
        }
    }

    async fn ping(&mut self) -> Result<(), DatabaseError> {
        match self {
            MySqlConnectionWrapper::Owned(connection) => {
                let mut conn = connection.lock().await;
                conn.ping().await.map_err(|e| {
                    DatabaseError::ConnectionError(format!("MySQL ping failed: {}", e))
                })
            }
            MySqlConnectionWrapper::Pooled(pooled_conn_opt) => {
                let pooled = pooled_conn_opt.as_mut().ok_or_else(|| {
                    DatabaseError::ConnectionError(
                        "Connection is in use by a transaction".to_string(),
                    )
                })?;
                pooled.ping().await.map_err(|e| {
                    DatabaseError::ConnectionError(format!("MySQL ping failed: {}", e))
                })
            }
        }
    }

    fn is_async_native(&self) -> bool {
        true // MySQL operations are truly async
    }
}

impl Clone for MySqlConnectionWrapper {
    fn clone(&self) -> Self {
        match self {
            MySqlConnectionWrapper::Owned(conn) => MySqlConnectionWrapper::Owned(Arc::clone(conn)),
            MySqlConnectionWrapper::Pooled(_pooled_conn_opt) => {
                // Since we can't clone the pooled connection, we return a None variant
                // This is intentional - pooled connections should not be cloned during active transactions
                MySqlConnectionWrapper::Pooled(None)
            }
        }
    }
}
