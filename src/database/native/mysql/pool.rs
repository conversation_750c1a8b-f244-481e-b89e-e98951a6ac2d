//! MySQL connection pool implementation using deadpool

use std::time::Duration;

use async_trait::async_trait;
use deadpool::managed::{Manager, Pool, PoolConfig};
use mysql_async::{Conn, OptsBuilder};

use super::connection::MySqlConnectionWrapper;
use crate::database::native::common::{errors::DatabaseError, traits::DatabasePool};

pub struct MySqlPool {
    pool: Pool<MySqlConnectionManager>,
}

pub struct MySqlConnectionManager {
    opts: OptsBuilder,
}

impl MySqlConnectionManager {
    pub fn new(opts: OptsBuilder) -> Self {
        Self { opts }
    }
}

#[async_trait]
impl Manager for MySqlConnectionManager {
    type Type = Conn;
    type Error = mysql_async::Error;

    async fn create(&self) -> Result<Self::Type, Self::Error> {
        Conn::new(self.opts.clone()).await
    }

    async fn recycle(
        &self,
        conn: &mut Self::Type,
        _metrics: &deadpool::managed::Metrics,
    ) -> deadpool::managed::RecycleResult<Self::Error> {
        use mysql_async::prelude::Queryable;
        match conn.ping().await {
            Ok(_) => Ok(()),
            Err(e) => Err(deadpool::managed::RecycleError::Backend(e)),
        }
    }
}

impl MySqlPool {
    pub fn new(database_url: &str, max_connections: u32) -> Result<Self, DatabaseError> {
        let opts = OptsBuilder::from_opts(
            mysql_async::Opts::from_url(database_url)
                .map_err(|e| DatabaseError::PoolError(format!("Invalid database URL: {}", e)))?,
        );

        let manager = MySqlConnectionManager::new(opts);
        let config = PoolConfig {
            max_size: max_connections as usize,
            timeouts: deadpool::managed::Timeouts::default(),
            queue_mode: deadpool::managed::QueueMode::Fifo,
        };

        let pool = Pool::builder(manager)
            .config(config)
            .build()
            .map_err(|e| DatabaseError::PoolError(format!("Failed to create MySQL pool: {}", e)))?;

        Ok(Self { pool })
    }

    pub fn with_config(config: MySqlPoolConfig) -> Result<Self, DatabaseError> {
        let opts = OptsBuilder::default()
            .ip_or_hostname(config.host)
            .tcp_port(config.port)
            .user(Some(config.user))
            .db_name(Some(config.database))
            .pass(config.password);

        let manager = MySqlConnectionManager::new(opts);
        let pool_config = PoolConfig {
            max_size: config.max_connections as usize,
            timeouts: deadpool::managed::Timeouts {
                wait: Some(config.connection_timeout),
                create: Some(config.connection_timeout),
                recycle: Some(config.connection_timeout),
            },
            queue_mode: deadpool::managed::QueueMode::Fifo,
        };

        let pool = Pool::builder(manager)
            .config(pool_config)
            .build()
            .map_err(|e| DatabaseError::PoolError(format!("Failed to create MySQL pool: {}", e)))?;

        Ok(Self { pool })
    }

    /// Get the underlying deadpool for advanced usage
    pub fn inner(&self) -> &Pool<MySqlConnectionManager> {
        &self.pool
    }
}

#[async_trait]
impl DatabasePool for MySqlPool {
    type Connection = MySqlConnectionWrapper;

    async fn get_connection(&self) -> Result<Self::Connection, DatabaseError> {
        let conn = self.pool.get().await.map_err(|e| {
            DatabaseError::PoolError(format!("Failed to get connection from pool: {}", e))
        })?;

        // Use the pooled connection
        Ok(MySqlConnectionWrapper::from_pooled(conn))
    }

    async fn close(&self) -> Result<(), DatabaseError> {
        // deadpool doesn't have an explicit close method
        // Connections are closed when the pool is dropped
        Ok(())
    }

    fn max_connections(&self) -> u32 {
        self.pool.status().max_size as u32
    }

    fn active_connections(&self) -> u32 {
        let status = self.pool.status();
        (status.size - status.available) as u32
    }

    fn idle_connections(&self) -> u32 {
        self.pool.status().available as u32
    }

    async fn health_check(&self) -> Result<(), DatabaseError> {
        use crate::database::native::common::traits::DatabaseConnection;
        let mut conn = self.get_connection().await?;
        conn.ping().await
    }
}

impl MySqlPool {
    /// Get connection information for MySQL-specific features
    pub async fn get_connection_info(
        &self,
    ) -> Result<super::super::super::native_mysql::MySqlConnectionInfo, DatabaseError> {
        let status = self.pool.status();
        Ok(super::super::super::native_mysql::MySqlConnectionInfo {
            max_connections: status.max_size as u32,
            active_connections: (status.size - status.available) as u32,
        })
    }
}

impl Clone for MySqlPool {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
        }
    }
}

/// Configuration for MySQL connection pool
#[derive(Debug, Clone)]
pub struct MySqlPoolConfig {
    pub host: String,
    pub port: u16,
    pub user: String,
    pub password: Option<String>,
    pub database: String,
    pub max_connections: u32,
    pub connection_timeout: Duration,
}

impl Default for MySqlPoolConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 3306,
            user: "root".to_string(),
            password: None,
            database: "mysql".to_string(),
            max_connections: 10,
            connection_timeout: Duration::from_secs(30),
        }
    }
}

impl MySqlPoolConfig {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn host<S: Into<String>>(mut self, host: S) -> Self {
        self.host = host.into();
        self
    }

    pub fn port(mut self, port: u16) -> Self {
        self.port = port;
        self
    }

    pub fn user<S: Into<String>>(mut self, user: S) -> Self {
        self.user = user.into();
        self
    }

    pub fn password<S: Into<String>>(mut self, password: S) -> Self {
        self.password = Some(password.into());
        self
    }

    pub fn database<S: Into<String>>(mut self, database: S) -> Self {
        self.database = database.into();
        self
    }

    pub fn max_connections(mut self, max: u32) -> Self {
        self.max_connections = max;
        self
    }

    pub fn connection_timeout(mut self, timeout: Duration) -> Self {
        self.connection_timeout = timeout;
        self
    }

    pub fn to_connection_string(&self) -> String {
        let mut url = format!(
            "mysql://{}@{}:{}/{}",
            self.user, self.host, self.port, self.database
        );

        if let Some(ref password) = self.password {
            url = format!(
                "mysql://{}:{}@{}:{}/{}",
                self.user, password, self.host, self.port, self.database
            );
        }

        url
    }
}
