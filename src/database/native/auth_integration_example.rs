//! Example showing how to integrate the native database system with the auth engine

#[cfg(test)]
mod auth_integration {
    use chrono::Utc;
    use serde_json::json;
    use uuid::Uuid;

    use super::super::DatabaseProvider;
    use crate::database::native::common::{
        traits::{DatabaseConnection, DatabasePool, DatabaseValue},
        types::FromValue,
    };

    /// Example: Setting up auth tables with the native database system
    #[tokio::test]
    async fn example_auth_system_integration() {
        // Create a provider for the auth system
        let provider =
            DatabaseProvider::sqlite_memory(20).expect("Failed to create database provider");

        println!("🚀 Setting up auth system with native database drivers");
        println!("Database type: {}", provider.database_type());
        println!("Max connections: {}", provider.max_connections());

        let mut conn = provider
            .get_connection()
            .await
            .expect("Failed to get connection");

        // Create auth system tables
        setup_auth_tables(&mut conn)
            .await
            .expect("Failed to setup auth tables");

        // Demonstrate user registration flow
        let user_id = register_user(
            &mut conn,
            "john_doe",
            "<EMAIL>",
            "secure_password_hash",
        )
        .await
        .expect("Failed to register user");
        println!("✅ User registered with ID: {user_id}");

        // Demonstrate session management
        let session_id = create_session(&mut conn, user_id)
            .await
            .expect("Failed to create session");
        println!("✅ Session created with ID: {session_id}");

        // Demonstrate RBAC (Role-Based Access Control)
        setup_rbac(&mut conn, user_id)
            .await
            .expect("Failed to setup RBAC");
        println!("✅ RBAC configured for user");

        // Demonstrate audit logging
        log_auth_event(&mut conn, user_id, "login", "User logged in successfully")
            .await
            .expect("Failed to log auth event");
        println!("✅ Auth event logged");

        // Demonstrate user profile with JSON metadata
        update_user_profile(&mut conn, user_id)
            .await
            .expect("Failed to update user profile");
        println!("✅ User profile updated with JSON metadata");

        // Demonstrate querying user data
        let user_data = get_user_with_profile(&mut conn, user_id)
            .await
            .expect("Failed to get user data");
        println!("✅ Retrieved user data: {}", user_data.username);

        // Demonstrate performance with batch operations
        let batch_start = std::time::Instant::now();
        create_multiple_sessions(&mut conn, user_id, 100)
            .await
            .expect("Failed to create batch sessions");
        let batch_duration = batch_start.elapsed();
        println!(
            "✅ Created 100 sessions in {:?} ({:.2} ops/sec)",
            batch_duration,
            100.0 / batch_duration.as_secs_f64()
        );

        // Health check
        provider.health_check().await.expect("Health check failed");
        println!("✅ Database health check passed");

        println!("🎉 Auth system integration example completed successfully!");
    }

    async fn setup_auth_tables(
        conn: &mut impl DatabaseConnection,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Users table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT NOT NULL UNIQUE,
                email TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                is_active INTEGER DEFAULT 1,
                email_verified INTEGER DEFAULT 0,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                metadata TEXT
            )
        "#,
            &[],
        )
        .await?;

        // Sessions table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS sessions (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                token_hash TEXT NOT NULL,
                expires_at TEXT NOT NULL,
                created_at TEXT NOT NULL,
                last_accessed_at TEXT,
                ip_address TEXT,
                user_agent TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        "#,
            &[],
        )
        .await?;

        // Roles table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS roles (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                permissions TEXT,
                created_at TEXT NOT NULL
            )
        "#,
            &[],
        )
        .await?;

        // User roles junction table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS user_roles (
                user_id TEXT NOT NULL,
                role_id TEXT NOT NULL,
                assigned_at TEXT NOT NULL,
                assigned_by TEXT,
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE
            )
        "#,
            &[],
        )
        .await?;

        // Audit log table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS audit_log (
                id TEXT PRIMARY KEY,
                user_id TEXT,
                event_type TEXT NOT NULL,
                event_data TEXT,
                ip_address TEXT,
                user_agent TEXT,
                timestamp TEXT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
            )
        "#,
            &[],
        )
        .await?;

        // Password history table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS password_history (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TEXT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        "#,
            &[],
        )
        .await?;

        // Two-factor authentication table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS two_factor_auth (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL UNIQUE,
                secret TEXT NOT NULL,
                backup_codes TEXT,
                is_enabled INTEGER DEFAULT 0,
                created_at TEXT NOT NULL,
                last_used_at TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        "#,
            &[],
        )
        .await?;

        Ok(())
    }

    async fn register_user(
        conn: &mut impl DatabaseConnection,
        username: &str,
        email: &str,
        password_hash: &str,
    ) -> Result<Uuid, Box<dyn std::error::Error>> {
        let user_id = Uuid::new_v4();
        let now = Utc::now();
        let metadata = json!({
            "registration_ip": "127.0.0.1",
            "registration_source": "web",
            "preferences": {
                "theme": "light",
                "notifications": true
            }
        });

        conn.execute(
            r#"
            INSERT INTO users (id, username, email, password_hash, created_at, updated_at, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        "#,
            &[
                DatabaseValue::Uuid(user_id),
                DatabaseValue::Text(username.to_string()),
                DatabaseValue::Text(email.to_string()),
                DatabaseValue::Text(password_hash.to_string()),
                DatabaseValue::DateTime(now),
                DatabaseValue::DateTime(now),
                DatabaseValue::Json(metadata),
            ],
        )
        .await?;

        Ok(user_id)
    }

    async fn create_session(
        conn: &mut impl DatabaseConnection,
        user_id: Uuid,
    ) -> Result<Uuid, Box<dyn std::error::Error>> {
        let session_id = Uuid::new_v4();
        let now = Utc::now();
        let expires_at = now + chrono::Duration::hours(24);

        conn.execute(r#"
            INSERT INTO sessions (id, user_id, token_hash, expires_at, created_at, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        "#, &[
            DatabaseValue::Uuid(session_id),
            DatabaseValue::Uuid(user_id),
            DatabaseValue::Text("hashed_token_value".to_string()),
            DatabaseValue::DateTime(expires_at),
            DatabaseValue::DateTime(now),
            DatabaseValue::Text("127.0.0.1".to_string()),
            DatabaseValue::Text("Mozilla/5.0 (Example Browser)".to_string()),
        ]).await?;

        Ok(session_id)
    }

    async fn setup_rbac(
        conn: &mut impl DatabaseConnection,
        user_id: Uuid,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Create a role
        let role_id = Uuid::new_v4();
        let permissions = json!(["read:profile", "write:profile", "read:dashboard"]);

        conn.execute(
            r#"
            INSERT INTO roles (id, name, description, permissions, created_at)
            VALUES (?, ?, ?, ?, ?)
        "#,
            &[
                DatabaseValue::Uuid(role_id),
                DatabaseValue::Text("user".to_string()),
                DatabaseValue::Text("Standard user role".to_string()),
                DatabaseValue::Json(permissions),
                DatabaseValue::DateTime(Utc::now()),
            ],
        )
        .await?;

        // Assign role to user
        conn.execute(
            r#"
            INSERT INTO user_roles (user_id, role_id, assigned_at)
            VALUES (?, ?, ?)
        "#,
            &[
                DatabaseValue::Uuid(user_id),
                DatabaseValue::Uuid(role_id),
                DatabaseValue::DateTime(Utc::now()),
            ],
        )
        .await?;

        Ok(())
    }

    async fn log_auth_event(
        conn: &mut impl DatabaseConnection,
        user_id: Uuid,
        event_type: &str,
        event_data: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let event_id = Uuid::new_v4();
        let event_json = json!({
            "message": event_data,
            "success": true,
            "method": "password"
        });

        conn.execute(
            r#"
            INSERT INTO audit_log (id, user_id, event_type, event_data, ip_address, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        "#,
            &[
                DatabaseValue::Uuid(event_id),
                DatabaseValue::Uuid(user_id),
                DatabaseValue::Text(event_type.to_string()),
                DatabaseValue::Json(event_json),
                DatabaseValue::Text("127.0.0.1".to_string()),
                DatabaseValue::DateTime(Utc::now()),
            ],
        )
        .await?;

        Ok(())
    }

    async fn update_user_profile(
        conn: &mut impl DatabaseConnection,
        user_id: Uuid,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let updated_metadata = json!({
            "registration_ip": "127.0.0.1",
            "registration_source": "web",
            "preferences": {
                "theme": "dark",
                "notifications": true,
                "language": "en"
            },
            "profile": {
                "first_name": "John",
                "last_name": "Doe",
                "bio": "Software developer",
                "avatar_url": "https://example.com/avatar.jpg"
            },
            "last_login": "2024-01-15T10:30:00Z"
        });

        conn.execute(
            r#"
            UPDATE users SET metadata = ?, updated_at = ? WHERE id = ?
        "#,
            &[
                DatabaseValue::Json(updated_metadata),
                DatabaseValue::DateTime(Utc::now()),
                DatabaseValue::Uuid(user_id),
            ],
        )
        .await?;

        Ok(())
    }

    #[allow(dead_code)]
    #[derive(Debug)]
    struct UserWithProfile {
        id: Uuid,
        username: String,
        email: String,
        is_active: bool,
        metadata: serde_json::Value,
        role_count: i64,
        session_count: i64,
    }

    async fn get_user_with_profile(
        conn: &mut impl DatabaseConnection,
        user_id: Uuid,
    ) -> Result<UserWithProfile, Box<dyn std::error::Error>> {
        // Complex query joining multiple tables
        let row = conn
            .query_one(
                r#"
            SELECT 
                u.id,
                u.username,
                u.email,
                u.is_active,
                u.metadata,
                (SELECT COUNT(*) FROM user_roles ur WHERE ur.user_id = u.id) as role_count,
                (SELECT COUNT(*) FROM sessions s WHERE s.user_id = u.id) as session_count
            FROM users u
            WHERE u.id = ?
        "#,
                &[DatabaseValue::Uuid(user_id)],
            )
            .await?;

        Ok(UserWithProfile {
            id: FromValue::from_value(row.get_by_name("id")?)?,
            username: FromValue::from_value(row.get_by_name("username")?)?,
            email: FromValue::from_value(row.get_by_name("email")?)?,
            is_active: FromValue::from_value(row.get_by_name("is_active")?)?,
            metadata: FromValue::from_value(row.get_by_name("metadata")?)?,
            role_count: FromValue::from_value(row.get_by_name("role_count")?)?,
            session_count: FromValue::from_value(row.get_by_name("session_count")?)?,
        })
    }

    async fn create_multiple_sessions(
        conn: &mut impl DatabaseConnection,
        user_id: Uuid,
        count: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let now = Utc::now();
        let expires_at = now + chrono::Duration::hours(24);

        for i in 0..count {
            let session_id = Uuid::new_v4();
            conn.execute(
                r#"
                INSERT INTO sessions (id, user_id, token_hash, expires_at, created_at, ip_address)
                VALUES (?, ?, ?, ?, ?, ?)
            "#,
                &[
                    DatabaseValue::Uuid(session_id),
                    DatabaseValue::Uuid(user_id),
                    DatabaseValue::Text(format!("token_hash_{i}")),
                    DatabaseValue::DateTime(expires_at),
                    DatabaseValue::DateTime(now),
                    DatabaseValue::Text("127.0.0.1".to_string()),
                ],
            )
            .await?;
        }

        Ok(())
    }
}
