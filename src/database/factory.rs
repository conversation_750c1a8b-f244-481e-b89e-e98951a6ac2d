//! Database provider factory for creating providers with convenient methods

use std::sync::Arc;

#[cfg(feature = "mysql-native")]
use crate::database::native_mysql::NativeMysqlProvider;
#[cfg(feature = "postgres-native")]
use crate::database::native_postgres::NativePostgresProvider;
#[cfg(feature = "sqlite-native")]
use crate::database::native_sqlite::NativeSqliteProvider;
use crate::{
    config::AuthConfig,
    database::{connection::DatabaseType, DatabaseProvider},
    error::{AuthError, Result},
};

/// Factory for creating database providers with convenient methods
pub struct DatabaseProviderFactory;

impl DatabaseProviderFactory {
    /// Create a SQLite provider with the given database path
    #[cfg(feature = "sqlite-native")]
    pub async fn create_sqlite(database_path: &str) -> Result<Arc<dyn DatabaseProvider>> {
        let provider = NativeSqliteProvider::new(database_path)?;
        let provider_arc = Arc::new(provider);
        // Initialize the database tables (both auth and RBAC)
        provider_arc.initialize().await?;

        // Verify tables were created by checking health
        let _health = provider_arc.health_check().await?;

        Ok(provider_arc)
    }

    /// Create a SQLite provider with the given database path and config
    #[cfg(feature = "sqlite-native")]
    pub async fn create_sqlite_with_config(
        database_path: &str,
        config: &AuthConfig,
    ) -> Result<Arc<dyn DatabaseProvider>> {
        let provider =
            NativeSqliteProvider::new_with_prefix(database_path, config.table_prefix.clone())?;
        let provider_arc = Arc::new(provider);
        // Initialize the database tables (both auth and RBAC)
        provider_arc.initialize().await?;

        // Verify tables were created by checking health
        let _health = provider_arc.health_check().await?;

        Ok(provider_arc)
    }

    /// Create a PostgreSQL provider with the given connection string
    #[cfg(feature = "postgres-native")]
    pub async fn create_postgres(connection_string: &str) -> Result<Arc<dyn DatabaseProvider>> {
        let provider = NativePostgresProvider::new(connection_string).await?;
        Ok(Arc::new(provider))
    }

    /// Create a PostgreSQL provider with the given connection string and config
    #[cfg(feature = "postgres-native")]
    pub async fn create_postgres_with_config(
        connection_string: &str,
        config: &AuthConfig,
    ) -> Result<Arc<dyn DatabaseProvider>> {
        let provider =
            NativePostgresProvider::new_with_prefix(connection_string, config.table_prefix.clone())
                .await?;
        let provider_arc = Arc::new(provider);
        // Initialize the database tables (both auth and RBAC)
        provider_arc.initialize().await?;

        // Verify tables were created by checking health
        let _health = provider_arc.health_check().await?;

        Ok(provider_arc)
    }

    /// Create a MySQL provider with the given connection string
    #[cfg(feature = "mysql-native")]
    pub async fn create_mysql(connection_string: &str) -> Result<Arc<dyn DatabaseProvider>> {
        let provider = NativeMysqlProvider::new(connection_string).await?;
        Ok(Arc::new(provider))
    }

    /// Create a MySQL provider with the given connection string and config
    #[cfg(feature = "mysql-native")]
    pub async fn create_mysql_with_config(
        connection_string: &str,
        config: &AuthConfig,
    ) -> Result<Arc<dyn DatabaseProvider>> {
        let provider =
            NativeMysqlProvider::new_with_prefix(connection_string, config.table_prefix.clone())
                .await?;
        let provider_arc = Arc::new(provider);
        provider_arc.initialize().await?;

        // Verify tables were created by checking health
        let _health = provider_arc.health_check().await?;

        Ok(provider_arc)
    }

    /// Create a provider from a database type and connection string
    pub async fn create_provider(
        database_type: DatabaseType,
        connection_string: &str,
    ) -> Result<Arc<dyn DatabaseProvider>> {
        match database_type {
            #[cfg(feature = "sqlite-native")]
            DatabaseType::Sqlite => Self::create_sqlite(connection_string).await,
            #[cfg(feature = "postgres-native")]
            DatabaseType::Postgres => Self::create_postgres(connection_string).await,
            #[cfg(feature = "mysql-native")]
            DatabaseType::Mysql => Self::create_mysql(connection_string).await,
            #[allow(unreachable_patterns)]
            _ => Err(AuthError::Configuration {
                message: format!("Unsupported database type: {database_type:?}"),
                field: Some("database_type".to_string()),
            }),
        }
    }

    /// Create a provider from a URL (legacy support)
    ///
    /// This method parses database URLs and creates the appropriate provider.
    /// URL formats:
    /// - SQLite: `sqlite:path/to/db.sqlite` or `sqlite::memory:`
    /// - PostgreSQL: `postgres://user:pass@host:port/dbname`
    /// - MySQL: `mysql://user:pass@host:port/dbname`
    pub async fn from_url(url: &str) -> Result<Arc<dyn DatabaseProvider>> {
        if url.starts_with("sqlite:") {
            let path = url.strip_prefix("sqlite:").unwrap_or(url);
            #[cfg(feature = "sqlite-native")]
            return Self::create_sqlite(path).await;
            #[cfg(not(feature = "sqlite-native"))]
            return Err(AuthError::Configuration {
                message: "SQLite support not enabled".to_string(),
                field: Some("database_url".to_string()),
            });
        } else if url.starts_with("postgres://") || url.starts_with("postgresql://") {
            #[cfg(feature = "postgres-native")]
            return Self::create_postgres(url).await;
            #[cfg(not(feature = "postgres-native"))]
            return Err(AuthError::Configuration {
                message: "PostgreSQL support not enabled".to_string(),
                field: Some("database_url".to_string()),
            });
        } else if url.starts_with("mysql://") {
            #[cfg(feature = "mysql-native")]
            return Self::create_mysql(url).await;
            #[cfg(not(feature = "mysql-native"))]
            return Err(AuthError::Configuration {
                message: "MySQL support not enabled".to_string(),
                field: Some("database_url".to_string()),
            });
        } else {
            Err(AuthError::Configuration {
                message: format!("Unsupported database URL format: {url}"),
                field: Some("database_url".to_string()),
            })
        }
    }
}

/// Convenient builder for database providers
pub struct DatabaseProviderBuilder {
    database_type: Option<DatabaseType>,
    connection_string: Option<String>,
    auto_initialize: bool,
}

impl DatabaseProviderBuilder {
    /// Create a new builder
    pub fn new() -> Self {
        Self {
            database_type: None,
            connection_string: None,
            auto_initialize: true,
        }
    }

    /// Set the database type
    pub fn database_type(mut self, database_type: DatabaseType) -> Self {
        self.database_type = Some(database_type);
        self
    }

    /// Set the connection string
    pub fn connection_string(mut self, connection_string: impl Into<String>) -> Self {
        self.connection_string = Some(connection_string.into());
        self
    }

    /// Set whether to automatically initialize the database
    pub fn auto_initialize(mut self, auto_initialize: bool) -> Self {
        self.auto_initialize = auto_initialize;
        self
    }

    /// Build the database provider
    pub async fn build(self) -> Result<Arc<dyn DatabaseProvider>> {
        let database_type = self.database_type.ok_or_else(|| AuthError::Configuration {
            message: "Database type is required".to_string(),
            field: Some("database_type".to_string()),
        })?;

        let connection_string = self
            .connection_string
            .ok_or_else(|| AuthError::Configuration {
                message: "Connection string is required".to_string(),
                field: Some("connection_string".to_string()),
            })?;

        let provider =
            DatabaseProviderFactory::create_provider(database_type, &connection_string).await?;

        if self.auto_initialize {
            provider.initialize().await?;
        }

        Ok(provider)
    }
}

impl Default for DatabaseProviderBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::AuthConfig;

    #[test]
    fn test_database_provider_factory_creation() {
        // Test that the factory can be instantiated
        assert!(true); // Factory is a collection of static methods
    }

    #[cfg(feature = "sqlite-native")]
    #[tokio::test]
    async fn test_create_sqlite() {
        let result = DatabaseProviderFactory::create_sqlite(":memory:").await;
        assert!(result.is_ok());

        let provider = result.unwrap();
        assert!(provider.health_check().await.is_ok());
    }

    #[cfg(feature = "sqlite-native")]
    #[tokio::test]
    async fn test_create_sqlite_with_config() {
        let config = AuthConfig::builder()
            .table_prefix("test_".to_string())
            .build()
            .unwrap();

        let result = DatabaseProviderFactory::create_sqlite_with_config(":memory:", &config).await;
        assert!(result.is_ok());

        let provider = result.unwrap();
        assert!(provider.health_check().await.is_ok());
    }

    #[test]
    fn test_database_provider_builder() {
        let builder = DatabaseProviderBuilder::new();
        assert!(builder.database_type.is_none());
        assert!(builder.connection_string.is_none());
        assert!(builder.auto_initialize);

        let builder = DatabaseProviderBuilder::default();
        assert!(builder.database_type.is_none());
    }

    #[tokio::test]
    async fn test_database_provider_builder_missing_fields() {
        let builder = DatabaseProviderBuilder::new();
        let result = builder.build().await;
        assert!(result.is_err()); // Should fail due to missing database_type

        let builder = DatabaseProviderBuilder::new().database_type(DatabaseType::Sqlite);
        let result = builder.build().await;
        assert!(result.is_err()); // Should fail due to missing connection_string
    }

    #[tokio::test]
    async fn test_from_url_parsing() {
        // Test URL parsing logic
        let sqlite_url = "sqlite::memory:";
        #[cfg(feature = "sqlite-native")]
        {
            let result = DatabaseProviderFactory::from_url(sqlite_url).await;
            assert!(result.is_ok());
        }

        // Test unsupported URL
        let invalid_url = "invalid://test";
        let result = DatabaseProviderFactory::from_url(invalid_url).await;
        assert!(result.is_err());
    }

    #[test]
    fn test_config_validation() {
        let config = AuthConfig::default();
        assert!(!config.table_prefix.is_empty() || config.table_prefix.is_empty()); // Either is valid

        let custom_config = AuthConfig::builder()
            .table_prefix("custom_".to_string())
            .build()
            .unwrap();
        assert_eq!(custom_config.table_prefix, "custom_");
    }
}
