//! Native PostgreSQL database provider implementation

use async_trait::async_trait;

use crate::{
    database::{
        native::{
            common::traits::{DatabaseConnection, DatabasePool, DatabaseRow, DatabaseValue},
            postgres::PostgresPool,
        },
        DatabaseHealth, DatabaseProvider, DatabaseStats, DatabaseTransaction,
    },
    error::{AuthError, Result},
    types::{
        CreatePermissionRequest, CreateRoleRequest, Permission, PermissionCheck, PermissionFilter,
        PermissionId, PermissionResult, Role, RoleFilter, RoleId, RolePermission, Session,
        SessionId, User, UserAttribute, UserAttributeFilter, UserAttributeRequest,
        UserAttributeValue, UserAttributes, UserId, UserRoleAssignment,
    },
};

/// Native PostgreSQL database provider
#[derive(Clone)]
pub struct NativePostgresProvider {
    pool: PostgresPool,
    table_prefix: String,
}

impl NativePostgresProvider {
    /// Create a new PostgreSQL provider with the given connection string
    pub async fn new(connection_string: &str) -> Result<Self> {
        let pool = PostgresPool::new(connection_string, 10)
            .map_err(|e| AuthError::internal(format!("Failed to create PostgreSQL pool: {e}")))?;

        Ok(Self {
            pool,
            table_prefix: String::new(),
        })
    }

    /// Create a new PostgreSQL provider with the given connection string and table prefix
    pub async fn new_with_prefix(connection_string: &str, table_prefix: String) -> Result<Self> {
        let pool = PostgresPool::new(connection_string, 10)
            .map_err(|e| AuthError::internal(format!("Failed to create PostgreSQL pool: {e}")))?;

        Ok(Self { pool, table_prefix })
    }

    /// Helper function to get table name with prefix
    fn table_name(&self, base_name: &str) -> String {
        // Validate that prefix only contains safe characters
        if !self
            .table_prefix
            .chars()
            .all(|c| c.is_alphanumeric() || c == '_')
        {
            panic!("Invalid table prefix: contains non-alphanumeric characters");
        }
        format!("{}{}", self.table_prefix, base_name)
    }

    // Helper method to convert database row to UserAttribute
    fn row_to_user_attribute(
        &self,
        row: &dyn crate::database::native::common::traits::DatabaseRow,
    ) -> Result<UserAttribute> {
        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                _ => String::new(),
            }
        };

        let get_f64 = |idx: usize| -> Option<f64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::F64(f) => Some(f),
                DatabaseValue::I32(i) => Some(i as f64),
                DatabaseValue::I64(i) => Some(i as f64),
                _ => None,
            }
        };

        let get_bool = |idx: usize| -> Option<bool> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Bool(b) => Some(b),
                DatabaseValue::I32(i) => Some(i != 0),
                DatabaseValue::I64(i) => Some(i != 0),
                _ => None,
            }
        };

        let id_str = get_text(0);
        let user_id_str = get_text(1);
        let key = get_text(2);
        let value_type = get_text(3);
        let value_string = get_text(4);
        let value_number = get_f64(5);
        let value_boolean = get_bool(6);
        let value_json = get_text(7);
        let created_at_str = get_text(8);
        let updated_at_str = get_text(9);

        // Parse UUIDs
        let id = uuid::Uuid::parse_str(&id_str)
            .map_err(|_| AuthError::internal("Invalid attribute ID"))?;
        let user_id = uuid::Uuid::parse_str(&user_id_str)
            .map_err(|_| AuthError::internal("Invalid user ID"))?;

        // Parse timestamps
        let created_at = chrono::DateTime::parse_from_rfc3339(&created_at_str)
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .unwrap_or_else(|_| chrono::Utc::now());
        let updated_at = chrono::DateTime::parse_from_rfc3339(&updated_at_str)
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .unwrap_or_else(|_| chrono::Utc::now());

        // Reconstruct the value based on type
        let value = match value_type.as_str() {
            "string" => UserAttributeValue::String(value_string),
            "number" => {
                if let Some(n) = value_number {
                    // Check if it's an integer value
                    if n.fract() == 0.0 && n >= i64::MIN as f64 && n <= i64::MAX as f64 {
                        UserAttributeValue::Integer(n as i64)
                    } else {
                        UserAttributeValue::Number(n)
                    }
                } else {
                    UserAttributeValue::Number(0.0)
                }
            }
            "boolean" => UserAttributeValue::Boolean(value_boolean.unwrap_or(false)),
            "json" => {
                if value_json.is_empty() {
                    UserAttributeValue::Json(serde_json::Value::Null)
                } else {
                    match serde_json::from_str(&value_json) {
                        Ok(serde_json::Value::Array(arr)) => UserAttributeValue::Array(arr),
                        Ok(json_val) => UserAttributeValue::Json(json_val),
                        Err(_) => UserAttributeValue::String(value_json),
                    }
                }
            }
            _ => UserAttributeValue::String(value_string),
        };

        Ok(UserAttribute {
            id: crate::types::UserAttributeId::from(id),
            user_id: UserId::from(user_id),
            key,
            value,
            created_at,
            updated_at,
            metadata: serde_json::Value::Object(serde_json::Map::new()),
        })
    }

    fn row_to_user(&self, row: &dyn DatabaseRow) -> Result<User> {
        let get_text = |idx: usize| -> String {
            match row
                .get_by_index(idx)
                .unwrap_or(DatabaseValue::Text(String::new()))
            {
                DatabaseValue::Text(s) => s,
                _ => String::new(),
            }
        };

        let get_i64 = |idx: usize| -> Option<i64> {
            match row
                .get_by_index(idx)
                .unwrap_or(DatabaseValue::Text(String::new()))
            {
                DatabaseValue::I64(i) => Some(i),
                DatabaseValue::I32(i) => Some(i as i64),
                _ => None,
            }
        };

        let get_bool = |idx: usize| -> bool {
            match row
                .get_by_index(idx)
                .unwrap_or(DatabaseValue::Text(String::new()))
            {
                DatabaseValue::Bool(b) => b,
                DatabaseValue::I64(i) => i != 0,
                DatabaseValue::I32(i) => i != 0,
                _ => false,
            }
        };

        Ok(User {
            id: get_text(0)
                .parse::<uuid::Uuid>()
                .map(UserId::from)
                .unwrap_or_default(),
            username: get_text(1),
            email: get_text(2),
            password_hash: get_text(3),
            salt: get_text(4),
            role: get_text(8).parse().unwrap_or(crate::types::UserRole::User),
            status: get_text(9)
                .parse()
                .unwrap_or(crate::types::UserStatus::Active),
            created_at: get_i64(5)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            updated_at: get_i64(6)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            last_login: get_i64(7).and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            is_active: true, // Default for compatibility
            is_verified: get_bool(10),
            failed_login_attempts: 0, // Default for compatibility
            login_attempts: get_i64(11).unwrap_or(0) as i32,
            locked_until: None, // Default for compatibility
            email_verified: get_bool(10),
            email_verification_token: None, // Default for compatibility
            password_reset_token: None,     // Default for compatibility
            password_reset_expires: None,   // Default for compatibility
            metadata: serde_json::from_str(&get_text(12)).unwrap_or_default(),
        })
    }

    fn row_to_session(&self, row: &dyn DatabaseRow) -> Result<Session> {
        let get_text = |idx: usize| -> String {
            match row
                .get_by_index(idx)
                .unwrap_or(DatabaseValue::Text(String::new()))
            {
                DatabaseValue::Text(s) => s,
                _ => String::new(),
            }
        };

        let get_i64 = |idx: usize| -> Option<i64> {
            match row
                .get_by_index(idx)
                .unwrap_or(DatabaseValue::Text(String::new()))
            {
                DatabaseValue::I64(i) => Some(i),
                DatabaseValue::I32(i) => Some(i as i64),
                _ => None,
            }
        };

        let get_bool = |idx: usize| -> bool {
            match row
                .get_by_index(idx)
                .unwrap_or(DatabaseValue::Text(String::new()))
            {
                DatabaseValue::Bool(b) => b,
                DatabaseValue::I64(i) => i != 0,
                DatabaseValue::I32(i) => i != 0,
                _ => false,
            }
        };

        Ok(Session {
            id: get_text(0)
                .parse::<uuid::Uuid>()
                .map(SessionId::from)
                .unwrap_or_default(),
            user_id: get_text(1)
                .parse::<uuid::Uuid>()
                .map(UserId::from)
                .unwrap_or_default(),
            token: get_text(2),
            refresh_token: if get_text(3).is_empty() {
                None
            } else {
                Some(get_text(3))
            },
            expires_at: get_i64(4)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            created_at: get_i64(5)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            last_accessed: get_i64(6)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            ip_address: if get_text(7).is_empty() {
                None
            } else {
                get_text(7).parse().ok()
            },
            user_agent: if get_text(8).is_empty() {
                None
            } else {
                Some(get_text(8))
            },
            is_active: get_bool(9),
            session_data: serde_json::from_str(&get_text(10)).unwrap_or_default(),
            metadata: serde_json::from_str(&get_text(11)).unwrap_or_default(),
        })
    }
}

#[async_trait]
impl DatabaseProvider for NativePostgresProvider {
    fn provider_type(&self) -> crate::database::connection::DatabaseType {
        crate::database::connection::DatabaseType::Postgres
    }

    fn supports_feature(&self, feature: &str) -> bool {
        match feature {
            "auth" | "rbac" | "transactions" => true,
            "json" => true,             // PostgreSQL has excellent JSON support
            "full_text_search" => true, // PostgreSQL supports full-text search
            _ => false,
        }
    }

    async fn initialize(&self) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Create users table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                created_at BIGINT NOT NULL,
                updated_at BIGINT NOT NULL,
                last_login BIGINT,
                role TEXT NOT NULL DEFAULT 'user',
                status TEXT NOT NULL DEFAULT 'active',
                is_verified BOOLEAN NOT NULL DEFAULT false,
                login_attempts INTEGER NOT NULL DEFAULT 0,
                metadata TEXT
            )",
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create users table: {e}")))?;

        // Create sessions table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                token TEXT NOT NULL,
                refresh_token TEXT,
                expires_at BIGINT NOT NULL,
                created_at BIGINT NOT NULL,
                last_accessed BIGINT NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                is_active BOOLEAN NOT NULL DEFAULT true,
                session_data TEXT,
                metadata TEXT,
                FOREIGN KEY (user_id) REFERENCES {} (id) ON DELETE CASCADE
            )",
                self.table_name("sessions"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create sessions table: {e}")))?;

        // Create indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_users_username ON {} (username)",
                self.table_prefix,
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create username index: {e}")))?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_users_email ON {} (email)",
                self.table_prefix,
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create email index: {e}")))?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_sessions_user_id ON {} (user_id)",
                self.table_prefix,
                self.table_name("sessions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create sessions user_id index: {e}"))
        })?;

        // Create optimized RBAC tables with consistent naming and better indexes

        // Create permissions table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id UUID PRIMARY KEY,
                name VARCHAR(255) UNIQUE NOT NULL,
                description TEXT,
                resource VARCHAR(255) NOT NULL,
                action VARCHAR(255) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                metadata JSONB DEFAULT '{{}}',
                UNIQUE(resource, action)
            )",
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create permissions table: {e}")))?;

        // Create roles table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id UUID PRIMARY KEY,
                name VARCHAR(255) UNIQUE NOT NULL,
                description TEXT,
                parent_id UUID,
                level INTEGER NOT NULL DEFAULT 0,
                is_system BOOLEAN NOT NULL DEFAULT FALSE,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                metadata JSONB DEFAULT '{{}}',
                FOREIGN KEY (parent_id) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("roles"),
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles table: {e}")))?;

        // Create role_permissions junction table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                role_id UUID NOT NULL,
                permission_id UUID NOT NULL,
                granted BOOLEAN NOT NULL DEFAULT TRUE,
                granted_by UUID,
                granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                metadata JSONB DEFAULT '{{}}',
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (granted_by) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("role_permissions"),
                self.table_name("roles"),
                self.table_name("permissions"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create role_permissions table: {e}"))
        })?;

        // Create user_role_assignments junction table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                user_id UUID NOT NULL,
                role_id UUID NOT NULL,
                assigned_by UUID,
                assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                expires_at TIMESTAMP WITH TIME ZONE,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                metadata JSONB DEFAULT '{{}}',
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("user_role_assignments"),
                self.table_name("users"),
                self.table_name("roles"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create user_role_assignments table: {e}"))
        })?;

        // Create user_attributes table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID NOT NULL,
                key TEXT NOT NULL,
                value_type TEXT NOT NULL,
                value_string TEXT,
                value_number DOUBLE PRECISION,
                value_boolean BOOLEAN,
                value_json JSONB,
                created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                UNIQUE(user_id, key),
                FOREIGN KEY (user_id) REFERENCES {} (id) ON DELETE CASCADE
            )",
                self.table_name("user_attributes"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create user_attributes table: {e}")))?;

        // Create optimized indexes for RBAC performance

        // Permissions indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_permissions_name ON {}(name)",
                self.table_prefix,
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create permissions name index: {e}"))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_permissions_resource ON {}(resource)",
                self.table_prefix,
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create permissions resource index: {e}"))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_permissions_action ON {}(action)",
                self.table_prefix,
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create permissions action index: {e}"))
        })?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_permissions_resource_action ON {}(resource, action)", self.table_prefix, self.table_name("permissions")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create permissions resource_action index: {e}")))?;

        // Roles indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_roles_name ON {}(name)",
                self.table_prefix,
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles name index: {e}")))?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_roles_parent_id ON {}(parent_id)",
                self.table_prefix,
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles parent_id index: {e}")))?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_roles_level ON {}(level)",
                self.table_prefix,
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create roles level index: {e}")))?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_roles_active ON {}(is_active) WHERE is_active = TRUE", self.table_prefix, self.table_name("roles")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create roles active index: {e}")))?;

        // Role permissions indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_role_permissions_role_id ON {}(role_id)",
                self.table_prefix,
                self.table_name("role_permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create role_permissions role_id index: {e}"
            ))
        })?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_role_permissions_permission_id ON {}(permission_id)", self.table_prefix, self.table_name("role_permissions")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create role_permissions permission_id index: {e}")))?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_role_permissions_granted ON {}(granted) WHERE granted = TRUE", self.table_prefix, self.table_name("role_permissions")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create role_permissions granted index: {e}")))?;

        // User role assignments indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_role_assignments_user_id ON {}(user_id)",
                self.table_prefix,
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create user_role_assignments user_id index: {e}"
            ))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_role_assignments_role_id ON {}(role_id)",
                self.table_prefix,
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create user_role_assignments role_id index: {e}"
            ))
        })?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_user_role_assignments_active ON {}(is_active, expires_at) WHERE is_active = TRUE", self.table_prefix, self.table_name("user_role_assignments")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create user_role_assignments active index: {e}")))?;
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_user_role_assignments_expires ON {}(expires_at) WHERE expires_at IS NOT NULL", self.table_prefix, self.table_name("user_role_assignments")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create user_role_assignments expires index: {e}")))?;

        // Composite index for permission checks
        conn.execute(&format!("CREATE INDEX IF NOT EXISTS {}idx_permission_check_composite ON {}(user_id, is_active)", self.table_prefix, self.table_name("user_role_assignments")), &[]).await
            .map_err(|e| AuthError::internal(format!("Failed to create permission check composite index: {e}")))?;

        // User attributes indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_attributes_user_id ON {}(user_id)",
                self.table_prefix,
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create user_attributes user_id index: {e}"
            ))
        })?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_attributes_key ON {}(key)",
                self.table_prefix,
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!("Failed to create user_attributes key index: {e}"))
        })?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {}idx_user_attributes_value_type ON {}(value_type)",
                self.table_prefix,
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(format!(
                "Failed to create user_attributes value_type index: {e}"
            ))
        })?;

        Ok(())
    }

    async fn health_check(&self) -> Result<DatabaseHealth> {
        let start = std::time::Instant::now();

        match self.pool.get_connection().await {
            Ok(mut conn) => match conn.execute("SELECT 1", &[]).await {
                Ok(_) => {
                    let response_time = start.elapsed();
                    Ok(DatabaseHealth {
                        is_healthy: true,
                        response_time,
                        connection_count: Some(self.pool.active_connections()),
                        error_message: None,
                    })
                }
                Err(e) => Ok(DatabaseHealth {
                    is_healthy: false,
                    response_time: start.elapsed(),
                    connection_count: None,
                    error_message: Some(format!("Query failed: {e}")),
                }),
            },
            Err(e) => Ok(DatabaseHealth {
                is_healthy: false,
                response_time: start.elapsed(),
                connection_count: None,
                error_message: Some(format!("Connection failed: {e}")),
            }),
        }
    }

    async fn get_stats(&self) -> Result<DatabaseStats> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let row = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", self.table_name("users")),
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to count users: {e}")))?;
        let total_users = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;
        let row = conn
            .query_one(
                &format!(
                    "SELECT COUNT(*) FROM {} WHERE is_active = true AND expires_at > $1",
                    self.table_name("sessions")
                ),
                &[DatabaseValue::I64(current_time)],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to count active sessions: {e}")))?;
        let active_sessions = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        let row = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", self.table_name("sessions")),
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to count total sessions: {e}")))?;
        let total_sessions = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        Ok(DatabaseStats {
            total_users,
            active_sessions,
            total_sessions,
            database_size: None,
            connection_pool_size: Some(self.pool.max_connections()),
            active_connections: Some(self.pool.active_connections()),
        })
    }

    // User operations
    async fn create_user(&self, user: &User) -> Result<UserId> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let created_at = user.created_at.timestamp();
        let updated_at = user.updated_at.timestamp();
        let last_login = user.last_login.map(|dt| dt.timestamp());
        let metadata_json = serde_json::to_string(&user.metadata).unwrap_or_default();

        conn.execute(
            &format!(
                "INSERT INTO {} (id, username, email, password_hash, salt, created_at, updated_at, 
             last_login, role, status, is_verified, login_attempts, metadata) 
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)",
                self.table_name("users")
            ),
            &[
                DatabaseValue::Text(user.id.to_string()),
                DatabaseValue::Text(user.username.clone()),
                DatabaseValue::Text(user.email.clone()),
                DatabaseValue::Text(user.password_hash.clone()),
                DatabaseValue::Text(user.salt.clone()),
                DatabaseValue::I64(created_at),
                DatabaseValue::I64(updated_at),
                last_login
                    .map(DatabaseValue::I64)
                    .unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(user.role.to_string()),
                DatabaseValue::Text(user.status.to_string()),
                DatabaseValue::Bool(user.is_verified),
                DatabaseValue::I32(user.login_attempts),
                DatabaseValue::Text(metadata_json),
            ],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to create user: {e}")))?;

        Ok(user.id)
    }

    async fn get_user_by_id(&self, user_id: UserId) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, username, email, password_hash, salt, created_at, updated_at, last_login, role, status, is_verified, login_attempts, metadata FROM {} WHERE id = $1", self.table_name("users")),
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to query user by id: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let user = self.row_to_user(row.as_ref())?;
        Ok(Some(user))
    }

    async fn get_user_by_username(&self, username: &str) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!(
                    "SELECT id, username, email, password_hash, salt, created_at, updated_at, last_login, role, status, is_verified, login_attempts, metadata FROM {} WHERE username = $1",
                    self.table_name("users")
                ),
                &[DatabaseValue::Text(username.to_string())],
            )
            .await
            .map_err(|e| {
                AuthError::internal(format!("Failed to query user by username: {e}"))
            })?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let user = self.row_to_user(row.as_ref())?;
        Ok(Some(user))
    }

    async fn get_user_by_email(&self, email: &str) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!(
                    "SELECT id, username, email, password_hash, salt, created_at, updated_at, last_login, role, status, is_verified, login_attempts, metadata FROM {} WHERE email = $1",
                    self.table_name("users")
                ),
                &[DatabaseValue::Text(email.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to query user by email: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let user = self.row_to_user(row.as_ref())?;
        Ok(Some(user))
    }

    async fn update_user(&self, user: &User) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let updated_at = user.updated_at.timestamp();
        let last_login = user.last_login.map(|dt| dt.timestamp());
        let metadata_json = serde_json::to_string(&user.metadata).unwrap_or_default();

        conn.execute(
            &format!("UPDATE {} SET username = $1, email = $2, password_hash = $3, salt = $4, updated_at = $5, 
             last_login = $6, role = $7, status = $8, is_verified = $9, login_attempts = $10, metadata = $11 
             WHERE id = $12", self.table_name("users")),
            &[
                DatabaseValue::Text(user.username.clone()),
                DatabaseValue::Text(user.email.clone()),
                DatabaseValue::Text(user.password_hash.clone()),
                DatabaseValue::Text(user.salt.clone()),
                DatabaseValue::I64(updated_at),
                last_login.map(DatabaseValue::I64).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(user.role.to_string()),
                DatabaseValue::Text(user.status.to_string()),
                DatabaseValue::Bool(user.is_verified),
                DatabaseValue::I32(user.login_attempts),
                DatabaseValue::Text(metadata_json),
                DatabaseValue::Text(user.id.to_string()),
            ]
        ).await.map_err(|e| AuthError::internal(format!("Failed to update user: {e}")))?;

        Ok(())
    }

    async fn delete_user(&self, user_id: UserId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        conn.execute(
            &format!("DELETE FROM {} WHERE id = $1", self.table_name("users")),
            &[DatabaseValue::Text(user_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to delete user: {e}")))?;

        Ok(())
    }

    async fn list_users(&self, offset: u64, limit: u64) -> Result<Vec<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                &format!(
                    "SELECT * FROM {} ORDER BY created_at DESC LIMIT $1 OFFSET $2",
                    self.table_name("users")
                ),
                &[
                    DatabaseValue::I64(limit as i64),
                    DatabaseValue::I64(offset as i64),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to list users: {e}")))?;

        let mut users = Vec::new();
        for row in rows {
            let user = self.row_to_user(row.as_ref())?;
            users.push(user);
        }

        Ok(users)
    }

    async fn count_users(&self) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let row = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", self.table_name("users")),
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to count users: {e}")))?;

        let count = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        Ok(count)
    }

    // RBAC operations - Core methods implemented
    async fn create_role(&self, request: CreateRoleRequest) -> Result<Role> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let role_id = uuid::Uuid::new_v4();
        let now = chrono::Utc::now();

        let level = if let Some(parent_id) = &request.parent_id {
            let rows = conn
                .query_all(
                    &format!(
                        "SELECT level FROM {} WHERE id = $1",
                        self.table_name("roles")
                    ),
                    &[DatabaseValue::Text(parent_id.to_string())],
                )
                .await
                .map_err(|e| AuthError::internal(format!("Failed to get parent role: {e}")))?;
            if rows.is_empty() {
                return Err(AuthError::NotFound {
                    resource: "parent_role".to_string(),
                    id: parent_id.to_string(),
                });
            }
            let parent_level: i64 = rows[0]
                .get_by_index(0)
                .unwrap_or(DatabaseValue::I64(0))
                .to_string()
                .parse()
                .unwrap_or(0);
            parent_level + 1
        } else {
            0
        };

        conn.execute(&format!("INSERT INTO {} (id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)", self.table_name("roles")),
            &[DatabaseValue::Text(role_id.to_string()), DatabaseValue::Text(request.name.clone()),
              DatabaseValue::Text(request.description.clone().unwrap_or_default()),
              request.parent_id.as_ref().map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Text(String::new())),
              DatabaseValue::I64(level), DatabaseValue::Bool(false), DatabaseValue::Bool(true),
              DatabaseValue::I64(now.timestamp()), DatabaseValue::I64(now.timestamp()),
              request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Text(String::new()))])
            .await.map_err(|e| AuthError::internal(format!("Failed to create role: {e}")))?;

        Ok(Role {
            id: RoleId::from(role_id),
            name: request.name,
            description: request.description,
            parent_id: request.parent_id,
            level: level as u32,
            is_system: false,
            is_active: true,
            created_at: now,
            updated_at: now,
            metadata: request.metadata.unwrap_or(serde_json::Value::Null),
        })
    }

    async fn create_permission(&self, request: CreatePermissionRequest) -> Result<Permission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;
        let permission_id = uuid::Uuid::new_v4();
        let now = chrono::Utc::now();

        conn.execute(&format!("INSERT INTO {} (id, name, description, resource, action, created_at, updated_at, metadata) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)", self.table_name("permissions")),
            &[DatabaseValue::Text(permission_id.to_string()), DatabaseValue::Text(request.name.clone()),
              DatabaseValue::Text(request.description.clone().unwrap_or_default()),
              DatabaseValue::Text(request.resource.clone()), DatabaseValue::Text(request.action.clone()),
              DatabaseValue::I64(now.timestamp()), DatabaseValue::I64(now.timestamp()),
              request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Text(String::new()))])
            .await.map_err(|e| AuthError::internal(format!("Failed to create permission: {e}")))?;

        Ok(Permission {
            id: PermissionId::from(permission_id),
            name: request.name,
            description: request.description,
            resource: request.resource,
            action: request.action,
            created_at: now,
            updated_at: now,
            metadata: request.metadata.unwrap_or(serde_json::Value::Null),
        })
    }

    async fn assign_permission_to_role(
        &self,
        role_id: RoleId,
        permission_id: PermissionId,
        granted_by: Option<UserId>,
    ) -> Result<RolePermission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;
        let now = chrono::Utc::now();

        conn.execute(&format!("INSERT INTO {} (role_id, permission_id, granted, granted_by, granted_at) VALUES ($1, $2, $3, $4, $5) ON CONFLICT (role_id, permission_id) DO UPDATE SET granted = EXCLUDED.granted, granted_by = EXCLUDED.granted_by, granted_at = EXCLUDED.granted_at", self.table_name("role_permissions")),
            &[DatabaseValue::Text(role_id.to_string()), DatabaseValue::Text(permission_id.to_string()),
              DatabaseValue::Bool(true), granted_by.map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Text(String::new())),
              DatabaseValue::I64(now.timestamp())])
            .await.map_err(|e| AuthError::internal(format!("Failed to assign permission to role: {e}")))?;

        Ok(RolePermission {
            role_id,
            permission_id,
            granted: true,
            granted_by,
            granted_at: now,
            metadata: serde_json::Value::Null,
        })
    }

    async fn assign_role_to_user(
        &self,
        user_id: UserId,
        role_id: RoleId,
        assigned_by: Option<UserId>,
    ) -> Result<UserRoleAssignment> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;
        let now = chrono::Utc::now();

        conn.execute(&format!("INSERT INTO {} (user_id, role_id, is_active, assigned_by, assigned_at, expires_at) VALUES ($1, $2, $3, $4, $5, $6) ON CONFLICT (user_id, role_id) DO UPDATE SET is_active = EXCLUDED.is_active, assigned_by = EXCLUDED.assigned_by, assigned_at = EXCLUDED.assigned_at", self.table_name("user_role_assignments")),
            &[DatabaseValue::Text(user_id.to_string()), DatabaseValue::Text(role_id.to_string()),
              DatabaseValue::Bool(true), assigned_by.map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Text(String::new())),
              DatabaseValue::I64(now.timestamp()), DatabaseValue::Text(String::new())])
            .await.map_err(|e| AuthError::internal(format!("Failed to assign role to user: {e}")))?;

        Ok(UserRoleAssignment {
            user_id,
            role_id,
            is_active: true,
            assigned_by,
            assigned_at: now,
            expires_at: None,
            metadata: serde_json::Value::Null,
        })
    }

    async fn has_permission(&self, user_id: UserId, resource: &str, action: &str) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Check permission through role hierarchy using recursive CTE
        // This query checks permissions from direct role assignments and inherited from parent roles
        let rows = conn
            .query_all(
                &format!(
                    "WITH RECURSIVE role_hierarchy AS (
                -- Base case: direct user role assignments
                SELECT r.id, r.parent_id, r.level
                FROM {} r
                JOIN {} ura ON r.id = ura.role_id
                WHERE ura.user_id = $1 
                  AND ura.is_active = TRUE 
                  AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
                
                UNION ALL
                
                -- Recursive case: parent roles
                SELECT r.id, r.parent_id, r.level
                FROM {} r
                JOIN role_hierarchy rh ON r.id = rh.parent_id
                WHERE r.is_active = TRUE
            )
            SELECT COUNT(*) FROM role_hierarchy rh
            JOIN {} rp ON rh.id = rp.role_id
            JOIN {} p ON rp.permission_id = p.id
            WHERE rp.granted = TRUE
              AND p.resource = $2 
              AND p.action = $3",
                    self.table_name("roles"),
                    self.table_name("user_role_assignments"),
                    self.table_name("roles"),
                    self.table_name("role_permissions"),
                    self.table_name("permissions")
                ),
                &[
                    DatabaseValue::Text(user_id.to_string()),
                    DatabaseValue::Text(resource.to_string()),
                    DatabaseValue::Text(action.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check permission: {e}")))?;

        if rows.is_empty() {
            return Ok(false);
        }
        let count: i64 = rows[0]
            .get_by_index(0)
            .unwrap_or(DatabaseValue::I64(0))
            .to_string()
            .parse()
            .unwrap_or(0);
        Ok(count > 0)
    }

    // Placeholder implementations for remaining methods
    async fn get_role(&self, role_id: RoleId) -> Result<Option<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn.query_all(
            &format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata 
             FROM {} WHERE id = $1", self.table_name("roles")),
            &[DatabaseValue::Text(role_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to get role: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let role = Role {
            id: RoleId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            parent_id: {
                let parent_str = row.get_by_index(3).unwrap().to_string();
                if parent_str.is_empty() || parent_str == "null" {
                    None
                } else {
                    Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                }
            },
            level: row
                .get_by_index(4)
                .unwrap()
                .to_string()
                .parse()
                .unwrap_or(0),
            is_system: match row.get_by_index(5).unwrap() {
                DatabaseValue::Bool(b) => b,
                _ => false,
            },
            is_active: match row.get_by_index(6).unwrap() {
                DatabaseValue::Bool(b) => b,
                _ => true,
            },
            created_at: {
                let timestamp: i64 = row
                    .get_by_index(7)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            updated_at: {
                let timestamp: i64 = row
                    .get_by_index(8)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            metadata: {
                let meta_str = row.get_by_index(9).unwrap().to_string();
                if meta_str.is_empty() || meta_str == "null" {
                    serde_json::Value::Null
                } else {
                    serde_json::from_str(&meta_str).unwrap_or(serde_json::Value::String(meta_str))
                }
            },
        };

        Ok(Some(role))
    }
    async fn get_role_by_name(&self, name: &str) -> Result<Option<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn.query_all(
            &format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata 
             FROM {} WHERE name = $1", self.table_name("roles")),
            &[DatabaseValue::Text(name.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to get role by name: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let role = Role {
            id: RoleId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            parent_id: {
                let parent_str = row.get_by_index(3).unwrap().to_string();
                if parent_str.is_empty() || parent_str == "null" {
                    None
                } else {
                    Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                }
            },
            level: row
                .get_by_index(4)
                .unwrap()
                .to_string()
                .parse()
                .unwrap_or(0),
            is_system: match row.get_by_index(5).unwrap() {
                DatabaseValue::Bool(b) => b,
                _ => false,
            },
            is_active: match row.get_by_index(6).unwrap() {
                DatabaseValue::Bool(b) => b,
                _ => true,
            },
            created_at: {
                let timestamp: i64 = row
                    .get_by_index(7)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            updated_at: {
                let timestamp: i64 = row
                    .get_by_index(8)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            metadata: {
                let meta_str = row.get_by_index(9).unwrap().to_string();
                if meta_str.is_empty() || meta_str == "null" {
                    serde_json::Value::Null
                } else {
                    serde_json::from_str(&meta_str).unwrap_or(serde_json::Value::String(meta_str))
                }
            },
        };

        Ok(Some(role))
    }
    async fn update_role(&self, role_id: RoleId, request: CreateRoleRequest) -> Result<Role> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let now = chrono::Utc::now().timestamp();

        // Calculate level based on parent role if provided
        let level = if let Some(parent_id) = &request.parent_id {
            let rows = conn
                .query_all(
                    &format!(
                        "SELECT level FROM {} WHERE id = $1",
                        self.table_name("roles")
                    ),
                    &[DatabaseValue::Text(parent_id.to_string())],
                )
                .await
                .map_err(|e| AuthError::internal(format!("Failed to get parent role: {e}")))?;

            if rows.is_empty() {
                return Err(AuthError::NotFound {
                    resource: "parent_role".to_string(),
                    id: parent_id.to_string(),
                });
            }

            let parent_level: i64 = rows[0]
                .get_by_index(0)
                .unwrap_or(DatabaseValue::I64(0))
                .to_string()
                .parse::<i64>()
                .map_err(|_| AuthError::internal("Invalid parent level"))?;

            parent_level + 1
        } else {
            0
        };

        conn.execute(
            &format!("UPDATE {} SET name = $1, description = $2, parent_id = $3, level = $4, updated_at = $5, metadata = $6 WHERE id = $7", self.table_name("roles")),
            &[
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                request.parent_id.as_ref().map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(level),
                DatabaseValue::I64(now),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(role_id.to_string()),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("unique constraint") || e.to_string().contains("duplicate key") {
                AuthError::Validation {
                    message: format!("Role name '{}' already exists", request.name),
                    field: Some("name".to_string()),
                }
            } else {
                AuthError::internal(format!("Failed to update role: {e}"))
            }
        })?;

        // Return the updated role
        self.get_role(role_id)
            .await?
            .ok_or_else(|| AuthError::NotFound {
                resource: "role".to_string(),
                id: role_id.to_string(),
            })
    }
    async fn delete_role(&self, role_id: RoleId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Check if role exists first
        let role_exists = conn
            .query_all(
                &format!("SELECT id FROM {} WHERE id = $1", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check role existence: {e}")))?;

        if role_exists.is_empty() {
            return Ok(false); // Role doesn't exist
        }

        // Check if role has child roles (prevent deletion if it has children)
        let child_roles = conn
            .query_all(
                &format!(
                    "SELECT id FROM {} WHERE parent_id = $1",
                    self.table_name("roles")
                ),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to check child roles: {e}")))?;

        if !child_roles.is_empty() {
            return Err(AuthError::Validation {
                message: "Cannot delete role that has child roles".to_string(),
                field: Some("parent_id".to_string()),
            });
        }

        // Delete the role (CASCADE will handle role_permissions and user_role_assignments)
        let result = conn
            .execute(
                &format!("DELETE FROM {} WHERE id = $1", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to delete role: {e}")))?;

        Ok(result > 0)
    }
    async fn list_roles(&self, filter: RoleFilter) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let mut query = format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata FROM {}", self.table_name("roles"));
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_count = 0;

        // Apply filters
        if let Some(name) = &filter.name {
            param_count += 1;
            conditions.push(format!("name ILIKE ${param_count}"));
            params.push(DatabaseValue::Text(format!("%{name}%")));
        }

        if let Some(parent_id) = &filter.parent_id {
            param_count += 1;
            conditions.push(format!("parent_id = ${param_count}"));
            params.push(DatabaseValue::Text(parent_id.to_string()));
        }

        if let Some(level) = filter.level {
            param_count += 1;
            conditions.push(format!("level = ${param_count}"));
            params.push(DatabaseValue::I64(level as i64));
        }

        if let Some(is_system) = filter.is_system {
            param_count += 1;
            conditions.push(format!("is_system = ${param_count}"));
            params.push(DatabaseValue::Bool(is_system));
        }

        if let Some(is_active) = filter.is_active {
            param_count += 1;
            conditions.push(format!("is_active = ${param_count}"));
            params.push(DatabaseValue::Bool(is_active));
        }

        if !conditions.is_empty() {
            query.push_str(" WHERE ");
            query.push_str(&conditions.join(" AND "));
        }

        // Add ordering
        query.push_str(" ORDER BY level ASC, name ASC");

        // Add pagination
        if let Some(limit) = filter.limit {
            param_count += 1;
            query.push_str(&format!(" LIMIT ${param_count}"));
            params.push(DatabaseValue::I64(limit as i64));

            if let Some(offset) = filter.offset {
                param_count += 1;
                query.push_str(&format!(" OFFSET ${param_count}"));
                params.push(DatabaseValue::I64(offset as i64));
            }
        }

        let rows = conn
            .query_all(&query, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to list roles: {e}")))?;

        let mut roles = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    let parent_str = row.get_by_index(3).unwrap().to_string();
                    if parent_str.is_empty() || parent_str == "null" {
                        None
                    } else {
                        Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: match row.get_by_index(5).unwrap() {
                    DatabaseValue::Bool(b) => b,
                    _ => false,
                },
                is_active: match row.get_by_index(6).unwrap() {
                    DatabaseValue::Bool(b) => b,
                    _ => true,
                },
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(9).unwrap().to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            roles.push(role);
        }

        Ok(roles)
    }
    async fn get_permission(&self, permission_id: PermissionId) -> Result<Option<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                "SELECT id, name, description, resource, action, created_at, updated_at, metadata 
             FROM permissions WHERE id = $1",
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get permission: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let permission = Permission {
            id: PermissionId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            resource: row.get_by_index(3).unwrap().to_string(),
            action: row.get_by_index(4).unwrap().to_string(),
            created_at: {
                let timestamp: i64 = row
                    .get_by_index(5)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            updated_at: {
                let timestamp: i64 = row
                    .get_by_index(6)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            metadata: {
                let meta_str = row.get_by_index(7).unwrap().to_string();
                if meta_str.is_empty() || meta_str == "null" {
                    serde_json::Value::Null
                } else {
                    serde_json::from_str(&meta_str).unwrap_or(serde_json::Value::String(meta_str))
                }
            },
        };

        Ok(Some(permission))
    }
    async fn get_permission_by_key(
        &self,
        resource: &str,
        action: &str,
    ) -> Result<Option<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                "SELECT id, name, description, resource, action, created_at, updated_at, metadata 
             FROM permissions WHERE resource = $1 AND action = $2",
                &[
                    DatabaseValue::Text(resource.to_string()),
                    DatabaseValue::Text(action.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get permission by key: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let permission = Permission {
            id: PermissionId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            resource: row.get_by_index(3).unwrap().to_string(),
            action: row.get_by_index(4).unwrap().to_string(),
            created_at: {
                let timestamp: i64 = row
                    .get_by_index(5)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            updated_at: {
                let timestamp: i64 = row
                    .get_by_index(6)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
            },
            metadata: {
                let meta_str = row.get_by_index(7).unwrap().to_string();
                if meta_str.is_empty() || meta_str == "null" {
                    serde_json::Value::Null
                } else {
                    serde_json::from_str(&meta_str).unwrap_or(serde_json::Value::String(meta_str))
                }
            },
        };

        Ok(Some(permission))
    }
    async fn update_permission(
        &self,
        permission_id: PermissionId,
        request: CreatePermissionRequest,
    ) -> Result<Permission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let now = chrono::Utc::now().timestamp();

        conn.execute(
            "UPDATE permissions SET name = $1, description = $2, resource = $3, action = $4, updated_at = $5, metadata = $6 WHERE id = $7",
            &[
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                DatabaseValue::Text(request.resource.clone()),
                DatabaseValue::Text(request.action.clone()),
                DatabaseValue::I64(now),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(permission_id.to_string()),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("unique constraint") || e.to_string().contains("duplicate key") {
                AuthError::Conflict {
                    resource: "permission".to_string(),
                    field: "resource_action".to_string(),
                    value: format!("{}:{}", request.resource, request.action),
                }
            } else {
                AuthError::internal(format!("Failed to update permission: {e}"))
            }
        })?;

        // Return the updated permission
        self.get_permission(permission_id)
            .await?
            .ok_or_else(|| AuthError::NotFound {
                resource: "permission".to_string(),
                id: permission_id.to_string(),
            })
    }
    async fn delete_permission(&self, permission_id: PermissionId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Check if permission exists first
        let permission_exists = conn
            .query_all(
                "SELECT id FROM permissions WHERE id = $1",
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| {
                AuthError::internal(format!("Failed to check permission existence: {e}"))
            })?;

        if permission_exists.is_empty() {
            return Ok(false); // Permission doesn't exist
        }

        // Delete the permission (CASCADE will handle role_permissions)
        let result = conn
            .execute(
                "DELETE FROM permissions WHERE id = $1",
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to delete permission: {e}")))?;

        Ok(result > 0)
    }
    async fn list_permissions(&self, filter: PermissionFilter) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let mut query = "SELECT id, name, description, resource, action, created_at, updated_at, metadata FROM permissions".to_string();
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_count = 0;

        // Apply filters
        if let Some(name) = &filter.name {
            param_count += 1;
            conditions.push(format!("name ILIKE ${param_count}"));
            params.push(DatabaseValue::Text(format!("%{name}%")));
        }

        if let Some(resource) = &filter.resource {
            param_count += 1;
            conditions.push(format!("resource = ${param_count}"));
            params.push(DatabaseValue::Text(resource.clone()));
        }

        if let Some(action) = &filter.action {
            param_count += 1;
            conditions.push(format!("action = ${param_count}"));
            params.push(DatabaseValue::Text(action.clone()));
        }

        if !conditions.is_empty() {
            query.push_str(" WHERE ");
            query.push_str(&conditions.join(" AND "));
        }

        // Add ordering
        query.push_str(" ORDER BY resource ASC, action ASC, name ASC");

        // Add pagination
        if let Some(limit) = filter.limit {
            param_count += 1;
            query.push_str(&format!(" LIMIT ${param_count}"));
            params.push(DatabaseValue::I64(limit as i64));

            if let Some(offset) = filter.offset {
                param_count += 1;
                query.push_str(&format!(" OFFSET ${param_count}"));
                params.push(DatabaseValue::I64(offset as i64));
            }
        }

        let rows = conn
            .query_all(&query, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to list permissions: {e}")))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(7).unwrap().to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }
    async fn revoke_permission_from_role(
        &self,
        role_id: RoleId,
        permission_id: PermissionId,
    ) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let result = conn
            .execute(
                "DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = $2",
                &[
                    DatabaseValue::Text(role_id.to_string()),
                    DatabaseValue::Text(permission_id.to_string()),
                ],
            )
            .await
            .map_err(|e| {
                AuthError::internal(format!("Failed to revoke permission from role: {e}"))
            })?;

        Ok(result > 0)
    }
    async fn get_role_permissions(&self, role_id: RoleId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                "SELECT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
                 FROM permissions p
                 JOIN role_permissions rp ON p.id = rp.permission_id
                 WHERE rp.role_id = $1 AND rp.granted = TRUE
                 ORDER BY p.resource ASC, p.action ASC, p.name ASC",
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get role permissions: {e}")))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(7).unwrap().to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }
    async fn revoke_role_from_user(&self, user_id: UserId, role_id: RoleId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let result = conn
            .execute(
                "DELETE FROM user_role_assignments WHERE user_id = $1 AND role_id = $2",
                &[
                    DatabaseValue::Text(user_id.to_string()),
                    DatabaseValue::Text(role_id.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to revoke role from user: {e}")))?;

        Ok(result > 0)
    }
    async fn get_user_roles(&self, user_id: UserId) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                "SELECT r.id, r.name, r.description, r.parent_id, r.level, r.is_system, r.is_active, r.created_at, r.updated_at, r.metadata 
                 FROM roles r
                 JOIN user_role_assignments ura ON r.id = ura.role_id
                 WHERE ura.user_id = $1 AND ura.is_active = TRUE AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
                 ORDER BY r.level ASC, r.name ASC",
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get user roles: {e}")))?;

        let mut roles = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    let parent_str = row.get_by_index(3).unwrap().to_string();
                    if parent_str.is_empty() || parent_str == "null" {
                        None
                    } else {
                        Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: match row.get_by_index(5).unwrap() {
                    DatabaseValue::Bool(b) => b,
                    _ => false,
                },
                is_active: match row.get_by_index(6).unwrap() {
                    DatabaseValue::Bool(b) => b,
                    _ => true,
                },
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(9).unwrap().to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            roles.push(role);
        }

        Ok(roles)
    }
    async fn get_users_with_role(&self, role_id: RoleId) -> Result<Vec<UserId>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                "SELECT user_id FROM user_role_assignments 
                 WHERE role_id = $1 AND is_active = TRUE AND (expires_at IS NULL OR expires_at > NOW())
                 ORDER BY assigned_at ASC",
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get users with role: {e}")))?;

        let mut user_ids = Vec::new();
        for row in rows {
            let user_id = UserId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            );
            user_ids.push(user_id);
        }

        Ok(user_ids)
    }
    async fn check_permission(&self, check: PermissionCheck) -> Result<PermissionResult> {
        let has_permission = self
            .has_permission(check.user_id, &check.resource, &check.action)
            .await?;

        // Get matched permissions if access is granted
        let matched_permissions = if has_permission {
            self.get_user_permissions(check.user_id)
                .await?
                .into_iter()
                .filter(|p| p.resource == check.resource && p.action == check.action)
                .collect()
        } else {
            Vec::new()
        };

        // Get effective roles for the user
        let effective_roles = self.get_user_roles(check.user_id).await?;

        Ok(PermissionResult {
            allowed: has_permission,
            reason: if has_permission {
                Some("Permission granted through role assignment".to_string())
            } else {
                Some("Permission denied - no matching role permissions".to_string())
            },
            matched_permissions,
            effective_roles,
        })
    }
    async fn get_user_permissions(&self, user_id: UserId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                "SELECT DISTINCT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
                 FROM permissions p
                 JOIN role_permissions rp ON p.id = rp.permission_id
                 JOIN user_role_assignments ura ON rp.role_id = ura.role_id
                 WHERE ura.user_id = $1 
                   AND ura.is_active = TRUE 
                   AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
                   AND rp.granted = TRUE
                 ORDER BY p.resource ASC, p.action ASC, p.name ASC",
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get user permissions: {e}")))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(7).unwrap().to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }
    async fn get_role_hierarchy(&self, role_id: RoleId) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Use recursive CTE to get the complete role hierarchy
        // PostgreSQL has excellent support for recursive CTEs
        let rows = conn
            .query_all(
                "WITH RECURSIVE role_hierarchy AS (
                    -- Base case: start with the given role
                    SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata, 0 as depth
                    FROM roles 
                    WHERE id = $1 AND is_active = TRUE
                    
                    UNION ALL
                    
                    -- Recursive case: get parent roles
                    SELECT r.id, r.name, r.description, r.parent_id, r.level, r.is_system, r.is_active, r.created_at, r.updated_at, r.metadata, rh.depth + 1
                    FROM roles r
                    INNER JOIN role_hierarchy rh ON r.id = rh.parent_id
                    WHERE r.is_active = TRUE AND rh.depth < 10
                )
                SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata
                FROM role_hierarchy
                ORDER BY depth ASC, level ASC",
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get role hierarchy: {e}")))?;

        let mut hierarchy = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    let parent_str = row.get_by_index(3).unwrap().to_string();
                    if parent_str.is_empty() || parent_str == "null" {
                        None
                    } else {
                        Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: match row.get_by_index(5).unwrap() {
                    DatabaseValue::Bool(b) => b,
                    _ => false,
                },
                is_active: match row.get_by_index(6).unwrap() {
                    DatabaseValue::Bool(b) => b,
                    _ => true,
                },
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(9).unwrap().to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            hierarchy.push(role);
        }

        Ok(hierarchy)
    }
    async fn get_effective_permissions(&self, user_id: UserId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Get all permissions for the user including inherited permissions from role hierarchy
        // This query gets permissions from direct role assignments and parent roles using recursive CTE
        let rows = conn
            .query_all(
                "WITH RECURSIVE role_hierarchy AS (
                    -- Base case: direct user role assignments
                    SELECT r.id, r.parent_id, r.level
                    FROM roles r
                    JOIN user_role_assignments ura ON r.id = ura.role_id
                    WHERE ura.user_id = $1 
                      AND ura.is_active = TRUE 
                      AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
                    
                    UNION ALL
                    
                    -- Recursive case: parent roles
                    SELECT r.id, r.parent_id, r.level
                    FROM roles r
                    JOIN role_hierarchy rh ON r.id = rh.parent_id
                    WHERE r.is_active = TRUE
                )
                SELECT DISTINCT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
                FROM permissions p
                JOIN role_permissions rp ON p.id = rp.permission_id
                JOIN role_hierarchy rh ON rp.role_id = rh.id
                WHERE rp.granted = TRUE
                ORDER BY p.resource ASC, p.action ASC, p.name ASC",
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get effective permissions: {e}")))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let timestamp: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                updated_at: {
                    let timestamp: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(timestamp, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(7).unwrap().to_string();
                    if meta_str.is_empty() || meta_str == "null" {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }

    async fn begin_transaction(&self) -> Result<Box<dyn DatabaseTransaction>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let native_transaction = conn
            .begin_transaction()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to begin transaction: {e}")))?;

        Ok(Box::new(
            crate::database::transaction_bridge::TransactionBridge::new(native_transaction),
        ))
    }

    // User attributes operations
    async fn set_attribute(
        &self,
        user_id: UserId,
        request: UserAttributeRequest,
    ) -> Result<UserAttribute> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let attribute_id = uuid::Uuid::new_v4();
        let user_id_uuid = uuid::Uuid::from(user_id);
        let now = chrono::Utc::now();

        // Determine value type and column values
        let (value_type, value_string, value_number, value_boolean, value_json) =
            match &request.value {
                UserAttributeValue::String(s) => ("string", Some(s.clone()), None, None, None),
                UserAttributeValue::Number(n) => ("number", None, Some(*n), None, None),
                UserAttributeValue::Integer(i) => ("number", None, Some(*i as f64), None, None),
                UserAttributeValue::Boolean(b) => ("boolean", None, None, Some(*b), None),
                UserAttributeValue::Json(j) => (
                    "json",
                    None,
                    None,
                    None,
                    Some(serde_json::to_string(j).unwrap_or_default()),
                ),
                UserAttributeValue::Array(a) => (
                    "json",
                    None,
                    None,
                    None,
                    Some(serde_json::to_string(a).unwrap_or_default()),
                ),
            };

        // Use INSERT ... ON CONFLICT for upsert behavior
        let sql = "INSERT INTO user_attributes 
                   (id, user_id, key, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at)
                   VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 
                           COALESCE((SELECT created_at FROM user_attributes WHERE user_id = $9 AND key = $10), $11),
                           $12)
                   ON CONFLICT (user_id, key) 
                   DO UPDATE SET 
                       value_type = EXCLUDED.value_type,
                       value_string = EXCLUDED.value_string,
                       value_number = EXCLUDED.value_number,
                       value_boolean = EXCLUDED.value_boolean,
                       value_json = EXCLUDED.value_json,
                       updated_at = EXCLUDED.updated_at";

        let params = vec![
            DatabaseValue::Text(attribute_id.to_string()),
            DatabaseValue::Text(user_id_uuid.to_string()),
            DatabaseValue::Text(request.key.clone()),
            DatabaseValue::Text(value_type.to_string()),
            value_string
                .map(DatabaseValue::Text)
                .unwrap_or(DatabaseValue::Null),
            value_number
                .map(DatabaseValue::F64)
                .unwrap_or(DatabaseValue::Null),
            value_boolean
                .map(DatabaseValue::Bool)
                .unwrap_or(DatabaseValue::Null),
            value_json
                .map(DatabaseValue::Text)
                .unwrap_or(DatabaseValue::Null),
            DatabaseValue::Text(user_id_uuid.to_string()),
            DatabaseValue::Text(request.key.clone()),
            DatabaseValue::Text(now.to_rfc3339()),
            DatabaseValue::Text(now.to_rfc3339()),
        ];

        conn.execute(sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to set user attribute: {e}")))?;

        // Return the created/updated attribute
        Ok(UserAttribute {
            id: crate::types::UserAttributeId::from(attribute_id),
            user_id,
            key: request.key,
            value: request.value,
            created_at: now,
            updated_at: now,
            metadata: request.metadata.unwrap_or_default(),
        })
    }

    async fn get_attribute(&self, user_id: UserId, key: &str) -> Result<Option<UserAttribute>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let sql = "SELECT id, user_id, key, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                   FROM user_attributes WHERE user_id = $1 AND key = $2";

        let params = vec![
            DatabaseValue::Text(uuid::Uuid::from(user_id).to_string()),
            DatabaseValue::Text(key.to_string()),
        ];

        let rows = conn
            .query_all(sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get user attribute: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        Ok(Some(self.row_to_user_attribute(row.as_ref())?))
    }

    async fn get_user_attributes(&self, user_id: UserId) -> Result<UserAttributes> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let sql = "SELECT id, user_id, key, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                   FROM user_attributes WHERE user_id = $1 ORDER BY key";

        let params = vec![DatabaseValue::Text(uuid::Uuid::from(user_id).to_string())];

        let rows = conn
            .query_all(sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get user attributes: {e}")))?;

        let mut attributes = std::collections::HashMap::new();
        let mut last_updated = chrono::Utc::now();

        for row in rows {
            let attr = self.row_to_user_attribute(row.as_ref())?;
            if attr.updated_at > last_updated {
                last_updated = attr.updated_at;
            }
            attributes.insert(attr.key.clone(), attr.value);
        }

        Ok(UserAttributes {
            user_id,
            attributes,
            last_updated,
        })
    }

    async fn delete_attribute(&self, user_id: UserId, key: &str) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let sql = "DELETE FROM user_attributes WHERE user_id = $1 AND key = $2";
        let params = vec![
            DatabaseValue::Text(uuid::Uuid::from(user_id).to_string()),
            DatabaseValue::Text(key.to_string()),
        ];

        let affected = conn
            .execute(sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to delete user attribute: {e}")))?;

        Ok(affected > 0)
    }

    async fn delete_user_attributes(&self, user_id: UserId) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let sql = &format!(
            "DELETE FROM {} WHERE user_id = $1",
            self.table_name("user_attributes")
        );
        let params = vec![DatabaseValue::Text(uuid::Uuid::from(user_id).to_string())];

        let affected = conn
            .execute(sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to delete user attributes: {e}")))?;

        Ok(affected as u64)
    }

    async fn update_attribute(
        &self,
        user_id: UserId,
        key: &str,
        value: UserAttributeValue,
    ) -> Result<UserAttribute> {
        // Use set_attribute for upsert behavior
        let request = UserAttributeRequest {
            key: key.to_string(),
            value,
            metadata: None,
        };
        self.set_attribute(user_id, request).await
    }

    async fn search_attributes(&self, filter: UserAttributeFilter) -> Result<Vec<UserAttribute>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let mut sql = "SELECT id, user_id, key, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                       FROM user_attributes WHERE 1=1".to_string();
        let mut params = Vec::new();
        let mut param_count = 0;

        if let Some(user_id) = filter.user_id {
            param_count += 1;
            sql.push_str(&format!(" AND user_id = ${param_count}"));
            params.push(DatabaseValue::Text(uuid::Uuid::from(user_id).to_string()));
        }

        if let Some(keys) = filter.keys {
            if !keys.is_empty() {
                let mut placeholders = Vec::new();
                for key in keys {
                    param_count += 1;
                    placeholders.push(format!("${param_count}"));
                    params.push(DatabaseValue::Text(key));
                }
                sql.push_str(&format!(" AND key IN ({})", placeholders.join(",")));
            }
        }

        if let Some(value_type) = filter.value_type {
            param_count += 1;
            sql.push_str(&format!(" AND value_type = ${param_count}"));
            params.push(DatabaseValue::Text(value_type));
        }

        if let Some(created_after) = filter.created_after {
            param_count += 1;
            sql.push_str(&format!(" AND created_at > ${param_count}"));
            params.push(DatabaseValue::Text(created_after.to_rfc3339()));
        }

        if let Some(created_before) = filter.created_before {
            param_count += 1;
            sql.push_str(&format!(" AND created_at < ${param_count}"));
            params.push(DatabaseValue::Text(created_before.to_rfc3339()));
        }

        sql.push_str(" ORDER BY created_at DESC");

        if let Some(limit) = filter.limit {
            param_count += 1;
            sql.push_str(&format!(" LIMIT ${param_count}"));
            params.push(DatabaseValue::I64(limit as i64));

            if let Some(offset) = filter.offset {
                param_count += 1;
                sql.push_str(&format!(" OFFSET ${param_count}"));
                params.push(DatabaseValue::I64(offset as i64));
            }
        }

        let rows = conn
            .query_all(&sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to search user attributes: {e}")))?;

        let mut results = Vec::new();
        for row in rows {
            results.push(self.row_to_user_attribute(row.as_ref())?);
        }

        Ok(results)
    }

    async fn find_users_by_attribute(
        &self,
        key: &str,
        value: &UserAttributeValue,
    ) -> Result<Vec<UserId>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let (sql, params) = match value {
            UserAttributeValue::String(s) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE key = $1 AND value_type = 'string' AND value_string = $2",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(s.clone())]
            ),
            UserAttributeValue::Number(n) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE key = $1 AND value_type = 'number' AND value_number = $2",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::F64(*n)]
            ),
            UserAttributeValue::Integer(i) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE key = $1 AND value_type = 'number' AND value_number = $2",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::F64(*i as f64)]
            ),
            UserAttributeValue::Boolean(b) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE key = $1 AND value_type = 'boolean' AND value_boolean = $2",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Bool(*b)]
            ),
            UserAttributeValue::Json(j) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE key = $1 AND value_type = 'json' AND value_json = $2",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(serde_json::to_string(j).unwrap_or_default())]
            ),
            UserAttributeValue::Array(a) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE key = $1 AND value_type = 'json' AND value_json = $2",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(serde_json::to_string(a).unwrap_or_default())]
            ),
        };

        let rows = conn
            .query_all(sql, &params)
            .await
            .map_err(|e| AuthError::internal(format!("Failed to find users by attribute: {e}")))?;

        let mut user_ids = Vec::new();
        for row in rows {
            if let Ok(DatabaseValue::Text(user_id_str)) = row.get_by_index(0) {
                if let Ok(uuid) = uuid::Uuid::parse_str(&user_id_str) {
                    user_ids.push(UserId::from(uuid));
                }
            }
        }

        Ok(user_ids)
    }

    async fn set_attributes(
        &self,
        user_id: UserId,
        attributes: std::collections::HashMap<String, UserAttributeValue>,
    ) -> Result<Vec<UserAttribute>> {
        let mut results = Vec::new();

        for (key, value) in attributes {
            let request = UserAttributeRequest {
                key,
                value,
                metadata: None,
            };
            let attr = self.set_attribute(user_id, request).await?;
            results.push(attr);
        }

        Ok(results)
    }

    // Session operations
    async fn create_session(&self, session: &Session) -> Result<SessionId> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let created_at = session.created_at.timestamp();
        let expires_at = session.expires_at.timestamp();
        let last_accessed = session.last_accessed.timestamp();
        let ip_address = session.ip_address.map(|ip| ip.to_string());
        let session_data_json = serde_json::to_string(&session.session_data).unwrap_or_default();
        let metadata_json = serde_json::to_string(&session.metadata).unwrap_or_default();

        conn.execute(
            "INSERT INTO sessions (id, user_id, token, refresh_token, expires_at, created_at, last_accessed, 
             ip_address, user_agent, is_active, session_data, metadata) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)",
            &[
                DatabaseValue::Text(session.id.to_string()),
                DatabaseValue::Text(session.user_id.to_string()),
                DatabaseValue::Text(session.token.clone()),
                session.refresh_token.as_ref().map(|t| DatabaseValue::Text(t.clone())).unwrap_or(DatabaseValue::Text(String::new())),
                DatabaseValue::I64(expires_at),
                DatabaseValue::I64(created_at),
                DatabaseValue::I64(last_accessed),
                ip_address.map(DatabaseValue::Text).unwrap_or(DatabaseValue::Text(String::new())),
                session.user_agent.as_ref().map(|ua| DatabaseValue::Text(ua.clone())).unwrap_or(DatabaseValue::Text(String::new())),
                DatabaseValue::Bool(session.is_active),
                DatabaseValue::Text(session_data_json),
                DatabaseValue::Text(metadata_json),
            ]
        ).await.map_err(|e| AuthError::internal(format!("Failed to create session: {e}")))?;

        Ok(session.id)
    }

    async fn get_session_by_id(&self, session_id: SessionId) -> Result<Option<Session>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn
            .query_all(
                "SELECT * FROM sessions WHERE id = $1",
                &[DatabaseValue::Text(session_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to query session by id: {e}")))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let session = self.row_to_session(row.as_ref())?;
        Ok(Some(session))
    }

    async fn update_session(&self, session: &Session) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let expires_at = session.expires_at.timestamp();
        let last_accessed = session.last_accessed.timestamp();
        let ip_address = session.ip_address.map(|ip| ip.to_string());
        let session_data_json = serde_json::to_string(&session.session_data).unwrap_or_default();
        let metadata_json = serde_json::to_string(&session.metadata).unwrap_or_default();

        conn.execute(
            "UPDATE sessions SET user_id = $1, token = $2, refresh_token = $3, expires_at = $4, last_accessed = $5, 
             ip_address = $6, user_agent = $7, is_active = $8, session_data = $9, metadata = $10 WHERE id = $11",
            &[
                DatabaseValue::Text(session.user_id.to_string()),
                DatabaseValue::Text(session.token.clone()),
                session.refresh_token.as_ref().map(|t| DatabaseValue::Text(t.clone())).unwrap_or(DatabaseValue::Text(String::new())),
                DatabaseValue::I64(expires_at),
                DatabaseValue::I64(last_accessed),
                ip_address.map(DatabaseValue::Text).unwrap_or(DatabaseValue::Text(String::new())),
                session.user_agent.as_ref().map(|ua| DatabaseValue::Text(ua.clone())).unwrap_or(DatabaseValue::Text(String::new())),
                DatabaseValue::Bool(session.is_active),
                DatabaseValue::Text(session_data_json),
                DatabaseValue::Text(metadata_json),
                DatabaseValue::Text(session.id.to_string()),
            ]
        ).await.map_err(|e| AuthError::internal(format!("Failed to update session: {e}")))?;

        Ok(())
    }

    async fn delete_session(&self, session_id: SessionId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        conn.execute(
            "DELETE FROM sessions WHERE id = $1",
            &[DatabaseValue::Text(session_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to delete session: {e}")))?;

        Ok(())
    }

    async fn delete_user_sessions(&self, user_id: UserId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        conn.execute(
            "DELETE FROM sessions WHERE user_id = $1",
            &[DatabaseValue::Text(user_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(format!("Failed to delete user sessions: {e}")))?;

        Ok(())
    }

    async fn list_user_sessions(&self, user_id: UserId) -> Result<Vec<Session>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let rows = conn.query_all("SELECT * FROM sessions WHERE user_id = $1 AND is_active = true ORDER BY last_accessed DESC",
            &[DatabaseValue::Text(user_id.to_string())])
            .await.map_err(|e| AuthError::internal(format!("Failed to list user sessions: {e}")))?;

        let mut sessions = Vec::new();
        for row in rows {
            let session = self.row_to_session(row.as_ref())?;
            sessions.push(session);
        }

        Ok(sessions)
    }

    async fn cleanup_expired_sessions(&self) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;

        let result = conn
            .execute(
                "DELETE FROM sessions WHERE expires_at < $1",
                &[DatabaseValue::I64(current_time)],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to cleanup expired sessions: {e}")))?;

        Ok(result)
    }
}

impl NativePostgresProvider {
    /// Search users using PostgreSQL full-text search
    pub async fn search_users(&self, query: &str, limit: u64) -> Result<Vec<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Use PostgreSQL's full-text search with to_tsvector and to_tsquery
        let rows = conn
            .query_all(
                "SELECT id, username, email, password_hash, salt, created_at, updated_at, 
                 last_login, role, status, is_verified, login_attempts, locked_until, metadata 
                 FROM users 
                 WHERE to_tsvector('english', username || ' ' || email || ' ' || COALESCE(metadata->>'bio', '')) 
                 @@ to_tsquery('english', $1)
                 ORDER BY ts_rank(to_tsvector('english', username || ' ' || email || ' ' || COALESCE(metadata->>'bio', '')), 
                                 to_tsquery('english', $1)) DESC
                 LIMIT $2",
                &[
                    DatabaseValue::Text(query.to_string()),
                    DatabaseValue::I64(limit as i64),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to search users: {e}")))?;

        let mut users = Vec::new();
        for row in rows {
            users.push(self.row_to_user(&*row)?);
        }

        Ok(users)
    }

    /// Query users by JSONB metadata fields
    pub async fn query_users_by_metadata(
        &self,
        json_path: &str,
        value: &str,
        limit: u64,
    ) -> Result<Vec<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Use PostgreSQL's JSONB operators for efficient querying
        let rows = conn
            .query_all(
                "SELECT id, username, email, password_hash, salt, created_at, updated_at, 
                 last_login, role, status, is_verified, login_attempts, locked_until, metadata 
                 FROM users 
                 WHERE metadata #>> $1 = $2
                 ORDER BY created_at DESC
                 LIMIT $3",
                &[
                    DatabaseValue::Text(json_path.to_string()),
                    DatabaseValue::Text(value.to_string()),
                    DatabaseValue::I64(limit as i64),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to query users by metadata: {e}")))?;

        let mut users = Vec::new();
        for row in rows {
            users.push(self.row_to_user(&*row)?);
        }

        Ok(users)
    }

    /// Query users with complex JSONB conditions
    pub async fn query_users_by_jsonb_condition(
        &self,
        condition: &str,
        params: &[DatabaseValue],
        limit: u64,
    ) -> Result<Vec<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Build dynamic query with JSONB conditions
        let query = format!(
            "SELECT id, username, email, password_hash, salt, created_at, updated_at, 
             last_login, role, status, is_verified, login_attempts, locked_until, metadata 
             FROM users 
             WHERE {}
             ORDER BY created_at DESC
             LIMIT ${}",
            condition,
            params.len() + 1
        );

        let mut query_params = params.to_vec();
        query_params.push(DatabaseValue::I64(limit as i64));

        let rows = conn.query_all(&query, &query_params).await.map_err(|e| {
            AuthError::internal(format!("Failed to query users with JSONB condition: {e}"))
        })?;

        let mut users = Vec::new();
        for row in rows {
            users.push(self.row_to_user(&*row)?);
        }

        Ok(users)
    }

    /// Get database connection statistics (PostgreSQL-specific)
    pub async fn get_connection_stats(&self) -> Result<PostgresConnectionStats> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection: {e}")))?;

        // Query PostgreSQL system tables for connection statistics
        let row = conn
            .query_one(
                "SELECT 
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
                    (SELECT count(*) FROM pg_stat_activity) as total_connections,
                    (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections",
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(format!("Failed to get connection stats: {e}")))?;

        let get_i64 = |idx: usize| -> i64 {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::I64(0)) {
                DatabaseValue::I64(i) => i,
                DatabaseValue::I32(i) => i as i64,
                _ => 0,
            }
        };

        Ok(PostgresConnectionStats {
            active_connections: get_i64(0) as u32,
            idle_connections: get_i64(1) as u32,
            total_connections: get_i64(2) as u32,
            max_connections: get_i64(3) as u32,
        })
    }

    /// Simulate connection failure and recovery for testing
    pub async fn simulate_connection_failure(&self) -> Result<ConnectionFailureSimulation> {
        let mut simulation = ConnectionFailureSimulation::new();

        // Step 1: Test normal connection
        simulation.log_step("Testing normal connection...");
        match self.health_check().await {
            Ok(health) => {
                simulation.log_step(&format!(
                    "Normal connection healthy: {}ms",
                    health.response_time.as_millis()
                ));
                simulation.initial_health = Some(health);
            }
            Err(e) => {
                simulation.log_step(&format!("Initial connection failed: {e}"));
                simulation
                    .errors
                    .push(format!("Initial connection failed: {e}"));
                return Ok(simulation);
            }
        }

        // Step 2: Simulate connection pool stress test
        simulation.log_step("Testing connection pool under stress...");
        let stress_result = self.test_connection_pool_stress().await;
        simulation.pool_stress_successful = stress_result;

        // Step 3: Simulate query timeout
        simulation.log_step("Testing query timeout handling...");
        let timeout_result = self.test_query_timeout().await;
        simulation.timeout_test_successful = timeout_result;

        // Step 4: Test connection recovery
        simulation.log_step("Testing connection recovery...");
        match self.health_check().await {
            Ok(health) => {
                simulation.log_step(&format!(
                    "Connection recovered: {}ms",
                    health.response_time.as_millis()
                ));
                simulation.recovery_health = Some(health);
                simulation.recovery_successful = true;
            }
            Err(e) => {
                simulation.log_step(&format!("Connection recovery failed: {e}"));
                simulation.errors.push(format!("Recovery failed: {e}"));
                simulation.recovery_successful = false;
            }
        }

        // Step 5: Test concurrent connections
        simulation.log_step("Testing concurrent connection handling...");
        let concurrent_result = self.test_concurrent_connections().await;
        simulation.concurrent_connections_successful = concurrent_result;

        simulation.log_step("Connection failure simulation completed");
        Ok(simulation)
    }

    /// Test connection pool under stress
    async fn test_connection_pool_stress(&self) -> bool {
        let max_connections = self.pool.max_connections();
        let mut connections = Vec::new();

        // Try to get multiple connections rapidly
        for _ in 0..max_connections {
            match self.pool.get_connection().await {
                Ok(conn) => connections.push(conn),
                Err(_) => return false,
            }
        }

        // Test that we can still get connections after releasing some
        drop(connections);
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        (self.pool.get_connection().await).is_ok()
    }

    /// Test query timeout handling
    async fn test_query_timeout(&self) -> bool {
        let timeout_duration = std::time::Duration::from_millis(50);

        let result = tokio::time::timeout(timeout_duration, async {
            let mut conn = self.pool.get_connection().await?;
            // Use a simple query instead of pg_sleep which might not be available
            conn.query_one("SELECT 1 as test_value", &[]).await
        })
        .await;

        match result {
            Ok(Ok(_)) => true,   // Query completed successfully
            Ok(Err(_)) => false, // Query failed
            Err(_) => true,      // Timeout occurred (which is expected behavior)
        }
    }

    /// Test concurrent connection handling
    async fn test_concurrent_connections(&self) -> bool {
        let concurrent_tasks = 5; // Reduced number for reliability
        let mut handles = Vec::new();

        for i in 0..concurrent_tasks {
            let pool = self.pool.clone();
            let handle = tokio::spawn(async move {
                match pool.get_connection().await {
                    Ok(mut conn) => (conn
                        .query_one("SELECT $1 as task_id", &[DatabaseValue::I32(i)])
                        .await)
                        .is_ok(),
                    Err(_) => false,
                }
            });
            handles.push(handle);
        }

        // Wait for all tasks and check if they all succeeded
        let mut all_successful = true;
        for handle in handles {
            match handle.await {
                Ok(success) => {
                    if !success {
                        all_successful = false;
                    }
                }
                Err(_) => all_successful = false,
            }
        }

        all_successful
    }
}

/// PostgreSQL-specific connection statistics
#[derive(Debug, Clone)]
pub struct PostgresConnectionStats {
    pub active_connections: u32,
    pub idle_connections: u32,
    pub total_connections: u32,
    pub max_connections: u32,
}

/// Comprehensive connection failure simulation results
#[derive(Debug, Clone)]
pub struct ConnectionFailureSimulation {
    pub steps: Vec<String>,
    pub initial_health: Option<DatabaseHealth>,
    pub recovery_health: Option<DatabaseHealth>,
    pub recovery_successful: bool,
    pub pool_stress_successful: bool,
    pub timeout_test_successful: bool,
    pub concurrent_connections_successful: bool,
    pub errors: Vec<String>,
    pub start_time: std::time::Instant,
}

impl ConnectionFailureSimulation {
    fn new() -> Self {
        Self {
            steps: Vec::new(),
            initial_health: None,
            recovery_health: None,
            recovery_successful: false,
            pool_stress_successful: false,
            timeout_test_successful: false,
            concurrent_connections_successful: false,
            errors: Vec::new(),
            start_time: std::time::Instant::now(),
        }
    }

    fn log_step(&mut self, step: &str) {
        let elapsed = self.start_time.elapsed();
        self.steps.push(format!("[{elapsed:?}] {step}"));
    }

    /// Get a summary of the simulation results
    pub fn summary(&self) -> String {
        let mut summary = String::new();
        summary.push_str("=== Connection Failure Simulation Summary ===\n");

        if let Some(ref health) = self.initial_health {
            summary.push_str(&format!(
                "Initial Health: {} ({}ms)\n",
                if health.is_healthy {
                    "Healthy"
                } else {
                    "Unhealthy"
                },
                health.response_time.as_millis()
            ));
        }

        summary.push_str(&format!(
            "Pool Stress Test: {}\n",
            if self.pool_stress_successful {
                "Passed"
            } else {
                "Failed"
            }
        ));

        summary.push_str(&format!(
            "Timeout Test: {}\n",
            if self.timeout_test_successful {
                "Passed"
            } else {
                "Failed"
            }
        ));

        summary.push_str(&format!(
            "Concurrent Connections: {}\n",
            if self.concurrent_connections_successful {
                "Passed"
            } else {
                "Failed"
            }
        ));

        summary.push_str(&format!(
            "Recovery: {}\n",
            if self.recovery_successful {
                "Successful"
            } else {
                "Failed"
            }
        ));

        if !self.errors.is_empty() {
            summary.push_str(&format!("Errors: {}\n", self.errors.len()));
            for error in &self.errors {
                summary.push_str(&format!("  - {error}\n"));
            }
        }

        let total_tests = 4; // pool stress, timeout, concurrent, recovery
        let passed_tests = [
            self.pool_stress_successful,
            self.timeout_test_successful,
            self.concurrent_connections_successful,
            self.recovery_successful,
        ]
        .iter()
        .filter(|&&x| x)
        .count();

        summary.push_str(&format!(
            "Overall: {passed_tests}/{total_tests} tests passed\n"
        ));
        summary
    }

    /// Check if all tests passed
    pub fn all_tests_passed(&self) -> bool {
        self.pool_stress_successful
            && self.timeout_test_successful
            && self.concurrent_connections_successful
            && self.recovery_successful
            && self.errors.is_empty()
    }
}
