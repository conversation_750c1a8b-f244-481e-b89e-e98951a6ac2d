//! Database connection management

use std::time::Duration;

use url::Url;

use crate::error::{AuthErro<PERSON>, Result};

/// Database connection configuration
#[derive(Debug, <PERSON>lone)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: Duration,
    pub idle_timeout: Option<Duration>,
    pub max_lifetime: Option<Duration>,
    pub test_before_acquire: bool,
    pub ssl_mode: SslMode,
}

/// SSL connection mode
#[derive(Debug, <PERSON>lone, Co<PERSON>, PartialEq, Eq)]
pub enum SslMode {
    Disable,
    Allow,
    Prefer,
    Require,
    VerifyCa,
    VerifyFull,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "sqlite::memory:".to_string(),
            max_connections: 10,
            min_connections: 1,
            connection_timeout: Duration::from_secs(30),
            idle_timeout: Some(Duration::from_secs(600)), // 10 minutes
            max_lifetime: Some(Duration::from_secs(1800)), // 30 minutes
            test_before_acquire: true,
            ssl_mode: SslMode::Prefer,
        }
    }
}

impl DatabaseConfig {
    /// Create a new database configuration
    pub fn new<S: Into<String>>(url: S) -> Self {
        Self {
            url: url.into(),
            ..Default::default()
        }
    }

    /// Set maximum number of connections
    pub fn max_connections(mut self, max: u32) -> Self {
        self.max_connections = max;
        self
    }

    /// Set minimum number of connections
    pub fn min_connections(mut self, min: u32) -> Self {
        self.min_connections = min;
        self
    }

    /// Set connection timeout
    pub fn connection_timeout(mut self, timeout: Duration) -> Self {
        self.connection_timeout = timeout;
        self
    }

    /// Set idle timeout
    pub fn idle_timeout(mut self, timeout: Option<Duration>) -> Self {
        self.idle_timeout = timeout;
        self
    }

    /// Set maximum connection lifetime
    pub fn max_lifetime(mut self, lifetime: Option<Duration>) -> Self {
        self.max_lifetime = lifetime;
        self
    }

    /// Set SSL mode
    pub fn ssl_mode(mut self, mode: SslMode) -> Self {
        self.ssl_mode = mode;
        self
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<()> {
        if self.max_connections == 0 {
            return Err(AuthError::validation(
                "max_connections must be greater than 0",
            ));
        }

        if self.min_connections > self.max_connections {
            return Err(AuthError::validation(
                "min_connections cannot be greater than max_connections",
            ));
        }

        // Validate URL format
        Url::parse(&self.url)
            .map_err(|e| AuthError::validation(format!("Invalid database URL: {e}")))?;

        Ok(())
    }

    /// Get the database type from the URL
    pub fn database_type(&self) -> Result<DatabaseType> {
        let url = Url::parse(&self.url)
            .map_err(|e| AuthError::validation(format!("Invalid database URL: {e}")))?;

        match url.scheme() {
            "sqlite" => Ok(DatabaseType::Sqlite),
            "postgres" | "postgresql" => Ok(DatabaseType::Postgres),
            "mysql" => Ok(DatabaseType::Mysql),
            scheme => Err(AuthError::validation(format!(
                "Unsupported database type: {scheme}"
            ))),
        }
    }
}

/// Supported database types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DatabaseType {
    Sqlite,
    Postgres,
    Mysql,
}
