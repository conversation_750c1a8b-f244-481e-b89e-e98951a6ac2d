//! Native MySQL database provider implementation

use async_trait::async_trait;

use crate::{
    database::{
        native::{
            common::traits::{DatabaseConnection, DatabasePool, DatabaseRow, DatabaseValue},
            mysql::MySqlPool,
        },
        DatabaseHealth, DatabaseProvider, DatabaseStats, DatabaseTransaction,
    },
    error::{AuthError, Result},
    types::{
        CreatePermissionRequest, CreateRoleRequest, Permission, PermissionCheck, PermissionFilter,
        PermissionId, PermissionResult, Role, RoleFilter, RoleId, RolePermission, Session,
        SessionId, User, UserAttribute, UserAttributeFilter, UserAttributeRequest,
        UserAttributeValue, UserAttributes, UserId, UserRoleAssignment,
    },
};

/// Native MySQL database provider
#[derive(Clone)]
pub struct NativeMysqlProvider {
    pool: MySqlPool,
    table_prefix: String,
}

impl NativeMysqlProvider {
    /// Create a new MySQL provider with the given connection string
    pub async fn new(connection_string: &str) -> Result<Self> {
        let pool = MySqlPool::new(connection_string, 10)
            .map_err(|e| AuthError::internal(&format!("Failed to create MySQL pool: {}", e)))?;

        Ok(Self {
            pool,
            table_prefix: String::new(),
        })
    }

    /// Create a new MySQL provider with the given connection string and table prefix.
    ///
    /// The table prefix is useful for:
    /// - Multi-tenant applications where each tenant has isolated tables
    /// - Running multiple instances of the application in the same database
    /// - Testing with separate table sets
    ///
    /// # Arguments
    /// * `connection_string` - MySQL connection string
    /// * `table_prefix` - Prefix to add to all table names (e.g., "tenant1_", "test_")
    ///
    /// # Example
    /// ```
    /// let provider = NativeMysqlProvider::new_with_prefix(
    ///     "mysql://user:pass@localhost/db",
    ///     "app1_".to_string()
    /// ).await?;
    /// // This will create tables like: app1_users, app1_sessions, etc.
    /// ```
    /// Create a new MySQL provider with the given connection string and table prefix
    pub async fn new_with_prefix(connection_string: &str, table_prefix: String) -> Result<Self> {
        if !table_prefix
            .chars()
            .all(|c| c.is_alphanumeric() || c == '_')
        {
            return Err(AuthError::Configuration {
                message: "Invalid characters in table_prefix. Only alphanumeric and underscores are allowed.".to_string(),
                field: Some("table_prefix".to_string()),
            });
        }
        let pool = MySqlPool::new(connection_string, 10)
            .map_err(|e| AuthError::internal(&format!("Failed to create MySQL pool: {}", e)))?;

        Ok(Self { pool, table_prefix })
    }

    /// Create a new MySQL provider with the given connection string and table prefix.
    ///
    /// The table prefix is useful for:
    /// - Multi-tenant applications where each tenant has isolated tables
    /// - Running multiple instances of the application in the same database
    /// - Testing with separate table sets
    ///
    /// # Arguments
    /// * `connection_string` - MySQL connection string
    /// * `table_prefix` - Prefix to add to all table names (e.g., "tenant1_", "test_")
    ///
    /// # Example
    /// ```
    /// let provider = NativeMysqlProvider::new_with_prefix(
    ///     "mysql://user:pass@localhost/db",
    ///     "app1_".to_string()
    /// ).await?;
    /// // This will create tables like: app1_users, app1_sessions, etc.
    /// ```
    fn table_name(&self, base_name: &str) -> String {
        format!("{}{}", self.table_prefix, base_name)
    }

    // Helper method to generate a safe index name that respects MySQL's 64-character limit.
    fn index_name(&self, base_name: &str) -> String {
        let full_name = format!("{}{}", self.table_prefix, base_name);
        if full_name.len() > 64 {
            // If the name is too long, truncate it and add a hash of the full name.
            // This ensures uniqueness while staying within the length limit.
            let mut hasher = std::collections::hash_map::DefaultHasher::new();
            std::hash::Hash::hash(&full_name, &mut hasher);
            let hash = std::hash::Hasher::finish(&hasher);
            let truncated_name = full_name.chars().take(50).collect::<String>();
            format!("{}_{:x}", truncated_name, hash)
                .chars()
                .take(64)
                .collect()
        } else {
            full_name
        }
    }

    // Helper method to convert database row to UserAttribute
    fn row_to_user_attribute(
        &self,
        row: &dyn crate::database::native::common::traits::DatabaseRow,
    ) -> Result<UserAttribute> {
        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                _ => String::new(),
            }
        };

        let get_f64 = |idx: usize| -> Option<f64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::F64(f) => Some(f),
                DatabaseValue::I32(i) => Some(i as f64),
                DatabaseValue::I64(i) => Some(i as f64),
                _ => None,
            }
        };

        let get_bool = |idx: usize| -> Option<bool> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Bool(b) => Some(b),
                DatabaseValue::I32(i) => Some(i != 0),
                DatabaseValue::I64(i) => Some(i != 0),
                _ => None,
            }
        };

        let id_str = get_text(0);
        let user_id_str = get_text(1);
        let key = get_text(2);
        let value_type = get_text(3);
        let value_string = get_text(4);
        let value_number = get_f64(5);
        let value_boolean = get_bool(6);
        let value_json = get_text(7);
        let created_at_str = get_text(8);
        let updated_at_str = get_text(9);

        // Parse UUIDs
        let id = uuid::Uuid::parse_str(&id_str)
            .map_err(|_| AuthError::internal("Invalid attribute ID"))?;
        let user_id = uuid::Uuid::parse_str(&user_id_str)
            .map_err(|_| AuthError::internal("Invalid user ID"))?;

        // Parse timestamps
        let created_at = chrono::DateTime::parse_from_str(&created_at_str, "%Y-%m-%d %H:%M:%S")
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .unwrap_or_else(|_| chrono::Utc::now());
        let updated_at = chrono::DateTime::parse_from_str(&updated_at_str, "%Y-%m-%d %H:%M:%S")
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .unwrap_or_else(|_| chrono::Utc::now());

        // Reconstruct the value based on type
        let value = match value_type.as_str() {
            "string" => UserAttributeValue::String(value_string),
            "number" => {
                if let Some(n) = value_number {
                    // Check if it's an integer value
                    if n.fract() == 0.0 && n >= i64::MIN as f64 && n <= i64::MAX as f64 {
                        UserAttributeValue::Integer(n as i64)
                    } else {
                        UserAttributeValue::Number(n)
                    }
                } else {
                    UserAttributeValue::Number(0.0)
                }
            }
            "boolean" => UserAttributeValue::Boolean(value_boolean.unwrap_or(false)),
            "json" => {
                if value_json.is_empty() {
                    UserAttributeValue::Json(serde_json::Value::Null)
                } else {
                    match serde_json::from_str(&value_json) {
                        Ok(serde_json::Value::Array(arr)) => UserAttributeValue::Array(arr),
                        Ok(json_val) => UserAttributeValue::Json(json_val),
                        Err(_) => UserAttributeValue::String(value_json),
                    }
                }
            }
            _ => UserAttributeValue::String(value_string),
        };

        Ok(UserAttribute {
            id: crate::types::UserAttributeId::from(id),
            user_id: UserId::from(user_id),
            key,
            value,
            created_at,
            updated_at,
            metadata: serde_json::Value::Object(serde_json::Map::new()),
        })
    }

    fn row_to_user(&self, row: &dyn DatabaseRow) -> Result<User> {
        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                _ => String::new(),
            }
        };

        let get_i64 = |idx: usize| -> Option<i64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::I64(i) => Some(i),
                DatabaseValue::I32(i) => Some(i as i64),
                _ => None,
            }
        };

        Ok(User {
            id: get_text(0)
                .parse::<uuid::Uuid>()
                .map(UserId::from)
                .unwrap_or_default(),
            username: get_text(1),
            email: get_text(2),
            password_hash: get_text(3),
            salt: get_text(4),
            role: get_text(8).parse().unwrap_or(crate::types::UserRole::User),
            status: get_text(9)
                .parse()
                .unwrap_or(crate::types::UserStatus::Active),
            created_at: get_i64(5)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            updated_at: get_i64(6)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            last_login: get_i64(7).and_then(|ts| chrono::DateTime::from_timestamp(ts, 0)),
            is_active: true, // Default for compatibility
            is_verified: get_i64(10).unwrap_or(0) != 0,
            failed_login_attempts: 0, // Default for compatibility
            login_attempts: get_i64(11).unwrap_or(0) as i32,
            locked_until: None, // Default for compatibility
            email_verified: get_i64(10).unwrap_or(0) != 0,
            email_verification_token: None, // Default for compatibility
            password_reset_token: None,     // Default for compatibility
            password_reset_expires: None,   // Default for compatibility
            metadata: serde_json::from_str(&get_text(12)).unwrap_or_default(),
        })
    }

    fn row_to_session(&self, row: &dyn DatabaseRow) -> Result<Session> {
        let get_text = |idx: usize| -> String {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::Text(s) => s,
                _ => String::new(),
            }
        };

        let get_i64 = |idx: usize| -> Option<i64> {
            match row.get_by_index(idx).unwrap_or(DatabaseValue::Null) {
                DatabaseValue::I64(i) => Some(i),
                DatabaseValue::I32(i) => Some(i as i64),
                _ => None,
            }
        };

        Ok(Session {
            id: get_text(0)
                .parse::<uuid::Uuid>()
                .map(SessionId::from)
                .unwrap_or_default(),
            user_id: get_text(1)
                .parse::<uuid::Uuid>()
                .map(UserId::from)
                .unwrap_or_default(),
            token: get_text(2),
            refresh_token: if get_text(3).is_empty() {
                None
            } else {
                Some(get_text(3))
            },
            expires_at: get_i64(4)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            created_at: get_i64(5)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            last_accessed: get_i64(6)
                .and_then(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .unwrap_or_default(),
            ip_address: if get_text(7).is_empty() {
                None
            } else {
                get_text(7).parse().ok()
            },
            user_agent: if get_text(8).is_empty() {
                None
            } else {
                Some(get_text(8))
            },
            is_active: get_i64(9).unwrap_or(0) != 0,
            session_data: serde_json::from_str(&get_text(10)).unwrap_or_default(),
            metadata: serde_json::from_str(&get_text(11)).unwrap_or_default(),
        })
    }
}

#[async_trait]
impl DatabaseProvider for NativeMysqlProvider {
    fn provider_type(&self) -> crate::database::connection::DatabaseType {
        crate::database::connection::DatabaseType::Mysql
    }

    fn supports_feature(&self, feature: &str) -> bool {
        match feature {
            "auth" | "rbac" | "transactions" => true,
            "json" => true,             // MySQL 5.7+ has JSON support
            "full_text_search" => true, // MySQL supports full-text search
            _ => false,
        }
    }

    async fn initialize(&self) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Create users table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id VARCHAR(36) PRIMARY KEY,
                username VARCHAR(255) UNIQUE NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                created_at BIGINT NOT NULL,
                updated_at BIGINT NOT NULL,
                last_login BIGINT,
                role VARCHAR(50) NOT NULL DEFAULT 'user',
                status VARCHAR(50) NOT NULL DEFAULT 'active',
                is_verified BOOLEAN NOT NULL DEFAULT false,
                login_attempts INT NOT NULL DEFAULT 0,
                metadata TEXT
            )",
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create users table: {}", e)))?;

        // Create sessions table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id VARCHAR(36) PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL,
                token TEXT NOT NULL,
                refresh_token TEXT,
                expires_at BIGINT NOT NULL,
                created_at BIGINT NOT NULL,
                last_accessed BIGINT NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                is_active BOOLEAN NOT NULL DEFAULT true,
                session_data TEXT,
                metadata TEXT,
                FOREIGN KEY (user_id) REFERENCES {} (id) ON DELETE CASCADE
            )",
                self.table_name("sessions"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create sessions table: {}", e)))?;

        // Create indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {} (username)",
                self.index_name("idx_users_username"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create username index: {}", e)))?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {} (email)",
                self.index_name("idx_users_email"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create email index: {}", e)))?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {} (user_id)",
                self.index_name("idx_sessions_user_id"),
                self.table_name("sessions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!("Failed to create sessions user_id index: {}", e))
        })?;

        // Create optimized RBAC tables with consistent naming and better indexes

        // Create permissions table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id VARCHAR(36) PRIMARY KEY,
                name VARCHAR(255) UNIQUE NOT NULL,
                description TEXT,
                resource VARCHAR(255) NOT NULL,
                action VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                metadata JSON,
                UNIQUE KEY unique_resource_action (resource, action)
            )",
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create permissions table: {}", e)))?;

        // Create roles table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id VARCHAR(36) PRIMARY KEY,
                name VARCHAR(255) UNIQUE NOT NULL,
                description TEXT,
                parent_id VARCHAR(36),
                level INT NOT NULL DEFAULT 0,
                is_system BOOLEAN NOT NULL DEFAULT FALSE,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                metadata JSON,
                FOREIGN KEY (parent_id) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("roles"),
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create roles table: {}", e)))?;

        // Create role_permissions junction table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                role_id VARCHAR(36) NOT NULL,
                permission_id VARCHAR(36) NOT NULL,
                granted BOOLEAN NOT NULL DEFAULT TRUE,
                granted_by VARCHAR(36),
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata JSON,
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (granted_by) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("role_permissions"),
                self.table_name("roles"),
                self.table_name("permissions"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!("Failed to create role_permissions table: {}", e))
        })?;

        // Create user_role_assignments junction table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                user_id VARCHAR(36) NOT NULL,
                role_id VARCHAR(36) NOT NULL,
                assigned_by VARCHAR(36),
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                metadata JSON,
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES {}(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES {}(id) ON DELETE SET NULL
            )",
                self.table_name("user_role_assignments"),
                self.table_name("users"),
                self.table_name("roles"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create user_role_assignments table: {}",
                e
            ))
        })?;

        // Create user_attributes table
        conn.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS {} (
                id VARCHAR(36) PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL,
                `key` VARCHAR(255) NOT NULL,
                value_type VARCHAR(50) NOT NULL,
                value_string TEXT,
                value_number DOUBLE,
                value_boolean BOOLEAN,
                value_json JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_user_key (user_id, `key`),
                FOREIGN KEY (user_id) REFERENCES {} (id) ON DELETE CASCADE
            )",
                self.table_name("user_attributes"),
                self.table_name("users")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!("Failed to create user_attributes table: {}", e))
        })?;

        // Create optimized indexes for RBAC performance

        // Permissions indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(name)",
                self.index_name("idx_permissions_name"),
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!("Failed to create permissions name index: {}", e))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(resource)",
                self.index_name("idx_permissions_resource"),
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create permissions resource index: {}",
                e
            ))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(action)",
                self.index_name("idx_permissions_action"),
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!("Failed to create permissions action index: {}", e))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(resource, action)",
                self.index_name("idx_permissions_resource_action"),
                self.table_name("permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create permissions resource_action index: {}",
                e
            ))
        })?;

        // Roles indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(name)",
                self.index_name("idx_roles_name"),
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create roles name index: {}", e)))?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(parent_id)",
                self.index_name("idx_roles_parent_id"),
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!("Failed to create roles parent_id index: {}", e))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(level)",
                self.index_name("idx_roles_level"),
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create roles level index: {}", e)))?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(is_active)",
                self.index_name("idx_roles_active"),
                self.table_name("roles")
            ),
            &[],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create roles active index: {}", e)))?;

        // Role permissions indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(role_id)",
                self.index_name("idx_role_permissions_role_id"),
                self.table_name("role_permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create role_permissions role_id index: {}",
                e
            ))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(permission_id)",
                self.index_name("idx_role_permissions_permission_id"),
                self.table_name("role_permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create role_permissions permission_id index: {}",
                e
            ))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(granted)",
                self.index_name("idx_role_permissions_granted"),
                self.table_name("role_permissions")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create role_permissions granted index: {}",
                e
            ))
        })?;

        // User role assignments indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(user_id)",
                self.index_name("idx_user_role_assignments_user_id"),
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create user_role_assignments user_id index: {}",
                e
            ))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(role_id)",
                self.index_name("idx_user_role_assignments_role_id"),
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create user_role_assignments role_id index: {}",
                e
            ))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(is_active, expires_at)",
                self.index_name("idx_user_role_assignments_active"),
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create user_role_assignments active index: {}",
                e
            ))
        })?;
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(expires_at)",
                self.index_name("idx_user_role_assignments_expires"),
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create user_role_assignments expires index: {}",
                e
            ))
        })?;

        // Composite index for permission checks
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(user_id, is_active)",
                self.index_name("idx_permission_check_composite"),
                self.table_name("user_role_assignments")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create permission check composite index: {}",
                e
            ))
        })?;

        // User attributes indexes
        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(user_id)",
                self.index_name("idx_user_attributes_user_id"),
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create user_attributes user_id index: {}",
                e
            ))
        })?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(`key`)",
                self.index_name("idx_user_attributes_key"),
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create user_attributes key index: {}",
                e
            ))
        })?;

        conn.execute(
            &format!(
                "CREATE INDEX IF NOT EXISTS {} ON {}(value_type)",
                self.index_name("idx_user_attributes_value_type"),
                self.table_name("user_attributes")
            ),
            &[],
        )
        .await
        .map_err(|e| {
            AuthError::internal(&format!(
                "Failed to create user_attributes value_type index: {}",
                e
            ))
        })?;

        Ok(())
    }

    async fn health_check(&self) -> Result<DatabaseHealth> {
        let start = std::time::Instant::now();

        match self.pool.get_connection().await {
            Ok(mut conn) => match conn.execute("SELECT 1", &[]).await {
                Ok(_) => {
                    let response_time = start.elapsed();
                    Ok(DatabaseHealth {
                        is_healthy: true,
                        response_time,
                        connection_count: Some(self.pool.active_connections() as u32),
                        error_message: None,
                    })
                }
                Err(e) => Ok(DatabaseHealth {
                    is_healthy: false,
                    response_time: start.elapsed(),
                    connection_count: None,
                    error_message: Some(format!("Query failed: {}", e)),
                }),
            },
            Err(e) => Ok(DatabaseHealth {
                is_healthy: false,
                response_time: start.elapsed(),
                connection_count: None,
                error_message: Some(format!("Connection failed: {}", e)),
            }),
        }
    }

    async fn get_stats(&self) -> Result<DatabaseStats> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let row = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", self.table_name("users")),
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to count users: {}", e)))?;
        let total_users = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;
        let row = conn
            .query_one(
                &format!(
                    "SELECT COUNT(*) FROM {} WHERE is_active = true AND expires_at > ?",
                    self.table_name("sessions")
                ),
                &[DatabaseValue::I64(current_time)],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to count active sessions: {}", e)))?;
        let active_sessions = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        let row = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", self.table_name("sessions")),
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to count total sessions: {}", e)))?;
        let total_sessions = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        Ok(DatabaseStats {
            total_users,
            active_sessions,
            total_sessions,
            database_size: None,
            connection_pool_size: Some(self.pool.max_connections() as u32),
            active_connections: Some(self.pool.active_connections() as u32),
        })
    }

    // RBAC operations - Role management
    async fn create_role(&self, request: CreateRoleRequest) -> Result<Role> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let role_id = uuid::Uuid::new_v4().to_string();
        let now = chrono::Utc::now();

        // Calculate level based on parent role if provided
        let level = if let Some(parent_id) = &request.parent_id {
            let rows = conn
                .query_all(
                    &format!(
                        "SELECT level FROM {} WHERE id = ?",
                        self.table_name("roles")
                    ),
                    &[DatabaseValue::Text(parent_id.to_string())],
                )
                .await
                .map_err(|e| AuthError::internal(&format!("Failed to get parent role: {}", e)))?;

            if rows.is_empty() {
                return Err(AuthError::NotFound {
                    resource: "parent_role".to_string(),
                    id: parent_id.to_string(),
                });
            }

            let parent_level: i64 = rows[0]
                .get_by_index(0)
                .unwrap_or(DatabaseValue::I64(0))
                .to_string()
                .parse::<i64>()
                .map_err(|_| AuthError::internal("Invalid parent level"))?;

            parent_level + 1
        } else {
            0
        };

        conn.execute(
            &format!("INSERT INTO {} (id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", self.table_name("roles")),
            &[
                DatabaseValue::Text(role_id.clone()),
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                request.parent_id.as_ref().map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(level),
                DatabaseValue::Bool(false),
                DatabaseValue::Bool(true),
                DatabaseValue::I64(now.timestamp()),
                DatabaseValue::I64(now.timestamp()),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("Duplicate entry") {
                AuthError::Validation {
                    message: format!("Role name '{}' already exists", request.name),
                    field: Some("name".to_string()),
                }
            } else {
                AuthError::internal(&format!("Failed to create role: {}", e))
            }
        })?;

        Ok(Role {
            id: RoleId::from(uuid::Uuid::parse_str(&role_id).unwrap()),
            name: request.name,
            description: request.description,
            parent_id: request.parent_id,
            level: level as u32,
            is_system: false,
            is_active: true,
            created_at: now,
            updated_at: now,
            metadata: request.metadata.unwrap_or(serde_json::Value::Null),
        })
    }
    async fn get_role(&self, role_id: RoleId) -> Result<Option<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata 
                 FROM {} WHERE id = ?", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get role: {}", e)))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let role = Role {
            id: RoleId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            parent_id: {
                let parent_str = row.get_by_index(3).unwrap().to_string();
                if parent_str.is_empty() {
                    None
                } else {
                    Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                }
            },
            level: row
                .get_by_index(4)
                .unwrap()
                .to_string()
                .parse()
                .unwrap_or(0),
            is_system: row.get_by_index(5).unwrap().to_string() == "1"
                || row.get_by_index(5).unwrap().to_string() == "true",
            is_active: row.get_by_index(6).unwrap().to_string() == "1"
                || row.get_by_index(6).unwrap().to_string() == "true",
            created_at: {
                let ts: i64 = row
                    .get_by_index(7)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(ts, 0).unwrap()
            },
            updated_at: {
                let ts: i64 = row
                    .get_by_index(8)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(ts, 0).unwrap()
            },
            metadata: {
                let meta_str = row.get_by_index(9).unwrap().to_string();
                if meta_str.is_empty() {
                    serde_json::Value::Null
                } else {
                    serde_json::from_str(&meta_str).unwrap_or(serde_json::Value::String(meta_str))
                }
            },
        };

        Ok(Some(role))
    }
    async fn get_role_by_name(&self, name: &str) -> Result<Option<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata 
                 FROM {} WHERE name = ?", self.table_name("roles")),
                &[DatabaseValue::Text(name.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get role by name: {}", e)))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let role = Role {
            id: RoleId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            parent_id: {
                let parent_str = row.get_by_index(3).unwrap().to_string();
                if parent_str.is_empty() {
                    None
                } else {
                    Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                }
            },
            level: row
                .get_by_index(4)
                .unwrap()
                .to_string()
                .parse()
                .unwrap_or(0),
            is_system: row.get_by_index(5).unwrap().to_string() == "1"
                || row.get_by_index(5).unwrap().to_string() == "true",
            is_active: row.get_by_index(6).unwrap().to_string() == "1"
                || row.get_by_index(6).unwrap().to_string() == "true",
            created_at: {
                let ts: i64 = row
                    .get_by_index(7)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(ts, 0).unwrap()
            },
            updated_at: {
                let ts: i64 = row
                    .get_by_index(8)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(ts, 0).unwrap()
            },
            metadata: {
                let meta_str = row.get_by_index(9).unwrap().to_string();
                if meta_str.is_empty() {
                    serde_json::Value::Null
                } else {
                    serde_json::from_str(&meta_str).unwrap_or(serde_json::Value::String(meta_str))
                }
            },
        };

        Ok(Some(role))
    }
    async fn update_role(&self, role_id: RoleId, request: CreateRoleRequest) -> Result<Role> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let now = chrono::Utc::now();

        // Calculate level based on parent role if provided
        let level = if let Some(parent_id) = &request.parent_id {
            let rows = conn
                .query_all(
                    &format!(
                        "SELECT level FROM {} WHERE id = ?",
                        self.table_name("roles")
                    ),
                    &[DatabaseValue::Text(parent_id.to_string())],
                )
                .await
                .map_err(|e| AuthError::internal(&format!("Failed to get parent role: {}", e)))?;

            if rows.is_empty() {
                return Err(AuthError::NotFound {
                    resource: "parent_role".to_string(),
                    id: parent_id.to_string(),
                });
            }

            let parent_level: i64 = rows[0]
                .get_by_index(0)
                .unwrap_or(DatabaseValue::I64(0))
                .to_string()
                .parse::<i64>()
                .map_err(|_| AuthError::internal("Invalid parent level"))?;

            parent_level + 1
        } else {
            0
        };

        conn.execute(
            &format!("UPDATE {} SET name = ?, description = ?, parent_id = ?, level = ?, updated_at = ?, metadata = ?
             WHERE id = ?", self.table_name("roles")),
            &[
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                request.parent_id.as_ref().map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(level),
                DatabaseValue::I64(now.timestamp()),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(role_id.to_string()),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("Duplicate entry") {
                AuthError::Validation {
                    message: format!("Role name '{}' already exists", request.name),
                    field: Some("name".to_string()),
                }
            } else {
                AuthError::internal(&format!("Failed to update role: {}", e))
            }
        })?;

        // Get the updated role
        self.get_role(role_id)
            .await?
            .ok_or_else(|| AuthError::NotFound {
                resource: "role".to_string(),
                id: role_id.to_string(),
            })
    }
    async fn delete_role(&self, role_id: RoleId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Check if role exists and is not a system role
        let rows = conn
            .query_all(
                &format!(
                    "SELECT is_system FROM {} WHERE id = ?",
                    self.table_name("roles")
                ),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to check role: {}", e)))?;

        if rows.is_empty() {
            return Ok(false);
        }

        let is_system = rows[0].get_by_index(0).unwrap().to_string() == "1"
            || rows[0].get_by_index(0).unwrap().to_string() == "true";
        if is_system {
            return Err(AuthError::Validation {
                message: "Cannot delete system roles".to_string(),
                field: Some("is_system".to_string()),
            });
        }

        // Check if role has child roles
        let child_rows = conn
            .query_all(
                &format!(
                    "SELECT COUNT(*) FROM {} WHERE parent_id = ?",
                    self.table_name("roles")
                ),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to check child roles: {}", e)))?;

        let child_count: i64 = child_rows[0]
            .get_by_index(0)
            .unwrap_or(DatabaseValue::I64(0))
            .to_string()
            .parse()
            .map_err(|_| AuthError::internal("Invalid child count"))?;

        if child_count > 0 {
            return Err(AuthError::Validation {
                message: "Cannot delete role with child roles".to_string(),
                field: Some("parent_id".to_string()),
            });
        }

        let result = conn
            .execute(
                &format!("DELETE FROM {} WHERE id = ?", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to delete role: {}", e)))?;

        Ok(result > 0)
    }
    async fn list_roles(&self, filter: RoleFilter) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let mut query = format!("SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata FROM {} WHERE 1=1", self.table_name("roles"));
        let mut params = Vec::new();

        if let Some(name) = &filter.name {
            query.push_str(" AND name LIKE ?");
            params.push(DatabaseValue::Text(format!("%{}%", name)));
        }

        if let Some(parent_id) = &filter.parent_id {
            query.push_str(" AND parent_id = ?");
            params.push(DatabaseValue::Text(parent_id.to_string()));
        }

        if let Some(is_active) = filter.is_active {
            query.push_str(" AND is_active = ?");
            params.push(DatabaseValue::Bool(is_active));
        }

        if let Some(is_system) = filter.is_system {
            query.push_str(" AND is_system = ?");
            params.push(DatabaseValue::Bool(is_system));
        }

        query.push_str(" ORDER BY level ASC, name ASC");

        if let Some(limit) = filter.limit {
            query.push_str(" LIMIT ?");
            params.push(DatabaseValue::I64(limit as i64));
        }

        let rows = conn
            .query_all(&query, &params)
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to list roles: {}", e)))?;

        let mut roles = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    let parent_str = row.get_by_index(3).unwrap().to_string();
                    if parent_str.is_empty() {
                        None
                    } else {
                        Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: row.get_by_index(5).unwrap().to_string() == "1"
                    || row.get_by_index(5).unwrap().to_string() == "true",
                is_active: row.get_by_index(6).unwrap().to_string() == "1"
                    || row.get_by_index(6).unwrap().to_string() == "true",
                created_at: {
                    let ts: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                updated_at: {
                    let ts: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(9).unwrap().to_string();
                    if meta_str.is_empty() {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            roles.push(role);
        }

        Ok(roles)
    }
    async fn create_permission(&self, request: CreatePermissionRequest) -> Result<Permission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let permission_id = uuid::Uuid::new_v4().to_string();
        let now = chrono::Utc::now();

        conn.execute(
            &format!("INSERT INTO {} (id, name, description, resource, action, created_at, updated_at, metadata)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)", self.table_name("permissions")),
            &[
                DatabaseValue::Text(permission_id.clone()),
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                DatabaseValue::Text(request.resource.clone()),
                DatabaseValue::Text(request.action.clone()),
                DatabaseValue::I64(now.timestamp()),
                DatabaseValue::I64(now.timestamp()),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("Duplicate entry") {
                AuthError::Conflict {
                    resource: "permission".to_string(),
                    field: "resource_action".to_string(),
                    value: format!("{}:{}", request.resource, request.action),
                }
            } else {
                AuthError::internal(&format!("Failed to create permission: {}", e))
            }
        })?;

        Ok(Permission {
            id: PermissionId::from(uuid::Uuid::parse_str(&permission_id).unwrap()),
            name: request.name,
            description: request.description,
            resource: request.resource,
            action: request.action,
            created_at: now,
            updated_at: now,
            metadata: request.metadata.unwrap_or(serde_json::Value::Null),
        })
    }
    async fn get_permission(&self, permission_id: PermissionId) -> Result<Option<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, name, description, resource, action, created_at, updated_at, metadata 
                 FROM {} WHERE id = ?", self.table_name("permissions")),
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get permission: {}", e)))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let permission = Permission {
            id: PermissionId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            resource: row.get_by_index(3).unwrap().to_string(),
            action: row.get_by_index(4).unwrap().to_string(),
            created_at: {
                let ts: i64 = row
                    .get_by_index(5)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(ts, 0).unwrap()
            },
            updated_at: {
                let ts: i64 = row
                    .get_by_index(6)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(ts, 0).unwrap()
            },
            metadata: {
                let meta_str = row.get_by_index(7).unwrap().to_string();
                if meta_str.is_empty() {
                    serde_json::Value::Null
                } else {
                    serde_json::from_str(&meta_str).unwrap_or(serde_json::Value::String(meta_str))
                }
            },
        };

        Ok(Some(permission))
    }
    async fn get_permission_by_key(
        &self,
        resource: &str,
        action: &str,
    ) -> Result<Option<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                &format!("SELECT id, name, description, resource, action, created_at, updated_at, metadata 
                 FROM {} WHERE resource = ? AND action = ?", self.table_name("permissions")),
                &[
                    DatabaseValue::Text(resource.to_string()),
                    DatabaseValue::Text(action.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get permission by key: {}", e)))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let permission = Permission {
            id: PermissionId::from(
                uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
            ),
            name: row.get_by_index(1).unwrap().to_string(),
            description: {
                let desc = row.get_by_index(2).unwrap().to_string();
                if desc.is_empty() {
                    None
                } else {
                    Some(desc)
                }
            },
            resource: row.get_by_index(3).unwrap().to_string(),
            action: row.get_by_index(4).unwrap().to_string(),
            created_at: {
                let ts: i64 = row
                    .get_by_index(5)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(ts, 0).unwrap()
            },
            updated_at: {
                let ts: i64 = row
                    .get_by_index(6)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0);
                chrono::DateTime::from_timestamp(ts, 0).unwrap()
            },
            metadata: {
                let meta_str = row.get_by_index(7).unwrap().to_string();
                if meta_str.is_empty() {
                    serde_json::Value::Null
                } else {
                    serde_json::from_str(&meta_str).unwrap_or(serde_json::Value::String(meta_str))
                }
            },
        };

        Ok(Some(permission))
    }
    async fn update_permission(
        &self,
        permission_id: PermissionId,
        request: CreatePermissionRequest,
    ) -> Result<Permission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let now = chrono::Utc::now();

        conn.execute(
            &format!("UPDATE {} SET name = ?, description = ?, resource = ?, action = ?, updated_at = ?, metadata = ?
             WHERE id = ?", self.table_name("permissions")),
            &[
                DatabaseValue::Text(request.name.clone()),
                DatabaseValue::Text(request.description.clone().unwrap_or_default()),
                DatabaseValue::Text(request.resource.clone()),
                DatabaseValue::Text(request.action.clone()),
                DatabaseValue::I64(now.timestamp()),
                request.metadata.as_ref().map(|m| DatabaseValue::Text(m.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(permission_id.to_string()),
            ],
        )
        .await
        .map_err(|e| {
            if e.to_string().contains("Duplicate entry") {
                AuthError::Conflict {
                    resource: "permission".to_string(),
                    field: "resource_action".to_string(),
                    value: format!("{}:{}", request.resource, request.action),
                }
            } else {
                AuthError::internal(&format!("Failed to update permission: {}", e))
            }
        })?;

        // Get the updated permission
        self.get_permission(permission_id)
            .await?
            .ok_or_else(|| AuthError::NotFound {
                resource: "permission".to_string(),
                id: permission_id.to_string(),
            })
    }
    async fn delete_permission(&self, permission_id: PermissionId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let result = conn
            .execute(
                &format!(
                    "DELETE FROM {} WHERE id = ?",
                    self.table_name("permissions")
                ),
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to delete permission: {}", e)))?;

        Ok(result > 0)
    }
    async fn list_permissions(&self, filter: PermissionFilter) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let mut query = format!("SELECT id, name, description, resource, action, created_at, updated_at, metadata FROM {} WHERE 1=1", self.table_name("permissions"));
        let mut params = Vec::new();

        if let Some(name) = &filter.name {
            query.push_str(" AND name LIKE ?");
            params.push(DatabaseValue::Text(format!("%{}%", name)));
        }

        if let Some(resource) = &filter.resource {
            query.push_str(" AND resource = ?");
            params.push(DatabaseValue::Text(resource.clone()));
        }

        if let Some(action) = &filter.action {
            query.push_str(" AND action = ?");
            params.push(DatabaseValue::Text(action.clone()));
        }

        query.push_str(" ORDER BY resource ASC, action ASC, name ASC");

        if let Some(limit) = filter.limit {
            query.push_str(" LIMIT ?");
            params.push(DatabaseValue::I64(limit as i64));
        }

        let rows = conn
            .query_all(&query, &params)
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to list permissions: {}", e)))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let ts: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                updated_at: {
                    let ts: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(7).unwrap().to_string();
                    if meta_str.is_empty() {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }
    async fn assign_permission_to_role(
        &self,
        role_id: RoleId,
        permission_id: PermissionId,
        granted_by: Option<UserId>,
    ) -> Result<RolePermission> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let now = chrono::Utc::now();

        // Check if role exists
        let role_rows = conn
            .query_all(
                &format!("SELECT id FROM {} WHERE id = ?", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to check role: {}", e)))?;

        if role_rows.is_empty() {
            return Err(AuthError::NotFound {
                resource: "role".to_string(),
                id: role_id.to_string(),
            });
        }

        // Check if permission exists
        let permission_rows = conn
            .query_all(
                &format!(
                    "SELECT id FROM {} WHERE id = ?",
                    self.table_name("permissions")
                ),
                &[DatabaseValue::Text(permission_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to check permission: {}", e)))?;

        if permission_rows.is_empty() {
            return Err(AuthError::NotFound {
                resource: "permission".to_string(),
                id: permission_id.to_string(),
            });
        }

        conn.execute(
            &format!("INSERT INTO {} (role_id, permission_id, granted, granted_by, granted_at)
             VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE granted = VALUES(granted), granted_by = VALUES(granted_by), granted_at = VALUES(granted_at)", self.table_name("role_permissions")),
            &[
                DatabaseValue::Text(role_id.to_string()),
                DatabaseValue::Text(permission_id.to_string()),
                DatabaseValue::Bool(true),
                granted_by.map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(now.timestamp()),
            ],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to assign permission to role: {}", e)))?;

        Ok(RolePermission {
            role_id,
            permission_id,
            granted: true,
            granted_by,
            granted_at: now,
            metadata: serde_json::Value::Null,
        })
    }
    async fn revoke_permission_from_role(
        &self,
        role_id: RoleId,
        permission_id: PermissionId,
    ) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let result = conn
            .execute(
                &format!(
                    "DELETE FROM {} WHERE role_id = ? AND permission_id = ?",
                    self.table_name("role_permissions")
                ),
                &[
                    DatabaseValue::Text(role_id.to_string()),
                    DatabaseValue::Text(permission_id.to_string()),
                ],
            )
            .await
            .map_err(|e| {
                AuthError::internal(&format!("Failed to revoke permission from role: {}", e))
            })?;

        Ok(result > 0)
    }
    async fn get_role_permissions(&self, role_id: RoleId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn.query_all(
            &format!("SELECT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
             FROM {} p
             JOIN {} rp ON p.id = rp.permission_id
             WHERE rp.role_id = ? AND rp.granted = TRUE
             ORDER BY p.resource ASC, p.action ASC, p.name ASC", self.table_name("permissions"), self.table_name("role_permissions")),
            &[DatabaseValue::Text(role_id.to_string())]
        ).await.map_err(|e| AuthError::internal(&format!("Failed to get role permissions: {}", e)))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let ts: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                updated_at: {
                    let ts: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(7).unwrap().to_string();
                    if meta_str.is_empty() {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            permissions.push(permission);
        }
        Ok(permissions)
    }
    async fn assign_role_to_user(
        &self,
        user_id: UserId,
        role_id: RoleId,
        assigned_by: Option<UserId>,
    ) -> Result<UserRoleAssignment> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let now = chrono::Utc::now();

        // Check if user exists
        let user_rows = conn
            .query_all(
                &format!("SELECT id FROM {} WHERE id = ?", self.table_name("users")),
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to check user: {}", e)))?;

        if user_rows.is_empty() {
            return Err(AuthError::NotFound {
                resource: "user".to_string(),
                id: user_id.to_string(),
            });
        }

        // Check if role exists
        let role_rows = conn
            .query_all(
                &format!("SELECT id FROM {} WHERE id = ?", self.table_name("roles")),
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to check role: {}", e)))?;

        if role_rows.is_empty() {
            return Err(AuthError::NotFound {
                resource: "role".to_string(),
                id: role_id.to_string(),
            });
        }

        conn.execute(
            &format!("INSERT INTO {} (user_id, role_id, is_active, assigned_by, assigned_at, expires_at)
             VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE is_active = VALUES(is_active), assigned_by = VALUES(assigned_by), assigned_at = VALUES(assigned_at)", self.table_name("user_role_assignments")),
            &[
                DatabaseValue::Text(user_id.to_string()),
                DatabaseValue::Text(role_id.to_string()),
                DatabaseValue::Bool(true),
                assigned_by.map(|id| DatabaseValue::Text(id.to_string())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(now.timestamp()),
                DatabaseValue::Null, // No expiration by default
            ],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to assign role to user: {}", e)))?;

        Ok(UserRoleAssignment {
            user_id,
            role_id,
            is_active: true,
            assigned_by,
            assigned_at: now,
            expires_at: None,
            metadata: serde_json::Value::Null,
        })
    }
    async fn revoke_role_from_user(&self, user_id: UserId, role_id: RoleId) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let result = conn
            .execute(
                &format!(
                    "DELETE FROM {} WHERE user_id = ? AND role_id = ?",
                    self.table_name("user_role_assignments")
                ),
                &[
                    DatabaseValue::Text(user_id.to_string()),
                    DatabaseValue::Text(role_id.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to revoke role from user: {}", e)))?;

        Ok(result > 0)
    }
    async fn get_user_roles(&self, user_id: UserId) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn.query_all(
            "SELECT r.id, r.name, r.description, r.parent_id, r.level, r.is_system, r.is_active, r.created_at, r.updated_at, r.metadata 
             FROM roles r
             JOIN user_role_assignments ura ON r.id = ura.role_id
             WHERE ura.user_id = ? AND ura.is_active = TRUE AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
             ORDER BY r.level ASC, r.name ASC",
            &[DatabaseValue::Text(user_id.to_string())]
        ).await.map_err(|e| AuthError::internal(&format!("Failed to get user roles: {}", e)))?;

        let mut roles = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    let parent_str = row.get_by_index(3).unwrap().to_string();
                    if parent_str.is_empty() {
                        None
                    } else {
                        Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: row.get_by_index(5).unwrap().to_string() == "1"
                    || row.get_by_index(5).unwrap().to_string() == "true",
                is_active: row.get_by_index(6).unwrap().to_string() == "1"
                    || row.get_by_index(6).unwrap().to_string() == "true",
                created_at: {
                    let ts: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                updated_at: {
                    let ts: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(9).unwrap().to_string();
                    if meta_str.is_empty() {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            roles.push(role);
        }
        Ok(roles)
    }
    async fn get_users_with_role(&self, role_id: RoleId) -> Result<Vec<UserId>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                "SELECT user_id FROM user_role_assignments 
                 WHERE role_id = ? AND is_active = TRUE AND (expires_at IS NULL OR expires_at > NOW())",
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get users with role: {}", e)))?;

        let mut user_ids = Vec::new();
        for row in rows {
            let user_id_str = row.get_by_index(0).unwrap().to_string();
            if let Ok(uuid) = uuid::Uuid::parse_str(&user_id_str) {
                user_ids.push(UserId::from(uuid));
            }
        }

        Ok(user_ids)
    }
    async fn check_permission(&self, check: PermissionCheck) -> Result<PermissionResult> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Check if user has the specific permission through their roles
        let rows = conn
            .query_all(
                "SELECT COUNT(*) FROM user_role_assignments ur
                 JOIN role_permissions rp ON ur.role_id = rp.role_id
                 JOIN permissions p ON rp.permission_id = p.id
                 WHERE ur.user_id = ? 
                   AND ur.is_active = TRUE 
                   AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
                   AND rp.granted = TRUE
                   AND p.resource = ? 
                   AND p.action = ?",
                &[
                    DatabaseValue::Text(check.user_id.to_string()),
                    DatabaseValue::Text(check.resource.clone()),
                    DatabaseValue::Text(check.action.clone()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to check permission: {}", e)))?;

        let has_permission = if !rows.is_empty() {
            let count: i64 = rows[0]
                .get_by_index(0)
                .unwrap_or(DatabaseValue::I64(0))
                .to_string()
                .parse()
                .map_err(|_| AuthError::internal("Invalid permission count"))?;
            count > 0
        } else {
            false
        };

        Ok(PermissionResult {
            allowed: has_permission,
            reason: if has_permission {
                Some("Permission granted through role assignment".to_string())
            } else {
                Some("No matching permission found".to_string())
            },
            matched_permissions: Vec::new(),
            effective_roles: Vec::new(),
        })
    }
    async fn has_permission(&self, user_id: UserId, resource: &str, action: &str) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Check permission through role hierarchy using recursive CTE
        // This query checks permissions from direct role assignments and inherited from parent roles
        let rows = conn
            .query_all(
                "WITH RECURSIVE role_hierarchy AS (
                    -- Base case: direct user role assignments
                    SELECT r.id, r.parent_id, r.level
                    FROM roles r
                    JOIN user_role_assignments ura ON r.id = ura.role_id
                    WHERE ura.user_id = ? 
                      AND ura.is_active = TRUE 
                      AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
                    
                    UNION ALL
                    
                    -- Recursive case: parent roles
                    SELECT r.id, r.parent_id, r.level
                    FROM roles r
                    JOIN role_hierarchy rh ON r.id = rh.parent_id
                    WHERE r.is_active = TRUE
                )
                SELECT COUNT(*) FROM role_hierarchy rh
                JOIN role_permissions rp ON rh.id = rp.role_id
                JOIN permissions p ON rp.permission_id = p.id
                WHERE rp.granted = TRUE
                  AND p.resource = ? 
                  AND p.action = ?",
                &[
                    DatabaseValue::Text(user_id.to_string()),
                    DatabaseValue::Text(resource.to_string()),
                    DatabaseValue::Text(action.to_string()),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to check permission: {}", e)))?;

        if rows.is_empty() {
            return Ok(false);
        }

        let count: i64 = rows[0]
            .get_by_index(0)
            .unwrap_or(DatabaseValue::I64(0))
            .to_string()
            .parse()
            .map_err(|_| AuthError::internal("Invalid permission count"))?;

        Ok(count > 0)
    }
    async fn get_user_permissions(&self, user_id: UserId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn.query_all(
            "SELECT DISTINCT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
             FROM permissions p
             JOIN role_permissions rp ON p.id = rp.permission_id
             JOIN user_role_assignments ura ON rp.role_id = ura.role_id
             WHERE ura.user_id = ? AND ura.is_active = TRUE AND (ura.expires_at IS NULL OR ura.expires_at > NOW()) AND rp.granted = TRUE
             ORDER BY p.resource ASC, p.action ASC, p.name ASC",
            &[DatabaseValue::Text(user_id.to_string())]
        ).await.map_err(|e| AuthError::internal(&format!("Failed to get user permissions: {}", e)))?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let ts: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                updated_at: {
                    let ts: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(7).unwrap().to_string();
                    if meta_str.is_empty() {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            permissions.push(permission);
        }
        Ok(permissions)
    }
    async fn get_role_hierarchy(&self, role_id: RoleId) -> Result<Vec<Role>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Get the role hierarchy using a recursive CTE (Common Table Expression)
        // MySQL 8.0+ supports recursive CTEs
        let rows = conn
            .query_all(
                "WITH RECURSIVE role_hierarchy AS (
                    SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata, 0 as depth
                    FROM roles 
                    WHERE id = ?
                    UNION ALL
                    SELECT r.id, r.name, r.description, r.parent_id, r.level, r.is_system, r.is_active, r.created_at, r.updated_at, r.metadata, rh.depth + 1
                    FROM roles r
                    INNER JOIN role_hierarchy rh ON r.parent_id = rh.id
                    WHERE rh.depth < 10
                )
                SELECT id, name, description, parent_id, level, is_system, is_active, created_at, updated_at, metadata
                FROM role_hierarchy
                ORDER BY depth ASC, level ASC",
                &[DatabaseValue::Text(role_id.to_string())],
            )
            .await
            .map_err(|e| {
                // Fallback for older MySQL versions that don't support CTEs
                if e.to_string().contains("syntax error") || e.to_string().contains("doesn't support") {
                    return AuthError::internal("Role hierarchy requires MySQL 8.0+ for recursive queries. Using simple parent lookup instead.");
                }
                AuthError::internal(&format!("Failed to get role hierarchy: {}", e))
            })?;

        let mut roles = Vec::new();
        for row in rows {
            let role = Role {
                id: RoleId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                parent_id: {
                    let parent_str = row.get_by_index(3).unwrap().to_string();
                    if parent_str.is_empty() {
                        None
                    } else {
                        Some(RoleId::from(uuid::Uuid::parse_str(&parent_str).unwrap()))
                    }
                },
                level: row
                    .get_by_index(4)
                    .unwrap()
                    .to_string()
                    .parse()
                    .unwrap_or(0),
                is_system: row.get_by_index(5).unwrap().to_string() == "1"
                    || row.get_by_index(5).unwrap().to_string() == "true",
                is_active: row.get_by_index(6).unwrap().to_string() == "1"
                    || row.get_by_index(6).unwrap().to_string() == "true",
                created_at: {
                    let ts: i64 = row
                        .get_by_index(7)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                updated_at: {
                    let ts: i64 = row
                        .get_by_index(8)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(9).unwrap().to_string();
                    if meta_str.is_empty() {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            roles.push(role);
        }

        Ok(roles)
    }
    async fn get_effective_permissions(&self, user_id: UserId) -> Result<Vec<Permission>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Get all effective permissions for a user including inherited permissions from role hierarchy
        let rows = conn
            .query_all(
                "WITH RECURSIVE user_role_hierarchy AS (
                    -- Get direct user roles
                    SELECT ur.user_id, ur.role_id, r.parent_id, 0 as depth
                    FROM user_role_assignments ur
                    JOIN roles r ON ur.role_id = r.id
                    WHERE ur.user_id = ? 
                      AND ur.is_active = TRUE 
                      AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
                      AND r.is_active = TRUE
                    
                    UNION ALL
                    
                    -- Get inherited roles from parent hierarchy
                    SELECT urh.user_id, r.id as role_id, r.parent_id, urh.depth + 1
                    FROM user_role_hierarchy urh
                    JOIN roles r ON urh.parent_id = r.id
                    WHERE urh.depth < 10 AND r.is_active = TRUE
                )
                SELECT DISTINCT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at, p.metadata
                FROM user_role_hierarchy urh
                JOIN role_permissions rp ON urh.role_id = rp.role_id
                JOIN permissions p ON rp.permission_id = p.id
                WHERE rp.granted = TRUE
                ORDER BY p.resource ASC, p.action ASC, p.name ASC",
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| {
                // Fallback for older MySQL versions - just get direct permissions
                if e.to_string().contains("syntax error") || e.to_string().contains("doesn't support") {
                    // Use the existing get_user_permissions method as fallback
                    return AuthError::internal("Effective permissions with hierarchy requires MySQL 8.0+. Using direct permissions instead.");
                }
                AuthError::internal(&format!("Failed to get effective permissions: {}", e))
            })?;

        let mut permissions = Vec::new();
        for row in rows {
            let permission = Permission {
                id: PermissionId::from(
                    uuid::Uuid::parse_str(&row.get_by_index(0).unwrap().to_string()).unwrap(),
                ),
                name: row.get_by_index(1).unwrap().to_string(),
                description: {
                    let desc = row.get_by_index(2).unwrap().to_string();
                    if desc.is_empty() {
                        None
                    } else {
                        Some(desc)
                    }
                },
                resource: row.get_by_index(3).unwrap().to_string(),
                action: row.get_by_index(4).unwrap().to_string(),
                created_at: {
                    let ts: i64 = row
                        .get_by_index(5)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                updated_at: {
                    let ts: i64 = row
                        .get_by_index(6)
                        .unwrap()
                        .to_string()
                        .parse()
                        .unwrap_or(0);
                    chrono::DateTime::from_timestamp(ts, 0).unwrap()
                },
                metadata: {
                    let meta_str = row.get_by_index(7).unwrap().to_string();
                    if meta_str.is_empty() {
                        serde_json::Value::Null
                    } else {
                        serde_json::from_str(&meta_str)
                            .unwrap_or(serde_json::Value::String(meta_str))
                    }
                },
            };
            permissions.push(permission);
        }

        Ok(permissions)
    }

    async fn begin_transaction(&self) -> Result<Box<dyn DatabaseTransaction>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let native_transaction = conn
            .begin_transaction()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to begin transaction: {}", e)))?;

        Ok(Box::new(
            crate::database::transaction_bridge::TransactionBridge::new(native_transaction),
        ))
    }

    // User attributes operations
    async fn set_attribute(
        &self,
        user_id: UserId,
        request: UserAttributeRequest,
    ) -> Result<UserAttribute> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let attribute_id = uuid::Uuid::new_v4().to_string();
        let user_id_str = user_id.to_string();
        let now = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();

        // Determine value type and column values
        let (value_type, value_string, value_number, value_boolean, value_json) =
            match &request.value {
                UserAttributeValue::String(s) => ("string", Some(s.clone()), None, None, None),
                UserAttributeValue::Number(n) => ("number", None, Some(*n), None, None),
                UserAttributeValue::Integer(i) => ("number", None, Some(*i as f64), None, None),
                UserAttributeValue::Boolean(b) => ("boolean", None, None, Some(*b), None),
                UserAttributeValue::Json(j) => (
                    "json",
                    None,
                    None,
                    None,
                    Some(serde_json::to_string(j).unwrap_or_default()),
                ),
                UserAttributeValue::Array(a) => (
                    "json",
                    None,
                    None,
                    None,
                    Some(serde_json::to_string(a).unwrap_or_default()),
                ),
            };

        // Use INSERT ... ON DUPLICATE KEY UPDATE for upsert behavior
        let sql = "INSERT INTO user_attributes 
                   (id, user_id, `key`, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, 
                           COALESCE((SELECT created_at FROM user_attributes WHERE user_id = ? AND `key` = ?), ?),
                           ?)
                   ON DUPLICATE KEY UPDATE 
                       value_type = VALUES(value_type),
                       value_string = VALUES(value_string),
                       value_number = VALUES(value_number),
                       value_boolean = VALUES(value_boolean),
                       value_json = VALUES(value_json),
                       updated_at = VALUES(updated_at)";

        let params = vec![
            DatabaseValue::Text(attribute_id.clone()),
            DatabaseValue::Text(user_id_str),
            DatabaseValue::Text(request.key.clone()),
            DatabaseValue::Text(value_type.to_string()),
            value_string
                .map(DatabaseValue::Text)
                .unwrap_or(DatabaseValue::Null),
            value_number
                .map(DatabaseValue::F64)
                .unwrap_or(DatabaseValue::Null),
            value_boolean
                .map(DatabaseValue::Bool)
                .unwrap_or(DatabaseValue::Null),
            value_json
                .map(DatabaseValue::Text)
                .unwrap_or(DatabaseValue::Null),
            DatabaseValue::Text(user_id.to_string()),
            DatabaseValue::Text(request.key.clone()),
            DatabaseValue::Text(now.clone()),
            DatabaseValue::Text(now),
        ];

        conn.execute(sql, &params)
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to set user attribute: {}", e)))?;

        // Return the created/updated attribute
        Ok(UserAttribute {
            id: crate::types::UserAttributeId::from(uuid::Uuid::parse_str(&attribute_id).unwrap()),
            user_id,
            key: request.key,
            value: request.value,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            metadata: request.metadata.unwrap_or_default(),
        })
    }

    async fn get_attribute(&self, user_id: UserId, key: &str) -> Result<Option<UserAttribute>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let sql = "SELECT id, user_id, `key`, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                   FROM user_attributes WHERE user_id = ? AND `key` = ?";

        let params = vec![
            DatabaseValue::Text(user_id.to_string()),
            DatabaseValue::Text(key.to_string()),
        ];

        let rows = conn
            .query_all(sql, &params)
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get user attribute: {}", e)))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        Ok(Some(self.row_to_user_attribute(row.as_ref())?))
    }

    async fn get_user_attributes(&self, user_id: UserId) -> Result<UserAttributes> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let sql = "SELECT id, user_id, `key`, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                   FROM user_attributes WHERE user_id = ? ORDER BY `key`";

        let params = vec![DatabaseValue::Text(user_id.to_string())];

        let rows = conn
            .query_all(sql, &params)
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get user attributes: {}", e)))?;

        let mut attributes = std::collections::HashMap::new();
        let mut last_updated = chrono::Utc::now();

        for row in rows {
            let attr = self.row_to_user_attribute(row.as_ref())?;
            if attr.updated_at > last_updated {
                last_updated = attr.updated_at;
            }
            attributes.insert(attr.key.clone(), attr.value);
        }

        Ok(UserAttributes {
            user_id,
            attributes,
            last_updated,
        })
    }

    async fn delete_attribute(&self, user_id: UserId, key: &str) -> Result<bool> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let sql = "DELETE FROM user_attributes WHERE user_id = ? AND `key` = ?";
        let params = vec![
            DatabaseValue::Text(user_id.to_string()),
            DatabaseValue::Text(key.to_string()),
        ];

        let affected = conn
            .execute(sql, &params)
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to delete user attribute: {}", e)))?;

        Ok(affected > 0)
    }

    async fn delete_user_attributes(&self, user_id: UserId) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let sql = "DELETE FROM user_attributes WHERE user_id = ?";
        let params = vec![DatabaseValue::Text(user_id.to_string())];

        let affected = conn.execute(sql, &params).await.map_err(|e| {
            AuthError::internal(&format!("Failed to delete user attributes: {}", e))
        })?;

        Ok(affected as u64)
    }

    async fn update_attribute(
        &self,
        user_id: UserId,
        key: &str,
        value: UserAttributeValue,
    ) -> Result<UserAttribute> {
        // Use set_attribute for upsert behavior
        let request = UserAttributeRequest {
            key: key.to_string(),
            value,
            metadata: None,
        };
        self.set_attribute(user_id, request).await
    }

    async fn search_attributes(&self, filter: UserAttributeFilter) -> Result<Vec<UserAttribute>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let mut sql = "SELECT id, user_id, `key`, value_type, value_string, value_number, value_boolean, value_json, created_at, updated_at 
                       FROM user_attributes WHERE 1=1".to_string();
        let mut params = Vec::new();

        if let Some(user_id) = filter.user_id {
            sql.push_str(" AND user_id = ?");
            params.push(DatabaseValue::Text(user_id.to_string()));
        }

        if let Some(keys) = filter.keys {
            if !keys.is_empty() {
                let placeholders = keys.iter().map(|_| "?").collect::<Vec<_>>().join(",");
                sql.push_str(&format!(" AND `key` IN ({})", placeholders));
                for key in keys {
                    params.push(DatabaseValue::Text(key));
                }
            }
        }

        if let Some(value_type) = filter.value_type {
            sql.push_str(" AND value_type = ?");
            params.push(DatabaseValue::Text(value_type));
        }

        if let Some(created_after) = filter.created_after {
            sql.push_str(" AND created_at > ?");
            params.push(DatabaseValue::Text(
                created_after.format("%Y-%m-%d %H:%M:%S").to_string(),
            ));
        }

        if let Some(created_before) = filter.created_before {
            sql.push_str(" AND created_at < ?");
            params.push(DatabaseValue::Text(
                created_before.format("%Y-%m-%d %H:%M:%S").to_string(),
            ));
        }

        sql.push_str(" ORDER BY created_at DESC");

        if let Some(limit) = filter.limit {
            sql.push_str(&format!(" LIMIT {}", limit));
            if let Some(offset) = filter.offset {
                sql.push_str(&format!(" OFFSET {}", offset));
            }
        }

        let rows = conn.query_all(&sql, &params).await.map_err(|e| {
            AuthError::internal(&format!("Failed to search user attributes: {}", e))
        })?;

        let mut results = Vec::new();
        for row in rows {
            results.push(self.row_to_user_attribute(row.as_ref())?);
        }

        Ok(results)
    }

    async fn find_users_by_attribute(
        &self,
        key: &str,
        value: &UserAttributeValue,
    ) -> Result<Vec<UserId>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let (sql, params) = match value {
            UserAttributeValue::String(s) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE `key` = ? AND value_type = 'string' AND value_string = ?",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(s.clone())]
            ),
            UserAttributeValue::Number(n) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE `key` = ? AND value_type = 'number' AND value_number = ?",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::F64(*n)]
            ),
            UserAttributeValue::Integer(i) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE `key` = ? AND value_type = 'number' AND value_number = ?",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::F64(*i as f64)]
            ),
            UserAttributeValue::Boolean(b) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE `key` = ? AND value_type = 'boolean' AND value_boolean = ?",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Bool(*b)]
            ),
            UserAttributeValue::Json(j) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE `key` = ? AND value_type = 'json' AND value_json = ?",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(serde_json::to_string(j).unwrap_or_default())]
            ),
            UserAttributeValue::Array(a) => (
                "SELECT DISTINCT user_id FROM user_attributes WHERE `key` = ? AND value_type = 'json' AND value_json = ?",
                vec![DatabaseValue::Text(key.to_string()), DatabaseValue::Text(serde_json::to_string(a).unwrap_or_default())]
            ),
        };

        let rows = conn.query_all(sql, &params).await.map_err(|e| {
            AuthError::internal(&format!("Failed to find users by attribute: {}", e))
        })?;

        let mut user_ids = Vec::new();
        for row in rows {
            if let Ok(DatabaseValue::Text(user_id_str)) = row.get_by_index(0) {
                if let Ok(uuid) = uuid::Uuid::parse_str(&user_id_str) {
                    user_ids.push(UserId::from(uuid));
                }
            }
        }

        Ok(user_ids)
    }

    async fn set_attributes(
        &self,
        user_id: UserId,
        attributes: std::collections::HashMap<String, UserAttributeValue>,
    ) -> Result<Vec<UserAttribute>> {
        let mut results = Vec::new();

        for (key, value) in attributes {
            let request = UserAttributeRequest {
                key,
                value,
                metadata: None,
            };
            let attr = self.set_attribute(user_id, request).await?;
            results.push(attr);
        }

        Ok(results)
    }

    // User operations
    async fn create_user(&self, user: &User) -> Result<UserId> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let created_at = user.created_at.timestamp();
        let updated_at = user.updated_at.timestamp();
        let last_login = user.last_login.map(|dt| dt.timestamp());
        let metadata_json = serde_json::to_string(&user.metadata).unwrap_or_default();

        conn.execute(
            "INSERT INTO users (id, username, email, password_hash, salt, created_at, updated_at, 
             last_login, role, status, is_verified, login_attempts, metadata) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            &[
                DatabaseValue::Text(user.id.to_string()),
                DatabaseValue::Text(user.username.clone()),
                DatabaseValue::Text(user.email.clone()),
                DatabaseValue::Text(user.password_hash.clone()),
                DatabaseValue::Text(user.salt.clone()),
                DatabaseValue::I64(created_at),
                DatabaseValue::I64(updated_at),
                last_login
                    .map(DatabaseValue::I64)
                    .unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(user.role.to_string()),
                DatabaseValue::Text(user.status.to_string()),
                DatabaseValue::I64(if user.is_verified { 1 } else { 0 }),
                DatabaseValue::I64(user.login_attempts as i64),
                DatabaseValue::Text(metadata_json),
            ],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to create user: {}", e)))?;

        Ok(user.id)
    }

    async fn get_user_by_id(&self, user_id: UserId) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                "SELECT * FROM users WHERE id = ?",
                &[DatabaseValue::Text(user_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to query user by id: {}", e)))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let user = self.row_to_user(row.as_ref())?;
        Ok(Some(user))
    }

    async fn get_user_by_username(&self, username: &str) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                "SELECT * FROM users WHERE username = ?",
                &[DatabaseValue::Text(username.to_string())],
            )
            .await
            .map_err(|e| {
                AuthError::internal(&format!("Failed to query user by username: {}", e))
            })?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let user = self.row_to_user(row.as_ref())?;
        Ok(Some(user))
    }

    async fn get_user_by_email(&self, email: &str) -> Result<Option<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                "SELECT * FROM users WHERE email = ?",
                &[DatabaseValue::Text(email.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to query user by email: {}", e)))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let user = self.row_to_user(row.as_ref())?;
        Ok(Some(user))
    }

    async fn update_user(&self, user: &User) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let updated_at = user.updated_at.timestamp();
        let last_login = user.last_login.map(|dt| dt.timestamp());
        let metadata_json = serde_json::to_string(&user.metadata).unwrap_or_default();

        conn.execute(
            "UPDATE users SET username = ?, email = ?, password_hash = ?, salt = ?, updated_at = ?, 
             last_login = ?, role = ?, status = ?, is_verified = ?, login_attempts = ?, metadata = ? 
             WHERE id = ?",
            &[
                DatabaseValue::Text(user.username.clone()),
                DatabaseValue::Text(user.email.clone()),
                DatabaseValue::Text(user.password_hash.clone()),
                DatabaseValue::Text(user.salt.clone()),
                DatabaseValue::I64(updated_at),
                last_login.map(DatabaseValue::I64).unwrap_or(DatabaseValue::Null),
                DatabaseValue::Text(user.role.to_string()),
                DatabaseValue::Text(user.status.to_string()),
                DatabaseValue::I64(if user.is_verified { 1 } else { 0 }),
                DatabaseValue::I64(user.login_attempts as i64),
                DatabaseValue::Text(metadata_json),
                DatabaseValue::Text(user.id.to_string()),
            ]
        ).await.map_err(|e| AuthError::internal(&format!("Failed to update user: {}", e)))?;

        Ok(())
    }

    async fn delete_user(&self, user_id: UserId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        conn.execute(
            "DELETE FROM users WHERE id = ?",
            &[DatabaseValue::Text(user_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to delete user: {}", e)))?;

        Ok(())
    }

    async fn list_users(&self, offset: u64, limit: u64) -> Result<Vec<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                "SELECT * FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?",
                &[
                    DatabaseValue::I64(limit as i64),
                    DatabaseValue::I64(offset as i64),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to list users: {}", e)))?;

        let mut users = Vec::new();
        for row in rows {
            let user = self.row_to_user(row.as_ref())?;
            users.push(user);
        }

        Ok(users)
    }

    async fn count_users(&self) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let row = conn
            .query_one("SELECT COUNT(*) FROM users", &[])
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to count users: {}", e)))?;

        let count = match row.get_by_index(0)? {
            DatabaseValue::I64(count) => count as u64,
            DatabaseValue::I32(count) => count as u64,
            _ => 0,
        };

        Ok(count)
    }

    // Session operations
    async fn create_session(&self, session: &Session) -> Result<SessionId> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let created_at = session.created_at.timestamp();
        let expires_at = session.expires_at.timestamp();
        let last_accessed = session.last_accessed.timestamp();
        let ip_address = session.ip_address.map(|ip| ip.to_string());
        let session_data_json = serde_json::to_string(&session.session_data).unwrap_or_default();
        let metadata_json = serde_json::to_string(&session.metadata).unwrap_or_default();

        conn.execute(
            "INSERT INTO sessions (id, user_id, token, refresh_token, expires_at, created_at, last_accessed, 
             ip_address, user_agent, is_active, session_data, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            &[
                DatabaseValue::Text(session.id.to_string()),
                DatabaseValue::Text(session.user_id.to_string()),
                DatabaseValue::Text(session.token.clone()),
                session.refresh_token.as_ref().map(|t| DatabaseValue::Text(t.clone())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(expires_at),
                DatabaseValue::I64(created_at),
                DatabaseValue::I64(last_accessed),
                ip_address.map(DatabaseValue::Text).unwrap_or(DatabaseValue::Null),
                session.user_agent.as_ref().map(|ua| DatabaseValue::Text(ua.clone())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(if session.is_active { 1 } else { 0 }),
                DatabaseValue::Text(session_data_json),
                DatabaseValue::Text(metadata_json),
            ]
        ).await.map_err(|e| AuthError::internal(&format!("Failed to create session: {}", e)))?;

        Ok(session.id)
    }

    async fn get_session_by_id(&self, session_id: SessionId) -> Result<Option<Session>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn
            .query_all(
                "SELECT * FROM sessions WHERE id = ?",
                &[DatabaseValue::Text(session_id.to_string())],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to query session by id: {}", e)))?;

        if rows.is_empty() {
            return Ok(None);
        }

        let row = &rows[0];
        let session = self.row_to_session(row.as_ref())?;
        Ok(Some(session))
    }

    async fn update_session(&self, session: &Session) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let expires_at = session.expires_at.timestamp();
        let last_accessed = session.last_accessed.timestamp();
        let ip_address = session.ip_address.map(|ip| ip.to_string());
        let session_data_json = serde_json::to_string(&session.session_data).unwrap_or_default();
        let metadata_json = serde_json::to_string(&session.metadata).unwrap_or_default();

        conn.execute(
            "UPDATE sessions SET user_id = ?, token = ?, refresh_token = ?, expires_at = ?, last_accessed = ?, 
             ip_address = ?, user_agent = ?, is_active = ?, session_data = ?, metadata = ? WHERE id = ?",
            &[
                DatabaseValue::Text(session.user_id.to_string()),
                DatabaseValue::Text(session.token.clone()),
                session.refresh_token.as_ref().map(|t| DatabaseValue::Text(t.clone())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(expires_at),
                DatabaseValue::I64(last_accessed),
                ip_address.map(DatabaseValue::Text).unwrap_or(DatabaseValue::Null),
                session.user_agent.as_ref().map(|ua| DatabaseValue::Text(ua.clone())).unwrap_or(DatabaseValue::Null),
                DatabaseValue::I64(if session.is_active { 1 } else { 0 }),
                DatabaseValue::Text(session_data_json),
                DatabaseValue::Text(metadata_json),
                DatabaseValue::Text(session.id.to_string()),
            ]
        ).await.map_err(|e| AuthError::internal(&format!("Failed to update session: {}", e)))?;

        Ok(())
    }

    async fn delete_session(&self, session_id: SessionId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        conn.execute(
            "DELETE FROM sessions WHERE id = ?",
            &[DatabaseValue::Text(session_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to delete session: {}", e)))?;

        Ok(())
    }

    async fn delete_user_sessions(&self, user_id: UserId) -> Result<()> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        conn.execute(
            "DELETE FROM sessions WHERE user_id = ?",
            &[DatabaseValue::Text(user_id.to_string())],
        )
        .await
        .map_err(|e| AuthError::internal(&format!("Failed to delete user sessions: {}", e)))?;

        Ok(())
    }

    async fn list_user_sessions(&self, user_id: UserId) -> Result<Vec<Session>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let rows = conn.query_all("SELECT * FROM sessions WHERE user_id = ? AND is_active = true ORDER BY last_accessed DESC",
            &[DatabaseValue::Text(user_id.to_string())])
            .await.map_err(|e| AuthError::internal(&format!("Failed to list user sessions: {}", e)))?;

        let mut sessions = Vec::new();
        for row in rows {
            let session = self.row_to_session(row.as_ref())?;
            sessions.push(session);
        }

        Ok(sessions)
    }

    async fn cleanup_expired_sessions(&self) -> Result<u64> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;

        let result = conn
            .execute(
                "DELETE FROM sessions WHERE expires_at < ?",
                &[DatabaseValue::I64(current_time)],
            )
            .await
            .map_err(|e| {
                AuthError::internal(&format!("Failed to cleanup expired sessions: {}", e))
            })?;

        Ok(result)
    }
}

impl NativeMysqlProvider {
    /// MySQL-specific: Search users using FULLTEXT search
    pub async fn search_users_fulltext(&self, query: &str, limit: u64) -> Result<Vec<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Use MySQL's FULLTEXT search capabilities
        let rows = conn
            .query_all(
                "SELECT * FROM users 
                 WHERE MATCH(username, email) AGAINST(? IN NATURAL LANGUAGE MODE)
                 ORDER BY MATCH(username, email) AGAINST(? IN NATURAL LANGUAGE MODE) DESC
                 LIMIT ?",
                &[
                    DatabaseValue::Text(query.to_string()),
                    DatabaseValue::Text(query.to_string()),
                    DatabaseValue::I64(limit as i64),
                ],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to search users: {}", e)))?;

        let mut users = Vec::new();
        for row in rows {
            users.push(self.row_to_user(&*row)?);
        }

        Ok(users)
    }

    /// MySQL-specific: Query users by JSON metadata fields
    pub async fn query_users_by_json_metadata(
        &self,
        json_path: &str,
        value: &str,
        limit: u64,
    ) -> Result<Vec<User>> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Use MySQL's JSON functions for querying
        let rows = conn
            .query_all(
                "SELECT * FROM users 
                 WHERE JSON_EXTRACT(metadata, ?) = ?
                 ORDER BY created_at DESC
                 LIMIT ?",
                &[
                    DatabaseValue::Text(json_path.to_string()),
                    DatabaseValue::Text(value.to_string()),
                    DatabaseValue::I64(limit as i64),
                ],
            )
            .await
            .map_err(|e| {
                AuthError::internal(&format!("Failed to query users by JSON metadata: {}", e))
            })?;

        let mut users = Vec::new();
        for row in rows {
            users.push(self.row_to_user(&*row)?);
        }

        Ok(users)
    }

    /// MySQL-specific: Get database engine information
    pub async fn get_mysql_engine_info(&self) -> Result<MySqlEngineInfo> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        // Get MySQL version and engine information
        let version_row = conn
            .query_one("SELECT VERSION() as version", &[])
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get MySQL version: {}", e)))?;

        let version = match version_row.get_by_index(0)? {
            DatabaseValue::Text(v) => v,
            _ => "Unknown".to_string(),
        };

        // Get storage engine information
        let engine_row = conn
            .query_one(
                "SELECT ENGINE FROM information_schema.TABLES 
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'users' LIMIT 1",
                &[],
            )
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get storage engine: {}", e)))?;

        let storage_engine = match engine_row.get_by_index(0)? {
            DatabaseValue::Text(e) => e,
            _ => "Unknown".to_string(),
        };

        // Get connection information
        let connection_info =
            self.pool.get_connection_info().await.map_err(|e| {
                AuthError::internal(&format!("Failed to get connection info: {}", e))
            })?;

        Ok(MySqlEngineInfo {
            version,
            storage_engine,
            max_connections: connection_info.max_connections,
            active_connections: connection_info.active_connections,
            charset: "utf8mb4".to_string(), // Default charset we use
            collation: "utf8mb4_unicode_ci".to_string(), // Default collation we use
        })
    }

    /// MySQL-specific: Optimize tables for better performance
    pub async fn optimize_tables(&self) -> Result<MySqlOptimizationResult> {
        let mut conn = self
            .pool
            .get_connection()
            .await
            .map_err(|e| AuthError::internal(&format!("Failed to get connection: {}", e)))?;

        let start_time = std::time::Instant::now();
        let mut optimized_tables = Vec::new();

        // Optimize users table
        match conn.execute("OPTIMIZE TABLE users", &[]).await {
            Ok(_) => optimized_tables.push("users".to_string()),
            Err(e) => {
                return Err(AuthError::internal(&format!(
                    "Failed to optimize users table: {}",
                    e
                )))
            }
        }

        // Optimize sessions table
        match conn.execute("OPTIMIZE TABLE sessions", &[]).await {
            Ok(_) => optimized_tables.push("sessions".to_string()),
            Err(e) => {
                return Err(AuthError::internal(&format!(
                    "Failed to optimize sessions table: {}",
                    e
                )))
            }
        }

        let optimization_time = start_time.elapsed();

        Ok(MySqlOptimizationResult {
            optimized_tables,
            optimization_time,
            success: true,
        })
    }
}

/// MySQL-specific engine information
#[derive(Debug, Clone)]
pub struct MySqlEngineInfo {
    pub version: String,
    pub storage_engine: String,
    pub max_connections: u32,
    pub active_connections: u32,
    pub charset: String,
    pub collation: String,
}

/// MySQL table optimization results
#[derive(Debug, Clone)]
pub struct MySqlOptimizationResult {
    pub optimized_tables: Vec<String>,
    pub optimization_time: std::time::Duration,
    pub success: bool,
}

/// MySQL connection information
#[derive(Debug, Clone)]
pub struct MySqlConnectionInfo {
    pub max_connections: u32,
    pub active_connections: u32,
}
