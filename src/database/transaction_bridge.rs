//! Transaction bridge between native database transactions and auth database traits

use async_trait::async_trait;

use crate::{
    database::{
        native::common::traits::DatabaseTransaction as NativeDatabaseTransaction,
        DatabaseTransaction,
    },
    error::{AuthError, Result},
    types::{Session, SessionId, User, UserId},
};

/// Bridge wrapper that implements auth DatabaseTransaction trait using native database transactions
pub struct TransactionBridge {
    native_transaction: Box<dyn NativeDatabaseTransaction>,
}

impl TransactionBridge {
    pub fn new(native_transaction: Box<dyn NativeDatabaseTransaction>) -> Self {
        Self { native_transaction }
    }
}

impl std::fmt::Debug for TransactionBridge {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("TransactionBridge").finish()
    }
}

#[async_trait]
impl DatabaseTransaction for TransactionBridge {
    async fn commit(self: Box<Self>) -> Result<()> {
        self.native_transaction
            .commit()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to commit transaction: {e}")))
    }

    async fn rollback(self: Box<Self>) -> Result<()> {
        self.native_transaction
            .rollback()
            .await
            .map_err(|e| AuthError::internal(format!("Failed to rollback transaction: {e}")))
    }

    async fn create_user(&mut self, _user: &User) -> Result<UserId> {
        // For now, return an error as we need to implement the full bridge
        // This would require implementing all the database operations within transactions
        Err(AuthError::internal(
            "Transaction user operations not yet implemented",
        ))
    }

    async fn create_session(&mut self, _session: &Session) -> Result<SessionId> {
        // For now, return an error as we need to implement the full bridge
        Err(AuthError::internal(
            "Transaction session operations not yet implemented",
        ))
    }

    async fn update_user(&mut self, _user: &User) -> Result<()> {
        // For now, return an error as we need to implement the full bridge
        Err(AuthError::internal(
            "Transaction user operations not yet implemented",
        ))
    }
}
