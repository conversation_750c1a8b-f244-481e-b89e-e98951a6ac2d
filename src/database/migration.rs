//! Database migration management

use crate::error::{Auth<PERSON><PERSON><PERSON>, Result};
// use std::collections::HashMap; // Reserved for future use

/// Database migration
#[derive(Debug, <PERSON><PERSON>)]
pub struct Migration {
    pub version: u64,
    pub name: String,
    pub up_sql: String,
    pub down_sql: String,
    pub checksum: String,
}

/// Migration status
#[derive(Debu<PERSON>, Clone, PartialEq, Eq)]
pub enum MigrationStatus {
    Pending,
    Applied,
    Failed,
    RolledBack,
}

/// Migration result
#[derive(Debug, Clone)]
pub struct MigrationResult {
    pub version: u64,
    pub status: MigrationStatus,
    pub applied_at: Option<chrono::DateTime<chrono::Utc>>,
    pub execution_time: Option<std::time::Duration>,
    pub error: Option<String>,
}

/// Migration manager trait
#[async_trait::async_trait]
pub trait MigrationManager: Send + Sync {
    /// Get all available migrations
    async fn get_migrations(&self) -> Result<Vec<Migration>>;

    /// Get applied migrations
    async fn get_applied_migrations(&self) -> Result<Vec<MigrationResult>>;

    /// Apply pending migrations
    async fn migrate(&self) -> Result<Vec<MigrationResult>>;

    /// Rollback to a specific version
    async fn rollback_to(&self, version: u64) -> Result<Vec<MigrationResult>>;

    /// Validate migration integrity
    async fn validate(&self) -> Result<()>;

    /// Get current schema version
    async fn current_version(&self) -> Result<Option<u64>>;
}

/// Built-in migrations for IndidusAuth
pub struct BuiltinMigrations;

impl BuiltinMigrations {
    /// Get all built-in migrations for PostgreSQL
    pub fn get_postgres_migrations() -> Vec<Migration> {
        vec![
            Migration {
                version: 1,
                name: "create_users_table".to_string(),
                up_sql: r#"CREATE TABLE users (
                    id UUID PRIMARY KEY,
                    username VARCHAR(255) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt VARCHAR(255) NOT NULL DEFAULT '',
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    last_login TIMESTAMPTZ,
                    is_active BOOLEAN NOT NULL DEFAULT true,
                    is_verified BOOLEAN NOT NULL DEFAULT false,
                    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
                    locked_until TIMESTAMPTZ,
                    metadata JSONB NOT NULL DEFAULT '{}'::jsonb
                );
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username ON users(username);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active ON users(is_active) WHERE is_active = true;
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_metadata_gin ON users USING gin(metadata);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at);
                
                -- Add trigger to automatically update updated_at
                CREATE OR REPLACE FUNCTION update_updated_at_column()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = NOW();
                    RETURN NEW;
                END;
                $$ language 'plpgsql';
                
                CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
                    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();"#.to_string(),
                down_sql: "DROP TRIGGER IF EXISTS update_users_updated_at ON users; DROP FUNCTION IF EXISTS update_updated_at_column(); DROP TABLE users;".to_string(),
                checksum: "sha256:postgres_users_v1".to_string(),
            },
            Migration {
                version: 2,
                name: "create_sessions_table".to_string(),
                up_sql: r#"CREATE TABLE sessions (
                    id UUID PRIMARY KEY,
                    user_id UUID NOT NULL,
                    token VARCHAR(512) NOT NULL,
                    refresh_token VARCHAR(512),
                    expires_at TIMESTAMPTZ NOT NULL,
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    last_accessed TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    ip_address INET,
                    user_agent TEXT,
                    is_active BOOLEAN NOT NULL DEFAULT true,
                    session_data JSONB NOT NULL DEFAULT '{}'::jsonb,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                );
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_token ON sessions(token);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_active ON sessions(is_active, expires_at) WHERE is_active = true;
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_user_active ON sessions(user_id, is_active) WHERE is_active = true;
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_data_gin ON sessions USING gin(session_data);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_ip_address ON sessions(ip_address) WHERE ip_address IS NOT NULL;
                
                -- Add partial index for cleanup operations
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_expired ON sessions(expires_at) WHERE expires_at < NOW();"#.to_string(),
                down_sql: "DROP TABLE sessions;".to_string(),
                checksum: "sha256:postgres_sessions_v1".to_string(),
            },
            Migration {
                version: 3,
                name: "create_user_attributes_table".to_string(),
                up_sql: r#"CREATE TABLE user_attributes (
                    id UUID PRIMARY KEY,
                    user_id UUID NOT NULL,
                    key VARCHAR(255) NOT NULL,
                    value_type VARCHAR(50) NOT NULL,
                    value_string TEXT,
                    value_number DOUBLE PRECISION,
                    value_integer BIGINT,
                    value_boolean BOOLEAN,
                    value_json JSONB,
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE(user_id, key)
                );
                
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_user_id ON user_attributes(user_id);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_key ON user_attributes(key);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_user_key ON user_attributes(user_id, key);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_type ON user_attributes(value_type);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_string ON user_attributes(value_string) WHERE value_type = 'string';
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_number ON user_attributes(value_number) WHERE value_type IN ('number', 'integer');
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_boolean ON user_attributes(value_boolean) WHERE value_type = 'boolean';
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_json_gin ON user_attributes USING gin(value_json) WHERE value_type IN ('json', 'array');
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_created_at ON user_attributes(created_at);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_attributes_updated_at ON user_attributes(updated_at);
                
                -- Add trigger to automatically update updated_at
                CREATE TRIGGER update_user_attributes_updated_at BEFORE UPDATE ON user_attributes
                    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();"#.to_string(),
                down_sql: "DROP TRIGGER IF EXISTS update_user_attributes_updated_at ON user_attributes; DROP TABLE user_attributes;".to_string(),
                checksum: "sha256:postgres_user_attributes_v1".to_string(),
            },
            Migration {
                version: 4,
                name: "create_rbac_tables".to_string(),
                up_sql: r#"-- Create permissions table
                CREATE TABLE permissions (
                    id UUID PRIMARY KEY,
                    name VARCHAR(255) UNIQUE NOT NULL,
                    description TEXT,
                    resource VARCHAR(255) NOT NULL,
                    action VARCHAR(255) NOT NULL,
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
                    UNIQUE(resource, action)
                );
                
                -- Create roles table
                CREATE TABLE roles (
                    id UUID PRIMARY KEY,
                    name VARCHAR(255) UNIQUE NOT NULL,
                    description TEXT,
                    parent_id UUID,
                    level INTEGER NOT NULL DEFAULT 0,
                    is_system BOOLEAN NOT NULL DEFAULT false,
                    is_active BOOLEAN NOT NULL DEFAULT true,
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
                    FOREIGN KEY (parent_id) REFERENCES roles(id) ON DELETE SET NULL
                );
                
                -- Create role_permissions table
                CREATE TABLE role_permissions (
                    role_id UUID NOT NULL,
                    permission_id UUID NOT NULL,
                    granted BOOLEAN NOT NULL DEFAULT true,
                    granted_by UUID,
                    granted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
                    PRIMARY KEY (role_id, permission_id),
                    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
                    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
                );
                
                -- Create user_role_assignments table
                CREATE TABLE user_role_assignments (
                    user_id UUID NOT NULL,
                    role_id UUID NOT NULL,
                    assigned_by UUID,
                    assigned_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    expires_at TIMESTAMPTZ,
                    is_active BOOLEAN NOT NULL DEFAULT true,
                    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
                    PRIMARY KEY (user_id, role_id),
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
                );
                
                -- Create indexes for permissions
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_name ON permissions(name);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_resource ON permissions(resource);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_action ON permissions(action);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);
                
                -- Create indexes for roles
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_name ON roles(name);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_parent_id ON roles(parent_id);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_level ON roles(level);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_system ON roles(is_system);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_active ON roles(is_active) WHERE is_active = true;
                
                -- Create indexes for role_permissions
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_role_permissions_granted ON role_permissions(granted) WHERE granted = true;
                
                -- Create indexes for user_role_assignments
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_role_assignments_user_id ON user_role_assignments(user_id);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_role_assignments_role_id ON user_role_assignments(role_id);
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_role_assignments_active ON user_role_assignments(is_active, expires_at) WHERE is_active = true;
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_role_assignments_expires ON user_role_assignments(expires_at) WHERE expires_at IS NOT NULL;
                
                -- Add triggers to automatically update updated_at
                CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions
                    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
                    
                CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
                    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
                
                -- Insert default system roles
                INSERT INTO roles (id, name, description, level, is_system, is_active) VALUES
                    ('00000000-0000-0000-0000-000000000001', 'super_admin', 'Super Administrator with all permissions', 0, true, true),
                    ('00000000-0000-0000-0000-000000000002', 'admin', 'Administrator with most permissions', 1, true, true),
                    ('00000000-0000-0000-0000-000000000003', 'moderator', 'Moderator with limited permissions', 2, true, true),
                    ('00000000-0000-0000-0000-000000000004', 'user', 'Regular user with basic permissions', 3, true, true);
                
                -- Insert default permissions
                INSERT INTO permissions (id, name, description, resource, action) VALUES
                    ('10000000-0000-0000-0000-000000000001', 'users:read', 'Read user information', 'users', 'read'),
                    ('10000000-0000-0000-0000-000000000002', 'users:write', 'Create and update users', 'users', 'write'),
                    ('10000000-0000-0000-0000-000000000003', 'users:delete', 'Delete users', 'users', 'delete'),
                    ('10000000-0000-0000-0000-000000000004', 'roles:read', 'Read role information', 'roles', 'read'),
                    ('10000000-0000-0000-0000-000000000005', 'roles:write', 'Create and update roles', 'roles', 'write'),
                    ('10000000-0000-0000-0000-000000000006', 'roles:delete', 'Delete roles', 'roles', 'delete'),
                    ('10000000-0000-0000-0000-000000000007', 'permissions:read', 'Read permission information', 'permissions', 'read'),
                    ('10000000-0000-0000-0000-000000000008', 'permissions:write', 'Create and update permissions', 'permissions', 'write'),
                    ('10000000-0000-0000-0000-000000000009', 'permissions:delete', 'Delete permissions', 'permissions', 'delete'),
                    ('10000000-0000-0000-0000-000000000010', 'system:admin', 'System administration', 'system', 'admin');
                
                -- Assign permissions to default roles
                INSERT INTO role_permissions (role_id, permission_id, granted) VALUES
                    -- Super Admin gets all permissions
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000003', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000004', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000005', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000006', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000007', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000008', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000009', true),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000010', true),
                    -- Admin gets most permissions except system admin
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', true),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000002', true),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003', true),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000004', true),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000005', true),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000007', true),
                    -- Moderator gets read permissions and limited write
                    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', true),
                    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000002', true),
                    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000004', true),
                    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000007', true),
                    -- User gets basic read permissions
                    ('00000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', true);"#.to_string(),
                down_sql: r#"DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
                DROP TRIGGER IF EXISTS update_permissions_updated_at ON permissions;
                DROP TABLE IF EXISTS user_role_assignments;
                DROP TABLE IF EXISTS role_permissions;
                DROP TABLE IF EXISTS roles;
                DROP TABLE IF EXISTS permissions;"#.to_string(),
                checksum: "sha256:postgres_rbac_v1".to_string(),
            },
        ]
    }

    /// Get all built-in migrations for SQLite
    pub fn get_sqlite_migrations() -> Vec<Migration> {
        vec![
            Migration {
                version: 1,
                name: "create_users_table".to_string(),
                up_sql: r#"CREATE TABLE users (
                    id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL DEFAULT '',
                    role TEXT NOT NULL DEFAULT 'user',
                    status TEXT NOT NULL DEFAULT 'active',
                    created_at TEXT NOT NULL DEFAULT (datetime('now')),
                    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                    last_login TEXT,
                    is_active INTEGER NOT NULL DEFAULT 1,
                    is_verified INTEGER NOT NULL DEFAULT 0,
                    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
                    login_attempts INTEGER NOT NULL DEFAULT 0,
                    locked_until TEXT,
                    email_verified INTEGER NOT NULL DEFAULT 0,
                    email_verification_token TEXT,
                    password_reset_token TEXT,
                    password_reset_expires TEXT,
                    metadata TEXT NOT NULL DEFAULT '{}'
                );
                CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
                CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
                CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active) WHERE is_active = 1;
                CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
                
                -- Add trigger to automatically update updated_at
                CREATE TRIGGER update_users_updated_at 
                    AFTER UPDATE ON users
                    FOR EACH ROW
                    WHEN OLD.updated_at = NEW.updated_at
                BEGIN
                    UPDATE users SET updated_at = strftime('%Y-%m-%dT%H:%M:%SZ', 'now') WHERE id = NEW.id;
                END;"#.to_string(),
                down_sql: "DROP TRIGGER IF EXISTS update_users_updated_at; DROP TABLE IF EXISTS users;".to_string(),
                checksum: "sha256:sqlite_users_v1".to_string(),
            },
            Migration {
                version: 2,
                name: "create_sessions_table".to_string(),
                up_sql: r#"CREATE TABLE sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    token TEXT NOT NULL,
                    refresh_token TEXT,
                    expires_at TEXT NOT NULL,
                    created_at TEXT NOT NULL DEFAULT (datetime('now')),
                    last_accessed TEXT NOT NULL DEFAULT (datetime('now')),
                    ip_address TEXT,
                    user_agent TEXT,
                    is_active INTEGER NOT NULL DEFAULT 1,
                    session_data TEXT NOT NULL DEFAULT '{}',
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                );
                CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
                CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(token);
                CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at);
                CREATE INDEX IF NOT EXISTS idx_sessions_active ON sessions(is_active, expires_at) WHERE is_active = 1;
                CREATE INDEX IF NOT EXISTS idx_sessions_user_active ON sessions(user_id, is_active) WHERE is_active = 1;
                CREATE INDEX IF NOT EXISTS idx_sessions_ip_address ON sessions(ip_address) WHERE ip_address IS NOT NULL;"#.to_string(),
                down_sql: "DROP TABLE IF EXISTS sessions;".to_string(),
                checksum: "sha256:sqlite_sessions_v1".to_string(),
            },
            Migration {
                version: 3,
                name: "create_user_attributes_table".to_string(),
                up_sql: r#"CREATE TABLE user_attributes (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    key TEXT NOT NULL,
                    value_type TEXT NOT NULL,
                    value_string TEXT,
                    value_number REAL,
                    value_integer INTEGER,
                    value_boolean INTEGER,
                    value_json TEXT,
                    created_at TEXT NOT NULL DEFAULT (datetime('now')),
                    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                    metadata TEXT NOT NULL DEFAULT '{}',
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE(user_id, key)
                );
                
                CREATE INDEX IF NOT EXISTS idx_user_attributes_user_id ON user_attributes(user_id);
                CREATE INDEX IF NOT EXISTS idx_user_attributes_key ON user_attributes(key);
                CREATE INDEX IF NOT EXISTS idx_user_attributes_user_key ON user_attributes(user_id, key);
                CREATE INDEX IF NOT EXISTS idx_user_attributes_type ON user_attributes(value_type);
                CREATE INDEX IF NOT EXISTS idx_user_attributes_string ON user_attributes(value_string) WHERE value_type = 'string';
                CREATE INDEX IF NOT EXISTS idx_user_attributes_number ON user_attributes(value_number) WHERE value_type IN ('number', 'integer');
                CREATE INDEX IF NOT EXISTS idx_user_attributes_boolean ON user_attributes(value_boolean) WHERE value_type = 'boolean';
                CREATE INDEX IF NOT EXISTS idx_user_attributes_created_at ON user_attributes(created_at);
                CREATE INDEX IF NOT EXISTS idx_user_attributes_updated_at ON user_attributes(updated_at);
                
                -- Add trigger to automatically update updated_at
                CREATE TRIGGER update_user_attributes_updated_at 
                    AFTER UPDATE ON user_attributes
                    FOR EACH ROW
                    WHEN OLD.updated_at = NEW.updated_at
                BEGIN
                    UPDATE user_attributes SET updated_at = strftime('%Y-%m-%dT%H:%M:%SZ', 'now') WHERE id = NEW.id;
                END;"#.to_string(),
                down_sql: "DROP TRIGGER IF EXISTS update_user_attributes_updated_at; DROP TABLE IF EXISTS user_attributes;".to_string(),
                checksum: "sha256:sqlite_user_attributes_v1".to_string(),
            },
            Migration {
                version: 4,
                name: "create_rbac_tables".to_string(),
                up_sql: r#"-- Create permissions table
                CREATE TABLE permissions (
                    id TEXT PRIMARY KEY,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    resource TEXT NOT NULL,
                    action TEXT NOT NULL,
                    created_at TEXT NOT NULL DEFAULT (datetime('now')),
                    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                    metadata TEXT NOT NULL DEFAULT '{}',
                    UNIQUE(resource, action)
                );
                
                -- Create roles table
                CREATE TABLE roles (
                    id TEXT PRIMARY KEY,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    parent_id TEXT,
                    level INTEGER NOT NULL DEFAULT 0,
                    is_system INTEGER NOT NULL DEFAULT 0,
                    is_active INTEGER NOT NULL DEFAULT 1,
                    created_at TEXT NOT NULL DEFAULT (datetime('now')),
                    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                    metadata TEXT NOT NULL DEFAULT '{}',
                    FOREIGN KEY (parent_id) REFERENCES roles(id) ON DELETE SET NULL
                );
                
                -- Create role_permissions table
                CREATE TABLE role_permissions (
                    role_id TEXT NOT NULL,
                    permission_id TEXT NOT NULL,
                    granted INTEGER NOT NULL DEFAULT 1,
                    granted_by TEXT,
                    granted_at TEXT NOT NULL DEFAULT (datetime('now')),
                    metadata TEXT NOT NULL DEFAULT '{}',
                    PRIMARY KEY (role_id, permission_id),
                    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
                    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
                );
                
                -- Create user_role_assignments table
                CREATE TABLE user_role_assignments (
                    user_id TEXT NOT NULL,
                    role_id TEXT NOT NULL,
                    assigned_by TEXT,
                    assigned_at TEXT NOT NULL DEFAULT (datetime('now')),
                    expires_at TEXT,
                    is_active INTEGER NOT NULL DEFAULT 1,
                    metadata TEXT NOT NULL DEFAULT '{}',
                    PRIMARY KEY (user_id, role_id),
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
                );
                
                -- Create indexes for permissions
                CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(name);
                CREATE INDEX IF NOT EXISTS idx_permissions_resource ON permissions(resource);
                CREATE INDEX IF NOT EXISTS idx_permissions_action ON permissions(action);
                CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);
                
                -- Create indexes for roles
                CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
                CREATE INDEX IF NOT EXISTS idx_roles_parent_id ON roles(parent_id);
                CREATE INDEX IF NOT EXISTS idx_roles_level ON roles(level);
                CREATE INDEX IF NOT EXISTS idx_roles_system ON roles(is_system);
                CREATE INDEX IF NOT EXISTS idx_roles_active ON roles(is_active) WHERE is_active = 1;
                
                -- Create indexes for role_permissions
                CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
                CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
                CREATE INDEX IF NOT EXISTS idx_role_permissions_granted ON role_permissions(granted) WHERE granted = 1;
                
                -- Create indexes for user_role_assignments
                CREATE INDEX IF NOT EXISTS idx_user_role_assignments_user_id ON user_role_assignments(user_id);
                CREATE INDEX IF NOT EXISTS idx_user_role_assignments_role_id ON user_role_assignments(role_id);
                CREATE INDEX IF NOT EXISTS idx_user_role_assignments_active ON user_role_assignments(is_active, expires_at) WHERE is_active = 1;
                CREATE INDEX IF NOT EXISTS idx_user_role_assignments_expires ON user_role_assignments(expires_at) WHERE expires_at IS NOT NULL;
                
                -- Add triggers to automatically update updated_at
                CREATE TRIGGER update_permissions_updated_at 
                    AFTER UPDATE ON permissions
                    FOR EACH ROW
                    WHEN OLD.updated_at = NEW.updated_at
                BEGIN
                    UPDATE permissions SET updated_at = strftime('%Y-%m-%dT%H:%M:%SZ', 'now') WHERE id = NEW.id;
                END;
                
                CREATE TRIGGER update_roles_updated_at 
                    AFTER UPDATE ON roles
                    FOR EACH ROW
                    WHEN OLD.updated_at = NEW.updated_at
                BEGIN
                    UPDATE roles SET updated_at = strftime('%Y-%m-%dT%H:%M:%SZ', 'now') WHERE id = NEW.id;
                END;
                
                -- Insert default system roles
                INSERT INTO roles (id, name, description, level, is_system, is_active) VALUES
                    ('00000000-0000-0000-0000-000000000001', 'super_admin', 'Super Administrator with all permissions', 0, 1, 1),
                    ('00000000-0000-0000-0000-000000000002', 'admin', 'Administrator with most permissions', 1, 1, 1),
                    ('00000000-0000-0000-0000-000000000003', 'moderator', 'Moderator with limited permissions', 2, 1, 1),
                    ('00000000-0000-0000-0000-000000000004', 'user', 'Regular user with basic permissions', 3, 1, 1);
                
                -- Insert default permissions
                INSERT INTO permissions (id, name, description, resource, action) VALUES
                    ('10000000-0000-0000-0000-000000000001', 'users:read', 'Read user information', 'users', 'read'),
                    ('10000000-0000-0000-0000-000000000002', 'users:write', 'Create and update users', 'users', 'write'),
                    ('10000000-0000-0000-0000-000000000003', 'users:delete', 'Delete users', 'users', 'delete'),
                    ('10000000-0000-0000-0000-000000000004', 'roles:read', 'Read role information', 'roles', 'read'),
                    ('10000000-0000-0000-0000-000000000005', 'roles:write', 'Create and update roles', 'roles', 'write'),
                    ('10000000-0000-0000-0000-000000000006', 'roles:delete', 'Delete roles', 'roles', 'delete'),
                    ('10000000-0000-0000-0000-000000000007', 'permissions:read', 'Read permission information', 'permissions', 'read'),
                    ('10000000-0000-0000-0000-000000000008', 'permissions:write', 'Create and update permissions', 'permissions', 'write'),
                    ('10000000-0000-0000-0000-000000000009', 'permissions:delete', 'Delete permissions', 'permissions', 'delete'),
                    ('10000000-0000-0000-0000-000000000010', 'system:admin', 'System administration', 'system', 'admin');
                
                -- Assign permissions to default roles
                INSERT INTO role_permissions (role_id, permission_id, granted) VALUES
                    -- Super Admin gets all permissions
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000003', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000004', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000005', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000006', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000007', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000008', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000009', 1),
                    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000010', 1),
                    -- Admin gets most permissions except system admin
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', 1),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000002', 1),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003', 1),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000004', 1),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000005', 1),
                    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000007', 1),
                    -- Moderator gets read permissions and limited write
                    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', 1),
                    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000002', 1),
                    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000004', 1),
                    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000007', 1),
                    -- User gets basic read permissions
                    ('00000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', 1);"#.to_string(),
                down_sql: r#"DROP TRIGGER IF EXISTS update_roles_updated_at;
                DROP TRIGGER IF EXISTS update_permissions_updated_at;
                DROP TABLE IF EXISTS user_role_assignments;
                DROP TABLE IF EXISTS role_permissions;
                DROP TABLE IF EXISTS roles;
                DROP TABLE IF EXISTS permissions;"#.to_string(),
                checksum: "sha256:sqlite_rbac_v1".to_string(),
            },
        ]
    }

    /// Get all built-in migrations (defaults to SQLite for compatibility)
    pub fn get_all() -> Vec<Migration> {
        Self::get_sqlite_migrations()
    }
}

/// Simple migration manager implementation
pub struct SimpleMigrationManager {
    migrations: Vec<Migration>,
}

impl SimpleMigrationManager {
    pub fn new() -> Self {
        Self {
            migrations: BuiltinMigrations::get_all(),
        }
    }

    pub fn with_migrations(migrations: Vec<Migration>) -> Self {
        Self { migrations }
    }
}

impl Default for SimpleMigrationManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait::async_trait]
impl MigrationManager for SimpleMigrationManager {
    async fn get_migrations(&self) -> Result<Vec<Migration>> {
        Ok(self.migrations.clone())
    }

    async fn get_applied_migrations(&self) -> Result<Vec<MigrationResult>> {
        // This would query the database for applied migrations
        Ok(vec![])
    }

    async fn migrate(&self) -> Result<Vec<MigrationResult>> {
        // This would apply pending migrations
        Err(AuthError::internal("Migration not implemented yet"))
    }

    async fn rollback_to(&self, _version: u64) -> Result<Vec<MigrationResult>> {
        // This would rollback to the specified version
        Err(AuthError::internal("Rollback not implemented yet"))
    }

    async fn validate(&self) -> Result<()> {
        // This would validate migration integrity
        Ok(())
    }

    async fn current_version(&self) -> Result<Option<u64>> {
        // This would get the current schema version
        Ok(None)
    }
}
