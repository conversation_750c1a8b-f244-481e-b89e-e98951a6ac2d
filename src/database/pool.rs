//! Database connection pooling

use std::{
    sync::Arc,
    time::{Duration, Instant},
};

use tokio::sync::Semaphore;

use crate::error::Result;

/// Connection pool statistics
#[derive(Debug, Clone)]
pub struct PoolStats {
    pub total_connections: u32,
    pub active_connections: u32,
    pub idle_connections: u32,
    pub pending_requests: u32,
    pub total_acquired: u64,
    pub total_released: u64,
    pub average_acquire_time: Duration,
}

/// Connection pool health status
#[derive(Debug, Clone)]
pub struct PoolHealth {
    pub is_healthy: bool,
    pub error_rate: f64,
    pub average_response_time: Duration,
    pub last_error: Option<String>,
    pub last_health_check: Instant,
}

/// Database connection pool trait
pub trait ConnectionPool: Send + Sync {
    /// Get pool statistics
    fn stats(&self) -> PoolStats;

    /// Get pool health information
    fn health(&self) -> PoolHealth;

    /// Test all connections in the pool
    fn test_connections(&self) -> impl std::future::Future<Output = Result<()>> + Send;

    /// Close all connections and shutdown the pool
    fn close(&self) -> impl std::future::Future<Output = Result<()>> + Send;
}

/// Pool configuration
#[derive(Debug, Clone)]
pub struct PoolConfig {
    pub max_size: u32,
    pub min_size: u32,
    pub acquire_timeout: Duration,
    pub idle_timeout: Option<Duration>,
    pub max_lifetime: Option<Duration>,
    pub test_on_acquire: bool,
    pub test_on_release: bool,
}

impl Default for PoolConfig {
    fn default() -> Self {
        Self {
            max_size: 10,
            min_size: 1,
            acquire_timeout: Duration::from_secs(30),
            idle_timeout: Some(Duration::from_secs(600)),
            max_lifetime: Some(Duration::from_secs(1800)),
            test_on_acquire: true,
            test_on_release: false,
        }
    }
}

/// Generic connection pool implementation with performance optimizations
#[derive(Clone)]
pub struct GenericPool<T>
where
    T: Clone + Send + Sync,
{
    config: PoolConfig,
    semaphore: Arc<Semaphore>,
    stats: Arc<tokio::sync::RwLock<PoolStats>>,
    health: Arc<tokio::sync::RwLock<PoolHealth>>,
    // Performance optimization: pre-allocated connection slots
    connection_slots: Arc<tokio::sync::RwLock<Vec<ConnectionSlot>>>,
    // Performance optimization: fast path for health checks
    last_health_check: Arc<std::sync::atomic::AtomicU64>,
    health_check_interval: Duration,
    _phantom: std::marker::PhantomData<T>,
}

/// Connection slot for optimized connection management
#[derive(Debug)]
struct ConnectionSlot {
    _id: u32,
    created_at: Instant,
    _last_used: Instant,
    is_healthy: bool,
    _use_count: u64,
}

/// Advanced pool configuration for performance tuning
#[derive(Debug, Clone)]
pub struct AdvancedPoolConfig {
    pub base: PoolConfig,
    pub connection_warmup: bool,
    pub health_check_interval: Duration,
    pub connection_validation_query: Option<String>,
    pub prepared_statement_cache_size: usize,
    pub connection_reset_on_return: bool,
    pub fair_queuing: bool,
    pub connection_affinity: bool,
}

impl<T: Clone + Send + Sync + 'static> GenericPool<T> {
    pub fn new(config: PoolConfig) -> Self {
        let semaphore = Arc::new(Semaphore::new(config.max_size as usize));

        let stats = Arc::new(tokio::sync::RwLock::new(PoolStats {
            total_connections: config.max_size,
            active_connections: 0,
            idle_connections: config.max_size,
            pending_requests: 0,
            total_acquired: 0,
            total_released: 0,
            average_acquire_time: Duration::from_millis(1),
        }));

        let health = Arc::new(tokio::sync::RwLock::new(PoolHealth {
            is_healthy: true,
            error_rate: 0.0,
            average_response_time: Duration::from_millis(1),
            last_error: None,
            last_health_check: Instant::now(),
        }));

        // Pre-allocate connection slots for performance
        let mut slots = Vec::with_capacity(config.max_size as usize);
        let now = Instant::now();
        for i in 0..config.max_size {
            slots.push(ConnectionSlot {
                _id: i,
                created_at: now,
                _last_used: now,
                is_healthy: true,
                _use_count: 0,
            });
        }
        let connection_slots = Arc::new(tokio::sync::RwLock::new(slots));

        let health_check_interval = Duration::from_secs(30);
        let last_health_check = Arc::new(std::sync::atomic::AtomicU64::new(
            now.elapsed().as_nanos() as u64,
        ));

        Self {
            config,
            semaphore,
            stats,
            health,
            connection_slots,
            last_health_check,
            health_check_interval,
            _phantom: std::marker::PhantomData,
        }
    }

    /// Create pool with advanced configuration for performance tuning
    pub fn with_advanced_config(config: AdvancedPoolConfig) -> Self {
        let mut pool = Self::new(config.base);
        pool.health_check_interval = config.health_check_interval;

        // Apply performance optimizations based on advanced config
        if config.connection_warmup {
            // In a real implementation, we'd warm up connections here
        }

        pool
    }

    /// Fast path for connection acquisition with performance optimizations
    pub async fn acquire_fast(&self) -> Result<ConnectionHandle<T>> {
        let start = Instant::now();

        // Fast path: try to acquire without blocking
        if let Ok(permit) = self.semaphore.clone().try_acquire_owned() {
            let acquire_time = start.elapsed();
            self.update_acquire_stats(acquire_time).await;

            return Ok(ConnectionHandle {
                _permit: permit,
                pool: self.clone(),
                _acquired_at: start,
            });
        }

        // Slow path: wait for available connection with timeout
        let permit = tokio::time::timeout(
            self.config.acquire_timeout,
            self.semaphore.clone().acquire_owned(),
        )
        .await
        .map_err(|_| crate::AuthError::Timeout {
            operation: "connection_acquire".to_string(),
            duration: self.config.acquire_timeout,
        })?
        .map_err(|_| crate::AuthError::internal("Semaphore closed"))?;

        let acquire_time = start.elapsed();
        self.update_acquire_stats(acquire_time).await;

        Ok(ConnectionHandle {
            _permit: permit,
            pool: self.clone(),
            _acquired_at: start,
        })
    }

    /// Update acquisition statistics with performance tracking
    async fn update_acquire_stats(&self, acquire_time: Duration) {
        let mut stats = self.stats.write().await;
        stats.total_acquired += 1;

        // Exponential moving average for acquire time
        let alpha = 0.1; // Smoothing factor
        let new_avg = stats.average_acquire_time.as_nanos() as f64 * (1.0 - alpha)
            + acquire_time.as_nanos() as f64 * alpha;
        stats.average_acquire_time = Duration::from_nanos(new_avg as u64);

        stats.active_connections = stats.active_connections.saturating_add(1);
        stats.idle_connections = stats.idle_connections.saturating_sub(1);
    }

    /// Release connection with performance tracking
    async fn release_connection(&self) {
        let mut stats = self.stats.write().await;
        stats.total_released += 1;
        stats.active_connections = stats.active_connections.saturating_sub(1);
        stats.idle_connections = stats.idle_connections.saturating_add(1);
    }

    /// Optimized health check with caching
    pub async fn health_check_cached(&self) -> Result<()> {
        let now = Instant::now();
        let last_check_nanos = self
            .last_health_check
            .load(std::sync::atomic::Ordering::Relaxed);
        let last_check = Instant::now() - Duration::from_nanos(last_check_nanos);

        // Use cached result if recent enough
        if now.duration_since(last_check) < self.health_check_interval {
            let health = self.health.read().await;
            return if health.is_healthy {
                Ok(())
            } else {
                Err(crate::AuthError::internal("Pool is unhealthy"))
            };
        }

        // Perform actual health check
        let health_result = self.perform_health_check().await;

        // Update cache
        self.last_health_check.store(
            now.elapsed().as_nanos() as u64,
            std::sync::atomic::Ordering::Relaxed,
        );

        health_result
    }

    /// Perform actual health check
    async fn perform_health_check(&self) -> Result<()> {
        let start = Instant::now();

        // Check connection slots health
        let slots = self.connection_slots.read().await;
        let healthy_count = slots.iter().filter(|slot| slot.is_healthy).count();
        let total_count = slots.len();

        let health_ratio = healthy_count as f64 / total_count as f64;
        let response_time = start.elapsed();

        let mut health = self.health.write().await;
        health.is_healthy = health_ratio >= 0.8; // 80% healthy threshold
        health.average_response_time = response_time;
        health.last_health_check = Instant::now();

        if health.is_healthy {
            health.error_rate = (1.0 - health_ratio) * 0.1; // Reduce error rate
            health.last_error = None;
        } else {
            health.error_rate = 1.0 - health_ratio;
            health.last_error = Some("Pool health below threshold".to_string());
        }

        if health.is_healthy {
            Ok(())
        } else {
            Err(crate::AuthError::internal("Pool health check failed"))
        }
    }

    /// Get detailed performance metrics
    pub async fn get_performance_metrics(&self) -> PoolPerformanceMetrics {
        let stats = self.stats.read().await;
        let health = self.health.read().await;
        let slots = self.connection_slots.read().await;

        let connection_ages: Vec<Duration> = slots
            .iter()
            .map(|slot| Instant::now().duration_since(slot.created_at))
            .collect();

        let avg_connection_age = if !connection_ages.is_empty() {
            connection_ages.iter().sum::<Duration>() / connection_ages.len() as u32
        } else {
            Duration::from_secs(0)
        };

        PoolPerformanceMetrics {
            acquire_latency_p50: stats.average_acquire_time,
            acquire_latency_p99: stats.average_acquire_time * 3, // Approximation
            connection_utilization: stats.active_connections as f64
                / stats.total_connections as f64,
            error_rate: health.error_rate,
            throughput_per_second: if stats.average_acquire_time.as_millis() > 0 {
                1000.0 / stats.average_acquire_time.as_millis() as f64
            } else {
                0.0
            },
            average_connection_age: avg_connection_age,
            health_check_latency: health.average_response_time,
        }
    }
}

/// Connection handle with automatic cleanup
pub struct ConnectionHandle<T: Clone + Send + Sync + 'static> {
    _permit: tokio::sync::OwnedSemaphorePermit,
    pool: GenericPool<T>,
    _acquired_at: Instant,
}

impl<T: Clone + Send + Sync + 'static> Drop for ConnectionHandle<T> {
    fn drop(&mut self) {
        let pool = self.pool.clone();
        tokio::spawn(async move {
            pool.release_connection().await;
        });
    }
}

/// Performance metrics for monitoring
#[derive(Debug, Clone)]
pub struct PoolPerformanceMetrics {
    pub acquire_latency_p50: Duration,
    pub acquire_latency_p99: Duration,
    pub connection_utilization: f64,
    pub error_rate: f64,
    pub throughput_per_second: f64,
    pub average_connection_age: Duration,
    pub health_check_latency: Duration,
}

impl<T> ConnectionPool for GenericPool<T>
where
    T: Send + Sync + Clone,
{
    fn stats(&self) -> PoolStats {
        // This would be implemented with actual pool statistics
        PoolStats {
            total_connections: self.config.max_size,
            active_connections: 0,
            idle_connections: self.config.max_size,
            pending_requests: 0,
            total_acquired: 0,
            total_released: 0,
            average_acquire_time: Duration::from_millis(1),
        }
    }

    fn health(&self) -> PoolHealth {
        PoolHealth {
            is_healthy: true,
            error_rate: 0.0,
            average_response_time: Duration::from_millis(1),
            last_error: None,
            last_health_check: Instant::now(),
        }
    }

    async fn test_connections(&self) -> Result<()> {
        // Implementation would test all connections
        Ok(())
    }

    async fn close(&self) -> Result<()> {
        // Implementation would close all connections
        Ok(())
    }
}
