use anyhow::Result;
use once_cell::sync::OnceCell;
use r2d2_sqlite::SqliteConnectionManager;
use std::collections::HashMap;

use crate::{
    adapters::sqlite::SQLiteAdapter, config::SecurityConfig, database::Database,
    migrations::Migration,
};

/// Global instance of the SQLite Database.
static GLOBAL_SQLITE_DB: OnceCell<Database<SQLiteAdapter>> = OnceCell::new();

/// Configures the global SQLite database instance.
///
/// This function should be called early in your application's lifecycle to set up the
/// global SQLite database instance. It can only be called once - subsequent calls will
/// return an error.
///
/// # Arguments
///
/// * `db_url` - The database URL to connect to. Examples:
///   - `"file:memory?mode=memory"` for an in-memory database
///   - `"file:data.db"` for a file-based database
///   - `"file:test.db?mode=memory&cache=shared"` for a shared in-memory database (useful for tests)
/// * `config` - Security configuration including pool settings and migration table name
/// * `migrations_map` - Map of migration files where key is filename and value is SQL content
///
/// # Returns
///
/// Returns `Ok(())` if configuration succeeds, or an error if:
/// - The global instance is already configured
/// - Database connection fails
/// - Migrations fail to apply
///
/// # Example
///
/// ```rust,no_run
/// use std::collections::HashMap;
/// use db::config::SecurityConfig;
/// use db::global::configure_global_sqlite_db;
///
/// fn main() -> anyhow::Result<()> {
///     let config = SecurityConfig {
///         migration_table: "migrations".to_string(),
///         max_pool_size: 10,
///         pool_min_idle: 1,
///         ..Default::default()
///     };
///
///     let mut migrations = HashMap::new();
///     migrations.insert(
///         "001_init.sql".to_string(),
///         "-- migrate:up\nCREATE TABLE test (id INTEGER PRIMARY KEY);".to_string(),
///     );
///
///     configure_global_sqlite_db("file:test.db", config, migrations)?;
///     Ok(())
/// }
/// ```
pub fn configure_global_sqlite_db(
    db_url: &str,
    config: SecurityConfig,
    migrations_map: HashMap<String, String>,
) -> anyhow::Result<()> {
    // Create the database instance.
    let db = Database::<SQLiteAdapter>::new(db_url, &config)?;

    // Optimize SQLite settings for production use
    let conn = db.get_connection()?;
    conn.execute_batch(
        "PRAGMA auto_vacuum = FULL;
         PRAGMA temp_store = MEMORY;
         PRAGMA mmap_size = 268435456;",
    )?;

    // Load and run migrations.
    let migrations = Migration::load_from_map(migrations_map)?;
    db.run_migrations(migrations)?;

    // Set the global instance.
    GLOBAL_SQLITE_DB
        .set(db)
        .map_err(|_| anyhow::anyhow!("Global SQLite database is already configured"))?;
    Ok(())
}

/// Returns a connection from the global SQLite connection pool.
///
/// This function provides a connection that will automatically be returned to the pool
/// when it's dropped. If the global instance hasn't been configured, returns an error.
///
/// # Returns
///
/// Returns a pooled SQLite connection wrapped in `Result`, or an error if the global
/// instance hasn't been configured.
///
/// # Example
///
/// ```rust,no_run
/// use rusqlite::params;
/// use db::global::get_global_sqlite_connection;
///
/// fn main() -> anyhow::Result<()> {
///     let conn = get_global_sqlite_connection()?;
///     conn.execute(
///         "INSERT INTO users (name) VALUES (?)",
///         params!["Alice"],
///     )?;
///     Ok(())
/// }
/// ```
pub fn get_global_sqlite_connection() -> Result<r2d2::PooledConnection<SqliteConnectionManager>> {
    GLOBAL_SQLITE_DB
        .get()
        .ok_or(anyhow::anyhow!(
            "Global SQLite database has not been configured"
        ))?
        .get_connection()
}

/// Returns a reference to the global SQLite database instance.
///
/// This function provides direct access to the database instance, which can be useful
/// for operations that need to work with the pool itself rather than individual connections.
///
/// # Returns
///
/// Returns a reference to the Database instance wrapped in `Result`, or an error if
/// the global instance hasn't been configured.
///
/// # Example
///
/// ```rust,no_run
/// use db::global::get_global_sqlite_db;
///
/// fn main() -> anyhow::Result<()> {
///     let db = get_global_sqlite_db()?;
///     // Use the database instance directly
///     Ok(())
/// }
/// ```
pub fn get_global_sqlite_db() -> Result<&'static Database<SQLiteAdapter>> {
    GLOBAL_SQLITE_DB.get().ok_or(anyhow::anyhow!(
        "Global SQLite database has not been configured"
    ))
}

/// Returns true if the SQLite database has been initialized.
///
/// This function checks if the global SQLite database instance has been set.
///
/// # Returns
///
/// Returns `true` if the SQLite database has been initialized, otherwise `false`.
pub fn is_sqlite_database_initialized() -> bool {
    GLOBAL_SQLITE_DB.get().is_some()
}

#[cfg(test)]
mod tests {
    use rusqlite::params;
    use tempfile::NamedTempFile;

    use super::*;

    #[test]
    fn test_configure_and_get_global_sqlite_connection() -> Result<()> {
        let temp_db = NamedTempFile::new().expect("Failed to create tempfile");
        let db_url = format!("file:{}", temp_db.path().to_str().unwrap());

        let config = SecurityConfig {
            migration_table: "global_migrations".to_string(),
            max_pool_size: 10,
            pool_min_idle: 2,
            ..SecurityConfig::default()
        };

        // Create a migration map that creates a "todos" table
        let mut migrations_map = HashMap::new();
        migrations_map.insert(
            "001_create_todos.sql".to_string(),
            r#"-- migrate:up
CREATE TABLE IF NOT EXISTS `todos` (
    id INTEGER PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT
);
-- migrate:down
DROP TABLE IF EXISTS todos;"#
                .to_string(),
        );

        // Configure the global database
        configure_global_sqlite_db(&db_url, config.clone(), migrations_map)?;

        // Verify the global instance is configured
        assert!(
            is_sqlite_database_initialized(),
            "Global SQLite DB should be initialized"
        );

        // Retrieve a connection from the global instance
        let conn = get_global_sqlite_connection()?;

        // First verify that the migrations table exists
        let migrations_table_exists: bool = conn
            .query_row(
                "SELECT 1 FROM sqlite_master WHERE type='table' AND name=?",
                params![config.migration_table],
                |_| Ok(true),
            )
            .unwrap_or(false);
        assert!(migrations_table_exists, "Migrations table should exist");

        // Verify migration table exists and has the correct entry
        let (version, desc): (i64, String) = conn.query_row(
            &format!(
                "SELECT version, description FROM {} ORDER BY version DESC LIMIT 1",
                config.migration_table
            ),
            [],
            |row| Ok((row.get(0)?, row.get(1)?)),
        )?;

        assert_eq!(version, 1);
        assert!(
            desc.contains("create todos"),
            "Description should contain migration name"
        );

        // Verify todos table exists
        let todos_table_exists: bool = conn
            .query_row(
                "SELECT 1 FROM sqlite_master WHERE type='table' AND name='todos'",
                [],
                |_| Ok(true),
            )
            .unwrap_or(false);
        assert!(todos_table_exists, "Todos table should exist");

        // Insert a record into the "todos" table
        conn.execute(
            "INSERT INTO todos (title, description) VALUES (?, ?)",
            params!["Test Task", "Testing global db integration"],
        )?;

        // Query the inserted row
        let (title, description): (String, Option<String>) = conn.query_row(
            "SELECT title, description FROM todos WHERE id = last_insert_rowid()",
            [],
            |row| Ok((row.get(0)?, row.get(1)?)),
        )?;

        assert_eq!(title, "Test Task");
        assert_eq!(description.unwrap(), "Testing global db integration");

        Ok(())
    }
}
