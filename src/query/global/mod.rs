//! Global database module providing singleton database instances for SQLite, MySQL, and PostgreSQL.
//!
//! This module provides global database instances that can be configured once and accessed
//! throughout the application. It supports three database types:
//! - SQLite: For embedded/local database needs
//! - MySQL: For client-server database deployments
//! - PostgreSQL: For advanced relational database features
//!
//! # Example
//! ```rust,no_run
//! use db::global::{configure_global_sqlite_db, get_global_sqlite_connection};
//! use std::collections::HashMap;
//! use db::config::SecurityConfig;
//!
//! fn main() -> anyhow::Result<()> {
//!     let mut migrations = HashMap::new();
//!     // Configure the global SQLite instance
//!     configure_global_sqlite_db(
//!         "file:memory?mode=memory",
//!         SecurityConfig::default(),
//!         migrations
//!     )?;
//!
//!     // Get a connection anywhere in your code
//!     let conn = get_global_sqlite_connection()?;
//!     Ok(())
//! }
//! ```

pub mod global_mysql_db;
pub mod global_postgres_db;
pub mod global_sqlite_db;

pub use global_mysql_db::*;
pub use global_postgres_db::*;
pub use global_sqlite_db::*;
