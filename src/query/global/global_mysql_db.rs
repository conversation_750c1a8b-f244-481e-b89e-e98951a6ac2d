use crate::adapters::mysql::MySQLAdapter;
use anyhow::Result;
#[allow(unused_imports)]
use mysql::prelude::Queryable as _;
use once_cell::sync::OnceCell;
use std::collections::HashMap;

use crate::{config::SecurityConfig, database::Database, migrations::Migration};

/// Global instance of the MySQL Database.
static GLOBAL_MYSQL_DB: OnceCell<Database<MySQLAdapter>> = OnceCell::new();

/// Configures the global MySQL database instance.
///
/// This function should be called early in your application's lifecycle to set up the
/// global MySQL database instance. It can only be called once - subsequent calls will
/// return an error.
///
/// # Arguments
///
/// * `db_url` - The MySQL connection URL in the format:
///   `mysql://[user[:password]@]host[:port][/database][?param1=value1&...]`
///   Examples:
///   - `"mysql://user:pass@localhost/dbname"`
///   - `"mysql://localhost/dbname"`
///   - `"mysql://user:pass@localhost:3306/dbname?ssl_mode=disabled"`
/// * `config` - Security configuration including pool settings and migration table name
/// * `migrations_map` - Map of migration files where key is filename and value is SQL content
///
/// # Returns
///
/// Returns `Ok(())` if configuration succeeds, or an error if:
/// - The global instance is already configured
/// - Database connection fails
/// - Migrations fail to apply
///
/// # Example
///
/// ```rust,no_run
/// use std::collections::HashMap;
/// use db::config::SecurityConfig;
/// use db::global::global_mysql_db::configure_global_mysql_db;
///
/// fn main() -> anyhow::Result<()> {
///     let config = SecurityConfig {
///         migration_table: "migrations".to_string(),
///         max_pool_size: 10,
///         pool_min_idle: 1,
///         ..Default::default()
///     };
///
///     let mut migrations = HashMap::new();
///     migrations.insert(
///         "001_init.sql".to_string(),
///         "-- migrate:up\nCREATE TABLE test (id INT AUTO_INCREMENT PRIMARY KEY);".to_string(),
///     );
///
///     configure_global_mysql_db(
///         "mysql://user:pass@localhost/dbname",
///         config,
///         migrations
///     )?;
///     Ok(())
/// }
/// ```
pub fn configure_global_mysql_db(
    db_url: &str,
    config: SecurityConfig,
    migrations_map: HashMap<String, String>,
) -> Result<()> {
    let db = Database::<MySQLAdapter>::new(db_url, &config)?;
    let migrations = Migration::load_from_map(migrations_map)?;
    db.run_migrations(migrations)?;
    GLOBAL_MYSQL_DB
        .set(db)
        .map_err(|_| anyhow::anyhow!("Global MySQL database is already configured"))?;
    Ok(())
}

/// Returns a connection from the global MySQL connection pool.
///
/// This function provides a connection that will automatically be returned to the pool
/// when it's dropped. If the global instance hasn't been configured, returns an error.
///
/// # Returns
///
/// Returns a pooled MySQL connection wrapped in `Result`, or an error if the global
/// instance hasn't been configured.
///
/// # Example
///
/// ```rust,no_run
/// use db::global::global_mysql_db::get_global_mysql_connection;
/// use mysql::prelude::Queryable;
///
/// fn main() -> anyhow::Result<()> {
///     let mut conn = get_global_mysql_connection()?;
///     conn.exec_drop(
///         "INSERT INTO users (name) VALUES (?)",
///         ("Alice",),
///     )?;
///     Ok(())
/// }
/// ```
pub fn get_global_mysql_connection() -> Result<mysql::PooledConn> {
    GLOBAL_MYSQL_DB
        .get()
        .ok_or(anyhow::anyhow!(
            "Global MySQL database has not been configured"
        ))?
        .get_connection()
        .map_err(|e| anyhow::anyhow!(e))
}

/// Returns a reference to the global MySQL database instance.
///
/// This function provides direct access to the database instance, which can be useful
/// for operations that need to work with the pool itself rather than individual connections.
///
/// # Returns
///
/// Returns a reference to the Database instance wrapped in `Result`, or an error if
/// the global instance hasn't been configured.
///
/// # Example
///
/// ```rust,no_run
/// use db::global::global_mysql_db::get_global_mysql_db;
///
/// fn main() -> anyhow::Result<()> {
///     let db = get_global_mysql_db()?;
///     // Use the database instance directly
///     Ok(())
/// }
/// ```
pub fn get_global_mysql_db() -> Result<&'static Database<MySQLAdapter>> {
    GLOBAL_MYSQL_DB.get().ok_or(anyhow::anyhow!(
        "Global MySQL database has not been configured"
    ))
}

/// Returns true if the MySQL database has been initialized.
///
/// This function checks if the global MySQL database instance has been set.
///
/// # Returns
///
/// Returns `true` if the MySQL database has been initialized, otherwise `false`.
pub fn is_mysql_database_initialized() -> bool {
    GLOBAL_MYSQL_DB.get().is_some()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::SecurityConfig;
    use std::collections::HashMap;

    #[test]
    fn test_configure_and_get_global_mysql_connection() {
        // Use an environment variable or fallback test value for the connection URL.
        let db_url = std::env::var("TEST_MYSQL_URL")
            .unwrap_or_else(|_| "mysql://root:password@localhost/test_db".to_string());
        // Prepare a custom security configuration.
        let config = SecurityConfig {
            migration_table: "global_mysql_migrations".to_string(),
            max_pool_size: 5,
            pool_min_idle: 1,
            ..SecurityConfig::default()
        };
        // Create a migration map that creates a simple test table.
        let mut migrations_map = HashMap::new();
        migrations_map.insert(
            "001_create_test_table.sql".to_string(),
            r#"-- migrate:up
CREATE TABLE IF NOT EXISTS test_table (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL
);
-- migrate:down
DROP TABLE IF EXISTS test_table;"#
                .to_string(),
        );

        // Attempt to configure the global MySQL DB.
        if let Err(e) = configure_global_mysql_db(&db_url, config, migrations_map) {
            println!("Skipping global MySQL test: {}", e);
            return;
        }

        let mut conn = get_global_mysql_connection().expect("Global MySQL connection obtained");
        // Execute a simple query and get a value.
        let value: Option<i64> = conn.query_first("SELECT 1").expect("Query should succeed");
        assert_eq!(value, Some(1));
    }
}
