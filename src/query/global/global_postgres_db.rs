use anyhow::Result;
use once_cell::sync::OnceCell;
use r2d2::PooledConnection;
use r2d2_postgres::{postgres::NoTls, PostgresConnectionManager};
use std::collections::HashMap;

use crate::{
    adapters::postgres::PostgresAdapter, config::SecurityConfig, database::Database,
    migrations::Migration,
};

/// Global instance of the PostgreSQL Database.
static GLOBAL_POSTGRES_DB: OnceCell<Database<PostgresAdapter>> = OnceCell::new();

/// Configures the global PostgreSQL database instance.
///
/// This function should be called early in your application's lifecycle to set up the
/// global PostgreSQL database instance. It can only be called once - subsequent calls will
/// return an error.
///
/// # Arguments
///
/// * `db_url` - The PostgreSQL connection URL in the format:
///   `postgres://[user[:password]@]host[:port][/database][?param1=value1&...]`
///   Examples:
///   - `"postgres://user:pass@localhost/dbname"`
///   - `"postgres://localhost/dbname"`
///   - `"postgres://user:pass@localhost:5432/dbname?sslmode=require"`
/// * `config` - Security configuration including pool settings and migration table name
/// * `migrations_map` - Map of migration files where key is filename and value is SQL content
///
/// # Returns
///
/// Returns `Ok(())` if configuration succeeds, or an error if:
/// - The global instance is already configured
/// - Database connection fails
/// - Migrations fail to apply
///
/// # Example
///
/// ```rust,no_run
/// use std::collections::HashMap;
/// use db::config::SecurityConfig;
/// use db::global::global_postgres_db::configure_global_postgres_db;
///
/// fn main() -> anyhow::Result<()> {
///     let config = SecurityConfig {
///         migration_table: "migrations".to_string(),
///         max_pool_size: 10,
///         pool_min_idle: 1,
///         ..Default::default()
///     };
///
///     let mut migrations = HashMap::new();
///     migrations.insert(
///         "001_init.sql".to_string(),
///         "-- migrate:up\nCREATE TABLE test (id SERIAL PRIMARY KEY);".to_string(),
///     );
///
///     configure_global_postgres_db(
///         "postgres://user:pass@localhost/dbname",
///         config,
///         migrations
///     )?;
///     Ok(())
/// }
/// ```
pub fn configure_global_postgres_db(
    db_url: &str,
    config: SecurityConfig,
    migrations_map: HashMap<String, String>,
) -> Result<()> {
    let db = Database::<PostgresAdapter>::new(db_url, &config)?;
    let migrations = Migration::load_from_map(migrations_map)?;
    db.run_migrations(migrations)?;
    GLOBAL_POSTGRES_DB
        .set(db)
        .map_err(|_| anyhow::anyhow!("Global Postgres database is already configured"))?;
    Ok(())
}

/// Returns a connection from the global PostgreSQL connection pool.
///
/// This function provides a connection that will automatically be returned to the pool
/// when it's dropped. If the global instance hasn't been configured, returns an error.
///
/// # Returns
///
/// Returns a pooled PostgreSQL connection wrapped in `Result`, or an error if the global
/// instance hasn't been configured.
///
/// # Example
///
/// ```rust,no_run
/// use db::global::global_postgres_db::get_global_postgres_connection;
///
/// fn main() -> anyhow::Result<()> {
///     let mut conn = get_global_postgres_connection()?;
///     conn.execute(
///         "INSERT INTO users (name) VALUES ($1)",
///         &[&"Alice"],
///     )?;
///     Ok(())
/// }
/// ```
pub fn get_global_postgres_connection() -> Result<PooledConnection<PostgresConnectionManager<NoTls>>>
{
    GLOBAL_POSTGRES_DB
        .get()
        .ok_or(anyhow::anyhow!(
            "Global Postgres database has not been configured"
        ))?
        .get_connection()
        .map_err(Into::into)
}

/// Returns a reference to the global PostgreSQL database instance.
///
/// This function provides direct access to the database instance, which can be useful
/// for operations that need to work with the pool itself rather than individual connections.
///
/// # Returns
///
/// Returns a reference to the Database instance wrapped in `Result`, or an error if
/// the global instance hasn't been configured.
///
/// # Example
///
/// ```rust,no_run
/// use db::global::global_postgres_db::get_global_postgres_db;
///
/// fn main() -> anyhow::Result<()> {
///     let db = get_global_postgres_db()?;
///     // Use the database instance directly
///     Ok(())
/// }
/// ```
pub fn get_global_postgres_db() -> Result<&'static Database<PostgresAdapter>> {
    GLOBAL_POSTGRES_DB.get().ok_or(anyhow::anyhow!(
        "Global Postgres database has not been configured"
    ))
}

/// Returns true if the PostgreSQL database has been initialized.
///
/// This function checks if the global PostgreSQL database instance has been set.
///
/// # Returns
///
/// Returns `true` if the PostgreSQL database has been initialized, otherwise `false`.
pub fn is_postgres_database_initialized() -> bool {
    GLOBAL_POSTGRES_DB.get().is_some()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::SecurityConfig;
    use std::collections::HashMap;

    #[test]
    fn test_configure_and_get_global_postgres_connection() {
        // Use an environment variable or fallback test value for the connection URL.
        let db_url = std::env::var("TEST_POSTGRES_URL")
            .unwrap_or_else(|_| "postgres://postgres:postgres@localhost/test_db".to_string());
        // Prepare a custom security configuration.
        let config = SecurityConfig {
            migration_table: "global_postgres_migrations".to_string(),
            max_pool_size: 5,
            pool_min_idle: 1,
            ..SecurityConfig::default()
        };
        // Create a migration map that creates a simple test table.
        let mut migrations_map = HashMap::new();
        migrations_map.insert(
            "001_create_test_table.sql".to_string(),
            r#"-- migrate:up
CREATE TABLE IF NOT EXISTS test_table (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL
);
-- migrate:down
DROP TABLE IF EXISTS test_table;"#
                .to_string(),
        );

        // Attempt to configure the global Postgres DB.
        // If configuration fails (e.g., connection error), skip the test.
        if let Err(e) = configure_global_postgres_db(&db_url, config, migrations_map) {
            println!("Skipping global Postgres test: {}", e);
            return;
        }

        let mut conn =
            get_global_postgres_connection().expect("Global Postgres connection obtained");
        // Execute a simple query.
        let row = conn
            .query_opt("SELECT 1", &[])
            .expect("Query should succeed");
        let value: Option<i32> = row.map(|r| r.get(0));
        assert_eq!(value, Some(1));
    }
}
