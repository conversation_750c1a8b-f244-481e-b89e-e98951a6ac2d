use log::info;
use rand::Rng<PERSON>ore;
use std::time::Duration;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct SecurityConfig {
    pub max_pool_size: usize,
    pub connection_timeout: Duration,
    pub pool_min_idle: usize,
    pub pool_max_lifetime: Duration,
    pub max_query_depth: usize,
    pub max_page_size: usize,
    pub max_string_length: usize,
    pub cursor_expiry: Duration,
    pub max_array_size: usize,
    pub hmac_secret: Vec<u8>,
    pub migration_table: String,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        info!("Creating default SecurityConfig");

        let hmac_secret = match std::env::var("CURSOR_SECRET") {
            Ok(secret) => {
                if secret.len() >= 32 {
                    secret.into_bytes()
                } else {
                    info!("CURSOR_SECRET is too short ({} bytes), needs at least 32 bytes - generating random key", secret.len());
                    generate_random_key()
                }
            }
            Err(e) => {
                info!(
                    "CURSOR_SECRET not available ({:?}), generating random key",
                    e
                );
                generate_random_key()
            }
        };

        Self {
            max_pool_size: 5,
            connection_timeout: Duration::from_secs(30),
            pool_min_idle: 1,
            pool_max_lifetime: Duration::from_secs(1800),
            max_query_depth: 5,
            max_page_size: 100,
            max_string_length: 1000,
            cursor_expiry: Duration::from_secs(3600),
            max_array_size: 50,
            hmac_secret,
            migration_table: "__migrations".to_string(),
        }
    }
}

fn generate_random_key() -> Vec<u8> {
    let mut key = [0u8; 32];
    rand::rng().fill_bytes(&mut key);
    key.to_vec()
}

impl SecurityConfig {
    pub fn validate_migration_table_name(name: &str) -> anyhow::Result<()> {
        if !name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            anyhow::bail!("Invalid migration table name. Only alphanumeric and underscore characters are allowed.");
        }
        Ok(())
    }

    pub fn validate(&self) -> anyhow::Result<()> {
        // Pool configuration validation
        if self.max_pool_size < 1 || self.max_pool_size > 100 {
            anyhow::bail!("max_pool_size must be between 1 and 100");
        }
        if self.pool_min_idle > self.max_pool_size {
            anyhow::bail!("pool_min_idle cannot exceed max_pool_size");
        }

        // Timeout validation
        if self.connection_timeout < Duration::from_millis(50)
            || self.connection_timeout > Duration::from_secs(3600)
        {
            anyhow::bail!("connection_timeout must be between 50ms and 1h");
        }
        if self.pool_max_lifetime < Duration::from_secs(1)
            || self.pool_max_lifetime > Duration::from_secs(86400)
        {
            anyhow::bail!("pool_max_lifetime must be between 1s and 24h");
        }
        if self.cursor_expiry < Duration::from_secs(60)
            || self.cursor_expiry > Duration::from_secs(604800)
        {
            anyhow::bail!("cursor_expiry must be between 1m and 7 days");
        }

        // Query limits validation
        if self.max_query_depth < 1 || self.max_query_depth > 20 {
            anyhow::bail!("max_query_depth must be between 1 and 20");
        }
        if self.max_page_size < 1 || self.max_page_size > 1000 {
            anyhow::bail!("max_page_size must be between 1 and 1000");
        }
        if self.max_string_length < 1 || self.max_string_length > 10_000 {
            anyhow::bail!("max_string_length must be between 1 and 10,000");
        }
        if self.max_array_size < 1 || self.max_array_size > 1000 {
            anyhow::bail!("max_array_size must be between 1 and 1000");
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::SecurityConfig;
    use std::time::Duration;

    #[test]
    fn test_default_security_config() {
        let config = SecurityConfig::default();
        // Verify the defaults (including our new fields)
        assert_eq!(config.max_pool_size, 5);
        assert_eq!(config.connection_timeout, Duration::from_secs(30));
        assert_eq!(config.pool_min_idle, 1);
        assert_eq!(config.pool_max_lifetime, Duration::from_secs(1800));
        // You may also assert other defaults if needed
        assert_eq!(config.max_query_depth, 5);
        assert_eq!(config.max_page_size, 100);
        assert_eq!(config.max_string_length, 1000);
        assert_eq!(config.max_array_size, 50);
        // migration_table default should be "__migrations"
        assert_eq!(config.migration_table, "__migrations");
    }

    #[test]
    fn test_custom_security_config() {
        // Create a custom SecurityConfig with different values.
        let config = SecurityConfig {
            max_pool_size: 10,
            connection_timeout: Duration::from_secs(45),
            pool_min_idle: 2,
            pool_max_lifetime: Duration::from_secs(3600),
            max_query_depth: 6,
            max_page_size: 200,
            max_string_length: 1500,
            cursor_expiry: Duration::from_secs(7200),
            max_array_size: 100,
            hmac_secret: b"this-is-a-test-secret-with-at-least-32-chars!".to_vec(),
            migration_table: "custom_migrations".to_string(),
        };

        // Verify the custom values.
        assert_eq!(config.max_pool_size, 10);
        assert_eq!(config.connection_timeout, Duration::from_secs(45));
        assert_eq!(config.pool_min_idle, 2);
        assert_eq!(config.pool_max_lifetime, Duration::from_secs(3600));
        assert_eq!(config.max_query_depth, 6);
        assert_eq!(config.max_page_size, 200);
        assert_eq!(config.max_string_length, 1500);
        assert_eq!(config.cursor_expiry, Duration::from_secs(7200));
        assert_eq!(config.max_array_size, 100);
        assert_eq!(config.migration_table, "custom_migrations".to_string());
    }

    #[test]
    fn test_config_validation() {
        let valid_config = SecurityConfig::default();
        assert!(
            valid_config.validate().is_ok(),
            "Default config should be valid"
        );

        // Test pool size validation
        let mut invalid = SecurityConfig {
            max_pool_size: 0,
            ..valid_config
        };
        assert_eq!(
            invalid.validate().unwrap_err().to_string(),
            "max_pool_size must be between 1 and 100"
        );

        invalid.max_pool_size = 101;
        assert_eq!(
            invalid.validate().unwrap_err().to_string(),
            "max_pool_size must be between 1 and 100"
        );

        // Test connection timeout
        let invalid = SecurityConfig {
            connection_timeout: Duration::from_secs(0),
            ..SecurityConfig::default()
        };
        assert_eq!(
            invalid.validate().unwrap_err().to_string(),
            "connection_timeout must be between 50ms and 1h"
        );

        // Test query depth
        let invalid = SecurityConfig {
            max_query_depth: 0,
            ..SecurityConfig::default()
        };
        assert_eq!(
            invalid.validate().unwrap_err().to_string(),
            "max_query_depth must be between 1 and 20"
        );

        // Test all other fields similarly...
    }

    #[test]
    fn test_config_validation_edge_cases() {
        let config = SecurityConfig {
            max_pool_size: 100,
            pool_min_idle: 101,
            ..SecurityConfig::default()
        };
        assert_eq!(
            config.validate().unwrap_err().to_string(),
            "pool_min_idle cannot exceed max_pool_size"
        );
    }
}
