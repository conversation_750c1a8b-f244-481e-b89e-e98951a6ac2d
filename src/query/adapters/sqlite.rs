use anyhow::Result;
use r2d2::Pool;
use r2d2_sqlite::SqliteConnectionManager;
use regex::Regex;
use rusqlite::{params, OpenFlags};

use crate::{config::SecurityConfig, migrations::Migration};

/// SQLite database adapter implementation
///
/// This adapter provides SQLite-specific functionality while implementing the common DatabaseAdapter trait.
/// It handles connection pooling, migrations, and SQLite-specific features like file-based and in-memory databases.
///
/// # Examples
///
/// ```no_run
/// # use db::{config::SecurityConfig, database::Database, adapters::{DatabaseAdapter, sqlite::SQLiteAdapter}, Migration};
/// # fn main() -> anyhow::Result<()> {
/// let config = SecurityConfig::default();
///
/// // Connect to a file-based database
/// let adapter = SQLiteAdapter::connect("file:test.db", &config)?;
/// let conn = adapter.get_connection()?;
///
/// // Or connect to an in-memory database
/// let adapter = SQLiteAdapter::connect("file::memory:", &config)?;
/// let conn = adapter.get_connection()?;
/// # Ok(())
/// # }
/// ```
#[derive(Debug, Clone)]
pub struct SQLiteAdapter {
    pool: Pool<SqliteConnectionManager>,
    migration_table: String,
}

impl crate::adapters::database_adapter::DatabaseAdapter for SQLiteAdapter {
    type Connection = r2d2::PooledConnection<SqliteConnectionManager>;

    /// Creates a new SQLite connection pool
    ///
    /// # Arguments
    /// * `database_url` - SQLite connection URL (file:path or file::memory:)
    /// * `config` - Security and pool configuration
    ///
    /// # Examples
    ///
    /// ```no_run
    /// use db::{config::SecurityConfig, adapters::{DatabaseAdapter, sqlite::SQLiteAdapter}};
    /// # fn main() -> anyhow::Result<()> {
    /// let config = SecurityConfig::default();
    ///
    /// // File-based database
    /// let adapter = SQLiteAdapter::connect("file:test.db", &config)?;
    ///
    /// // In-memory database
    /// let adapter = SQLiteAdapter::connect("file::memory:", &config)?;
    /// # Ok(())
    /// # }
    /// ```
    fn connect(database_url: &str, config: &SecurityConfig) -> Result<Self> {
        Self::validate_table_name(&config.migration_table)?;

        let manager = SqliteConnectionManager::file(database_url)
            .with_flags(OpenFlags::SQLITE_OPEN_READ_WRITE | OpenFlags::SQLITE_OPEN_CREATE | OpenFlags::SQLITE_OPEN_URI)
            .with_init(|conn| {
                conn.execute_batch(
                    "PRAGMA foreign_keys = ON;
                     PRAGMA journal_mode=WAL;
                     PRAGMA busy_timeout=60000;
                     PRAGMA synchronous=NORMAL;
                     PRAGMA cache_size=10000;
                     PRAGMA temp_store=MEMORY;
                     PRAGMA mmap_size=30000000000;
                     PRAGMA page_size=4096;
                     PRAGMA locking_mode=NORMAL;",
                )
            });

        let pool = Pool::builder()
            .max_size(config.max_pool_size.try_into().unwrap_or(5))
            .min_idle(Some(config.pool_min_idle.try_into().unwrap_or(1)))
            .max_lifetime(Some(config.pool_max_lifetime))
            .connection_timeout(config.connection_timeout)
            .build(manager)?;

        Ok(Self {
            pool,
            migration_table: config.migration_table.clone(),
        })
    }

    /// Gets a connection from the pool
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{config::SecurityConfig, adapters::{DatabaseAdapter, sqlite::SQLiteAdapter}};
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let adapter = SQLiteAdapter::connect("file::memory:", &config)?;
    /// let conn = adapter.get_connection()?;
    /// # Ok(())
    /// # }
    /// ```
    fn get_connection(&self) -> Result<Self::Connection> {
        self.pool.get().map_err(Into::into)
    }

    /// Runs database migrations in a transaction
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{config::SecurityConfig, adapters::{DatabaseAdapter, sqlite::SQLiteAdapter}, Migration};
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let adapter = SQLiteAdapter::connect("file::memory:", &config)?;
    /// let migrations = vec![
    ///     Migration {
    ///         version: 1,
    ///         description: "Create users table".to_string(),
    ///         up: "CREATE TABLE users (id INTEGER PRIMARY KEY)".to_string(),
    ///         down: "DROP TABLE users".to_string(),
    ///     }
    /// ];
    /// adapter.run_migrations(migrations)?;
    /// # Ok(())
    /// # }
    /// ```
    fn run_migrations(&self, migrations: Vec<Migration>) -> Result<()> {
        use log::debug;

        let mut conn = self.pool.get()?;
        let tx = conn.transaction()?;

        let escaped_table = self.migration_table.replace("\"", "\"\"");

        tx.execute_batch(&format!(
            r#"CREATE TABLE IF NOT EXISTS "{}" (
                version INTEGER PRIMARY KEY,
                description TEXT NOT NULL,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"#,
            escaped_table
        ))?;

        let mut sorted_migrations = migrations;
        sorted_migrations.sort_by_key(|m| m.version);

        for migration in sorted_migrations {
            let exists: bool = tx.query_row(
                &format!("SELECT 1 FROM \"{}\" WHERE version = ?", escaped_table),
                params![migration.version],
                |_| Ok(true),
            ).unwrap_or(false);

            if exists {
                debug!("Skipping already applied migration: {}", migration.version);
                continue;
            }

            for statement in migration.up.split(';').filter(|s| !s.trim().is_empty()) {
                tx.execute_batch(statement.trim())?;
            }

            tx.execute(
                &format!("INSERT INTO \"{}\" (version, description) VALUES (?, ?)", escaped_table),
                params![migration.version, migration.description],
            )?;
        }

        tx.commit()?;
        Ok(())
    }

    fn release_connection(&self, conn: Self::Connection) {
        // Connections are automatically returned to the pool when dropped
        drop(conn);
    }
}

impl SQLiteAdapter {
    /// Validates table name format to prevent SQL injection
    ///
    /// # Arguments
    /// * `table_name` - Name of the table to validate
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::adapters::sqlite::SQLiteAdapter;
    /// # fn main() -> anyhow::Result<()> {
    /// // Valid table name
    /// SQLiteAdapter::validate_table_name("users")?;
    ///
    /// // Invalid table name
    /// assert!(SQLiteAdapter::validate_table_name("users; DROP TABLE users").is_err());
    /// # Ok(())
    /// # }
    /// ```
    pub fn validate_table_name(table_name: &str) -> Result<()> {
        let valid_name = Regex::new(r"^[a-zA-Z_][a-zA-Z0-9_]{0,63}$")?;
        if !valid_name.is_match(table_name) {
            return Err(anyhow::anyhow!(
                "Invalid migration table name: '{}'. Must match ^[a-zA-Z_][a-zA-Z0-9_]{{0,63}}$",
                table_name
            ));
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::adapters::database_adapter::DatabaseAdapter;
    use crate::config::SecurityConfig;
    use crate::database::Database;
    use crate::migrations::Migration;
    use anyhow::Result;
    use log::info;
    use rusqlite::params;
    use std::path::Path;
    use std::sync::Arc;
    use std::time::Duration;
    use tempfile::NamedTempFile;

    mod test_logger {
        use std::sync::Once;
        static INIT: Once = Once::new();

        pub fn setup() {
            INIT.call_once(|| {
                env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info"))
                    .try_init()
                    .ok();
            });
        }
    }

    #[test]
    fn test_full_migration_cycle() -> Result<()> {
        test_logger::setup();

        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            max_pool_size: 10,
            pool_min_idle: 2,
            ..SecurityConfig::default()
        };

        // Create temporary file for database
        let temp_db = NamedTempFile::new()?;
        let db_url = format!("file:{}", temp_db.path().to_str().unwrap());
        let db = Database::<SQLiteAdapter>::new(&db_url, &config)?;

        // Create test migrations in memory
        let mut migrations = Vec::new();
        migrations.push(Migration {
            version: 1,
            description: "Create users table".to_string(),
            up: r#"CREATE TABLE users (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"#
            .to_string(),
            down: "DROP TABLE users".to_string(),
        });

        migrations.push(Migration {
            version: 2,
            description: "Create posts table".to_string(),
            up: r#"CREATE TABLE posts (
                id INTEGER PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )"#
            .to_string(),
            down: "DROP TABLE posts".to_string(),
        });

        migrations.push(Migration {
            version: 3,
            description: "Create comments table".to_string(),
            up: r#"CREATE TABLE comments (
                id INTEGER PRIMARY KEY,
                content TEXT NOT NULL,
                post_id INTEGER,
                user_id INTEGER,
                FOREIGN KEY (post_id) REFERENCES posts(id),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )"#
            .to_string(),
            down: "DROP TABLE comments".to_string(),
        });

        // Run migrations
        db.run_migrations(migrations.clone())?;

        // Get a connection for verification
        let conn = db.get_connection()?;

        // First verify that the migrations table exists
        let migrations_table_exists: bool = conn
            .query_row(
                "SELECT 1 FROM sqlite_master WHERE type='table' AND name=?",
                params![config.migration_table],
                |_| Ok(true),
            )
            .unwrap_or(false);
        assert!(migrations_table_exists, "Migrations table should exist");

        // Verify migration table has correct entries
        let count: i64 = conn.query_row(
            &format!("SELECT COUNT(*) FROM {}", config.migration_table),
            [],
            |row| row.get(0),
        )?;
        assert_eq!(count, 3, "Should have 3 migrations applied");

        // Verify all tables exist
        let tables = vec!["users", "posts", "comments"];
        for table in tables {
            let exists: bool = conn
                .query_row(
                    "SELECT 1 FROM sqlite_master WHERE type='table' AND name=?",
                    params![table],
                    |_| Ok(true),
                )
                .unwrap_or(false);
            assert!(exists, "Table {} should exist", table);
        }

        // Verify table columns
        verify_table_columns(&db, "users", &["id", "name", "email", "created_at"])?;
        verify_table_columns(&db, "posts", &["id", "title", "content", "user_id"])?;
        verify_table_columns(&db, "comments", &["id", "content", "post_id", "user_id"])?;

        // Test idempotency - run migrations again
        db.run_migrations(migrations)?;
        let count: i64 = conn.query_row(
            &format!("SELECT COUNT(*) FROM {}", config.migration_table),
            [],
            |row| row.get(0),
        )?;
        assert_eq!(count, 3, "Should still have 3 migrations after rerun");

        Ok(())
    }

    #[test]
    fn test_migration_ordering() -> Result<()> {
        test_logger::setup();

        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            max_pool_size: 10,
            pool_min_idle: 2,
            ..SecurityConfig::default()
        };

        let db_url = format!(
            "file:test_migration_ordering_{}?mode=memory&cache=shared",
            uuid::Uuid::new_v4()
        );
        let db = Database::<SQLiteAdapter>::new(&db_url, &config)?;

        // Create test migrations in memory with out-of-order versions
        let mut migrations = Vec::new();
        migrations.push(Migration {
            version: 3,
            description: "Third migration".to_string(),
            up: "CREATE TABLE third (id INTEGER PRIMARY KEY)".to_string(),
            down: "DROP TABLE third".to_string(),
        });

        migrations.push(Migration {
            version: 1,
            description: "First migration".to_string(),
            up: "CREATE TABLE first (id INTEGER PRIMARY KEY)".to_string(),
            down: "DROP TABLE first".to_string(),
        });

        migrations.push(Migration {
            version: 2,
            description: "Second migration".to_string(),
            up: "-- This comment has a semicolon in text but not in code\nCREATE TABLE second (id INTEGER)".to_string(),
            down: "DROP TABLE second".to_string(),
        });

        // Run migrations
        db.run_migrations(migrations)?;

        // Get a connection for verification
        let conn = db.get_connection()?;

        // First verify that the migrations table exists
        let migrations_table_exists: bool = conn
            .query_row(
                "SELECT 1 FROM sqlite_master WHERE type='table' AND name=?",
                params![config.migration_table],
                |_| Ok(true),
            )
            .unwrap_or(false);
        assert!(migrations_table_exists, "Migrations table should exist");

        // Verify migrations were applied in correct order
        let mut stmt = conn.prepare(&format!(
            "SELECT version FROM {} ORDER BY version",
            config.migration_table
        ))?;

        let versions: Vec<i64> = stmt
            .query_map([], |row| row.get(0))?
            .collect::<Result<_, _>>()?;

        assert_eq!(
            versions,
            vec![1, 2, 3],
            "Migrations should be applied in version order"
        );

        // Verify tables exist in correct order
        for table in &["first", "second", "third"] {
            let exists: bool = conn
                .query_row(
                    "SELECT 1 FROM sqlite_master WHERE type='table' AND name=?",
                    params![table],
                    |_| Ok(true),
                )
                .unwrap_or(false);
            assert!(exists, "Table {} should exist", table);
        }

        Ok(())
    }

    fn verify_table_columns(
        db: &Database<SQLiteAdapter>,
        table_name: &str,
        expected_columns: &[&str],
    ) -> Result<()> {
        let conn = db.get_connection()?;
        let mut stmt = conn.prepare(&format!("PRAGMA table_info({})", table_name))?;

        let columns: Vec<String> = stmt
            .query_map([], |row| row.get(1))?
            .collect::<Result<_, _>>()?;

        for expected in expected_columns {
            assert!(
                columns.contains(&expected.to_string()),
                "Column {} not found in table {}",
                expected,
                table_name
            );
        }

        Ok(())
    }

    #[test]
    fn test_invalid_migration_files() {
        // Test missing "up" section
        let migrations = Migration::load_from_dir(Path::new("tests/invalid_migrations/missing_up"));
        assert!(
            migrations.is_err(),
            "Should reject migration missing up section"
        );
        assert!(migrations
            .unwrap_err()
            .to_string()
            .contains("Migration missing 'up' section"));

        // Test missing "down" section
        let migrations =
            Migration::load_from_dir(Path::new("tests/invalid_migrations/missing_down"));
        assert!(
            migrations.is_err(),
            "Should reject migration missing down section"
        );
        assert!(migrations
            .unwrap_err()
            .to_string()
            .contains("Migration missing 'down' section"));
    }

    #[test]
    fn test_failed_sql_execution() {
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            ..SecurityConfig::default()
        };

        let db_url = format!(
            "file:test_failed_sql_{}?mode=memory&cache=shared",
            uuid::Uuid::new_v4()
        );
        let db = Database::<SQLiteAdapter>::new(&db_url, &config).unwrap();
        let migrations =
            Migration::load_from_dir(Path::new("tests/invalid_migrations/invalid_sql")).unwrap();
        let result = db.run_migrations(migrations);

        assert!(result.is_err(), "Should fail on invalid SQL syntax");
        info!("Result: {:?}", result);
        let error = result.unwrap_err();
        info!("Error: {:?}", error);

        let error_message = error.to_string();
        assert!(error_message.contains("near \"name\": syntax error"));

        // Verify no tables were created
        let conn = db.get_connection().unwrap();
        let tables: Vec<String> = conn
            .prepare("SELECT name FROM sqlite_master WHERE type = 'table'")
            .unwrap()
            .query_map([], |row| row.get(0))
            .unwrap()
            .collect::<Result<_, _>>()
            .unwrap();

        assert!(
            !tables.contains(&"invalid_table".to_string()),
            "Invalid table should not exist"
        );
    }

    #[test]
    fn test_duplicate_versions() {
        // Create temp dir with duplicate versions
        let temp_dir = tempfile::tempdir().unwrap();
        let migrations_dir = temp_dir.path();

        // Copy valid migration
        std::fs::copy(
            "tests/test_migrations/001_initial.sql",
            migrations_dir.join("001_initial.sql"),
        )
        .unwrap();

        // Create duplicate version
        std::fs::write(
            migrations_dir.join("001_duplicate.sql"),
            "-- migrate:up\nCREATE TABLE bad_table (id INTEGER);\n-- migrate:down\nDROP TABLE bad_table;"
        ).unwrap();

        let migrations = Migration::load_from_dir(migrations_dir);

        assert!(migrations.is_err(), "Should detect duplicate versions");
        assert!(migrations
            .unwrap_err()
            .to_string()
            .contains("Duplicate migration version"));
    }

    #[test]
    fn test_corrupted_filenames() {
        // Test non-numeric version
        let temp_dir = tempfile::tempdir().unwrap();
        std::fs::write(
            temp_dir.path().join("XXX_bad_version.sql"),
            "-- migrate:up\nCREATE TABLE bad (id INTEGER);\n-- migrate:down\nDROP TABLE bad;",
        )
        .unwrap();

        // Should reject non-numeric version
        let migrations = Migration::load_from_dir(temp_dir.path());
        assert!(migrations.is_err(), "Should reject non-numeric version");
    }

    #[test]
    fn test_missing_migration_files() {
        let migrations = Migration::load_from_dir(Path::new("tests/test_migrations"));
        assert!(
            migrations.is_ok(),
            "Should handle missing migration directory"
        );

        // Simulate missing file by using different directory
        let migrations = Migration::load_from_dir(Path::new("tests/empty_dir"));
        assert!(
            migrations.is_err(),
            "Should handle missing migration directory"
        );
    }

    #[test]
    fn test_rollback_on_failure() {
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            ..SecurityConfig::default()
        };

        let db_url = format!("file:{}?mode=memory&cache=shared", uuid::Uuid::new_v4());
        let db = Database::<SQLiteAdapter>::new(&db_url, &config).unwrap();

        // Create temp dir with one good and one bad migration
        let temp_dir = tempfile::tempdir().unwrap();
        let migrations_dir = temp_dir.path();

        // Valid migration
        std::fs::copy(
            "tests/test_migrations/001_initial.sql",
            migrations_dir.join("001_initial.sql"),
        )
        .unwrap();

        // Invalid migration
        std::fs::write(
            migrations_dir.join("002_bad.sql"),
            "-- migrate:up\nINVALID SQL STATEMENT;\n-- migrate:down\nDROP TABLE bad;",
        )
        .unwrap();

        let migrations = Migration::load_from_dir(migrations_dir).unwrap();
        let result = db.run_migrations(migrations);
        assert!(result.is_err(), "Should fail on bad migration");

        // Verify no tables from either migration exist
        let conn = db.get_connection().unwrap();
        let tables: Vec<String> = conn
            .prepare("SELECT name FROM sqlite_master WHERE type = 'table'")
            .unwrap()
            .query_map([], |row| row.get(0))
            .unwrap()
            .collect::<Result<_, _>>()
            .unwrap();

        assert!(
            !tables.contains(&"users".to_string()),
            "Should roll back all changes"
        );
        assert!(
            !tables.contains(&"bad".to_string()),
            "Invalid table should not exist"
        );
    }

    #[test]
    fn test_explicit_release_connection() -> anyhow::Result<()> {
        test_logger::setup();

        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            ..SecurityConfig::default()
        };
        let db = Database::<SQLiteAdapter>::new("file::memory:?cache=shared", &config)?;

        // Obtain a connection and run a dummy query
        let conn = db.get_connection()?;
        let result: i32 = conn.query_row("SELECT 1", [], |row| row.get(0))?;
        assert_eq!(result, 1, "Initial query should return 1");

        // Explicitly release the connection
        db.adapter.release_connection(conn);

        // Obtain a new connection and run the dummy query again
        let conn2 = db.get_connection()?;
        let result2: i32 = conn2.query_row("SELECT 1", [], |row| row.get(0))?;
        assert_eq!(
            result2, 1,
            "Query after releasing connection should return 1"
        );

        Ok(())
    }

    #[test]
    fn test_connection_pool_exhaustion() -> Result<()> {
        test_logger::setup();

        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            max_pool_size: 2,
            pool_min_idle: 2,
            connection_timeout: Duration::from_secs(1),
            ..SecurityConfig::default()
        };

        let db = Database::<SQLiteAdapter>::new("file::memory:?cache=shared", &config)?;

        // Acquire max allowed connections
        let conn1 = db.get_connection()?;
        let conn2 = db.get_connection()?;

        // Attempt to get third connection
        let result = db.get_connection();
        assert!(
            result.is_err(),
            "Should fail to get third connection"
        );
        assert!(
            result.unwrap_err().to_string().contains("timed out"),
            "Should return timeout error"
        );

        // Release one connection and try again
        db.adapter.release_connection(conn1);
        let conn3 = db.get_connection()?;
        drop(conn2);
        drop(conn3);

        Ok(())
    }

    #[test]
    fn test_connection_validation() -> Result<()> {
        test_logger::setup();

        let config = SecurityConfig::default();
        let db_url = format!(
            "file:test_conn_validation_{}?mode=memory",
            uuid::Uuid::new_v4()
        );
        let db = Database::<SQLiteAdapter>::new(&db_url, &config)?;

        // Verify foreign keys are enabled
        let conn = db.get_connection()?;
        let fk_enabled: bool = conn.query_row("PRAGMA foreign_keys", [], |row| row.get(0))?;
        assert!(
            fk_enabled,
            "Foreign keys should be enabled on new connections"
        );

        Ok(())
    }

    #[test]
    fn test_table_name_validation() {
        // Valid names
        SQLiteAdapter::validate_table_name("valid_name").unwrap();
        SQLiteAdapter::validate_table_name("_valid123").unwrap();
        SQLiteAdapter::validate_table_name(&"a".repeat(64)).unwrap();

        // Invalid names
        assert!(SQLiteAdapter::validate_table_name("").is_err());
        assert!(SQLiteAdapter::validate_table_name("123invalid").is_err());
        assert!(SQLiteAdapter::validate_table_name("invalid-name").is_err());
        assert!(SQLiteAdapter::validate_table_name(&"a".repeat(65)).is_err());
        assert!(SQLiteAdapter::validate_table_name("test; DROP TABLE users").is_err());

        // Add unicode test
        assert!(SQLiteAdapter::validate_table_name("méil").is_err());
        assert!(SQLiteAdapter::validate_table_name("таблица").is_err());
    }

    #[test]
    fn test_migration_table_escaping() -> Result<()> {
        test_logger::setup();

        let config = SecurityConfig {
            migration_table: "strange_table_name".to_string(),
            ..SecurityConfig::default()
        };

        // Create temporary file database
        let temp_db = NamedTempFile::new()?;
        let db_url = format!("file:{}", temp_db.path().to_str().unwrap());
        let db = Database::<SQLiteAdapter>::new(&db_url, &config)?;

        let migrations = vec![Migration {
            version: 1,
            description: "Test".to_string(),
            up: "CREATE TABLE test (id INTEGER)".to_string(),
            down: "DROP TABLE test".to_string(),
        }];

        db.run_migrations(migrations)?;

        // Verify migration table exists with strange name
        let conn = db.get_connection()?;
        let exists: bool = conn
            .query_row(
                "SELECT 1 FROM sqlite_master WHERE type='table' AND name=?",
                params![config.migration_table],
                |_| Ok(true),
            )
            .unwrap_or(false);

        assert!(exists, "Migration table should exist with custom name");

        Ok(())
    }

    #[test]
    fn test_empty_migration() -> Result<()> {
        test_logger::setup();

        let config = SecurityConfig::default();
        let db_url = format!(
            "file:test_empty_migration_{}?mode=memory",
            uuid::Uuid::new_v4()
        );
        let db = Database::<SQLiteAdapter>::new(&db_url, &config)?;

        let migrations = vec![Migration {
            version: 1,
            description: "Empty".to_string(),
            up: "-- No operation".to_string(),
            down: "-- No operation".to_string(),
        }];

        db.run_migrations(migrations)?;

        // Verify no tables created
        let conn = db.get_connection()?;
        let tables: Vec<String> = conn
            .prepare("SELECT name FROM sqlite_master WHERE type = 'table'")?
            .query_map([], |row| row.get(0))?
            .collect::<Result<_, _>>()?;

        assert!(
            !tables.contains(&"test".to_string()),
            "No tables should be created"
        );

        Ok(())
    }

    #[test]
    fn test_concurrent_migrations() -> Result<()> {
        test_logger::setup();
        let config = SecurityConfig::default();
        let temp_db = NamedTempFile::new()?;
        let db_url = format!(
            "file:{}?mode=rwc&journal_mode=WAL",
            temp_db.path().to_str().unwrap()
        );
        let db = Arc::new(Database::<SQLiteAdapter>::new(&db_url, &config)?);

        let migrations = vec![Migration {
            version: 1,
            description: "Concurrent test".to_string(),
            up: "CREATE TABLE concurrent (id INTEGER)".to_string(),
            down: "DROP TABLE concurrent".to_string(),
        }];

        let handles: Vec<_> = (0..4)
            .map(|_| {
                let db = db.clone();
                let migrations = migrations.clone();
                std::thread::spawn(move || match db.run_migrations(migrations.clone()) {
                    Ok(_) => Ok(()),
                    Err(e) if e.to_string().contains("database is locked") => Ok(()),
                    Err(e) => Err(e),
                })
            })
            .collect();

        // Simply wait for all threads to complete without error
        for handle in handles {
            handle.join().unwrap()?;
        }

        let conn = db.get_connection()?;
        let count: i64 = conn.query_row(
            &format!("SELECT COUNT(*) FROM {}", config.migration_table),
            [],
            |row| row.get(0),
        )?;
        assert_eq!(count, 1, "Migration should be applied exactly once");

        // Verify table was created
        let table_count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='concurrent'",
            [],
            |row| row.get(0),
        )?;
        assert_eq!(table_count, 1, "Table should exist after migration");

        Ok(())
    }

    #[test]
    fn test_sql_injection_in_description() -> Result<()> {
        test_logger::setup();
        let config = SecurityConfig::default();
        let temp_db = NamedTempFile::new()?;
        let db_url = format!("file:{}", temp_db.path().to_str().unwrap());
        let db = Database::<SQLiteAdapter>::new(&db_url, &config)?;

        let migrations = vec![Migration {
            version: 1,
            description: "Test'); DROP TABLE users;--".to_string(),
            up: "CREATE TABLE test (id INTEGER)".to_string(),
            down: "DROP TABLE test".to_string(),
        }];

        db.run_migrations(migrations)?;

        let conn = db.get_connection()?;
        let description: String = conn.query_row(
            &format!("SELECT description FROM {} LIMIT 1", config.migration_table),
            [],
            |row| row.get(0),
        )?;

        assert_eq!(
            description, "Test'); DROP TABLE users;--",
            "Should safely store malicious description"
        );

        // Verify users table wasn't dropped
        let users_exists: bool = conn
            .query_row(
                "SELECT 1 FROM sqlite_master WHERE type='table' AND name='users'",
                [],
                |_| Ok(true),
            )
            .unwrap_or(false);
        assert!(!users_exists, "Users table should not exist");

        Ok(())
    }

    #[test]
    fn test_connection_state_recovery() -> Result<()> {
        test_logger::setup();
        let config = SecurityConfig {
            max_pool_size: 2,
            pool_min_idle: 2,
            ..SecurityConfig::default()
        };

        let db = Database::<SQLiteAdapter>::new("file::memory:?cache=shared", &config)?;

        // Get a connection and intentionally leave it in a bad state
        {
            let mut conn = db.get_connection()?;
            let tx = conn.transaction()?;
            tx.execute_batch("CREATE TABLE test (id INTEGER);")?;
            // Don't commit, let it drop
        }

        // Get another connection - should work despite previous connection's bad state
        let conn2 = db.get_connection()?;
        let result = conn2.query_row("SELECT 1", [], |row| row.get::<_, i32>(0));
        assert!(result.is_ok(), "Should be able to use new connection");

        // Verify the temporary table doesn't exist (transaction was rolled back)
        let table_exists: bool = conn2
            .query_row(
                "SELECT 1 FROM sqlite_master WHERE type='table' AND name='test'",
                [],
                |_| Ok(true),
            )
            .unwrap_or(false);
        assert!(!table_exists, "Temporary table should not exist");

        Ok(())
    }

    #[test]
    fn test_concurrent_connection_stress() -> Result<()> {
        test_logger::setup();
        let config = SecurityConfig {
            max_pool_size: 20,
            pool_min_idle: 2,
            ..SecurityConfig::default()
        };

        let db = Arc::new(Database::<SQLiteAdapter>::new(
            "file::memory:?cache=shared&mode=memory",
            &config,
        )?);

        // Create a table for concurrent access
        {
            let conn = db.get_connection()?;
            conn.execute_batch(
                "CREATE TABLE stress_test (id INTEGER PRIMARY KEY, counter INTEGER DEFAULT 0)",
            )?;
            conn.execute("INSERT INTO stress_test (id, counter) VALUES (1, 0)", [])?;
        }

        let threads: Vec<_> = (0..10)
            .map(|_| {
                let db = db.clone();
                std::thread::spawn(move || {
                    for _ in 0..100 {
                        loop {
                            match db.get_connection() {
                                Ok(mut conn) => {
                                    match || -> Result<()> {
                                        let tx = conn.transaction()?;

                                        // Read current value with retry on lock
                                        let current: i32 = tx.query_row(
                                            "SELECT counter FROM stress_test WHERE id = 1",
                                            [],
                                            |row| row.get(0),
                                        )?;

                                        // Increment with retry on lock
                                        tx.execute(
                                            "UPDATE stress_test SET counter = ? WHERE id = 1",
                                            params![current + 1],
                                        )?;

                                        tx.commit()?;
                                        let conn = db.get_connection()?;
                                        // Wait for table creation to be visible
                                        for _ in 0..5 {
                                            if let Ok(_) = conn.execute("SELECT 1 FROM stress_test WHERE id = 1", []) {
                                                break;
                                            }
                                            std::thread::sleep(std::time::Duration::from_millis(100));
                                        }
                                        Ok(())
                                    }() {
                                        Ok(_) => break,
                                        Err(e)
                                            if e.to_string()
                                                .contains("database table is locked") =>
                                        {
                                            std::thread::sleep(std::time::Duration::from_millis(
                                                10,
                                            ));
                                            continue;
                                        }
                                        Err(e) => panic!("Unexpected error: {}", e),
                                    }
                                }
                                Err(e) if e.to_string().contains("Connection pool exhausted") => {
                                    std::thread::sleep(std::time::Duration::from_millis(10));
                                    continue;
                                }
                                Err(e) => panic!("Unexpected error: {}", e),
                            }
                        }
                    }
                })
            })
            .collect();

        // Wait for all threads
        for handle in threads {
            handle.join().unwrap();
        }

        // Verify final count
        let conn = db.get_connection()?;
        let final_count: i32 =
            conn.query_row("SELECT counter FROM stress_test WHERE id = 1", [], |row| {
                row.get(0)
            })?;

        assert_eq!(
            final_count, 1000,
            "Counter should be 1000 after all concurrent operations"
        );

        Ok(())
    }

    #[test]
    fn test_high_concurrency_pool() -> Result<()> {
        test_logger::setup();
        let config = SecurityConfig {
            max_pool_size: 10,
            pool_min_idle: 2,
            ..SecurityConfig::default()
        };

        let db = Arc::new(Database::<SQLiteAdapter>::new(
            "file::memory:?cache=shared",
            &config,
        )?);

        // Create test table with proper synchronization
        {
            let mut conn = db.get_connection()?;
            let tx = conn.transaction()?;
            tx.execute_batch(
                "CREATE TABLE IF NOT EXISTS high_concurrency (id INTEGER PRIMARY KEY, value TEXT)",
            )?;
            tx.commit()?;
            let conn = db.get_connection()?;
            // Wait for table creation to be visible
            for _ in 0..5 {
                if let Ok(_) = conn.execute("SELECT 1 FROM high_concurrency LIMIT 1", []) {
                    break;
                }
                std::thread::sleep(std::time::Duration::from_millis(100));
            }
        }

        let thread_count = 20;
        let ops_per_thread = 50;
        let threads: Vec<_> = (0..thread_count)
            .map(|i| {
                let db = db.clone();
                std::thread::spawn(move || {
                    for j in 0..ops_per_thread {
                        let mut retries = 0;
                        let max_retries = 10;
                        loop {
                            match db.get_connection() {
                                Ok(mut conn) => {
                                    match || -> Result<()> {
                                        let tx = conn.transaction()?;
                                        // Verify table exists before attempting insert
                                        let exists: bool = tx.query_row(
                                            "SELECT 1 FROM sqlite_master WHERE type='table' AND name='high_concurrency'",
                                            [],
                                            |_| Ok(true),
                                        ).unwrap_or(false);
                                        
                                        if !exists {
                                            return Err(anyhow::anyhow!("Table does not exist"));
                                        }

                                        tx.execute(
                                            "INSERT INTO high_concurrency (value) VALUES (?)",
                                            params![format!("Thread {} - {}", i, j)],
                                        )?;
                                        tx.commit()?;
                                        Ok(())
                                    }() {
                                        Ok(_) => break,
                                        Err(e) if e.to_string().contains("database is locked")
                                            || e.to_string().contains("database table is locked")
                                            || e.to_string().contains("database schema is locked")
                                            || e.to_string().contains("no such table")
                                            || e.to_string().contains("Table does not exist") =>
                                        {
                                            if retries >= max_retries {
                                                panic!("Max retries exceeded: {}", e);
                                            }
                                            retries += 1;
                                            std::thread::sleep(std::time::Duration::from_millis(
                                                50 * retries as u64,
                                            ));
                                            continue;
                                        }
                                        Err(e) => panic!("Unexpected error: {}", e),
                                    }
                                }
                                Err(e) if e.to_string().contains("Connection pool exhausted") => {
                                    if retries >= max_retries {
                                        panic!("Max retries exceeded: {}", e);
                                    }
                                    retries += 1;
                                    std::thread::sleep(std::time::Duration::from_millis(
                                        50 * retries as u64,
                                    ));
                                    continue;
                                }
                                Err(e) => panic!("Unexpected error: {}", e),
                            }
                        }
                    }
                })
            })
            .collect();

        // Wait for all threads with timeout
        let timeout = std::time::Duration::from_secs(60);
        let start = std::time::Instant::now();
        for handle in threads {
            if let Err(_) = handle.join() {
                panic!("Thread panicked");
            }
            if start.elapsed() > timeout {
                panic!("Test timed out");
            }
        }

        // Verify results
        let conn = db.get_connection()?;
        let count: i64 = conn.query_row("SELECT COUNT(*) FROM high_concurrency", [], |row| {
            row.get(0)
        })?;

        assert_eq!(
            count,
            (thread_count * ops_per_thread) as i64,
            "Should have all successful inserts"
        );

        // Verify no duplicate IDs
        let duplicate_count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM (SELECT id FROM high_concurrency GROUP BY id HAVING COUNT(*) > 1)",
            [],
            |row| row.get(0),
        )?;
        assert_eq!(duplicate_count, 0, "Should have no duplicate IDs");

        Ok(())
    }
}

// #[cfg(test)]
// mod benchmarks {
//     use super::*;
//     use crate::{config::SecurityConfig, database::Database};
//     use test::Bencher;

//     #[bench]
//     fn bench_connection_pool(b: &mut Bencher) {
//         let config = SecurityConfig {
//             max_pool_size: 10,
//             pool_min_idle: 5,
//             ..SecurityConfig::default()
//         };
//         let db_url = format!("file:bench_pool_{}?mode=memory", uuid::Uuid::new_v4());
//         let db = Database::<SQLiteAdapter>::new(&db_url, &config).unwrap();

//         b.iter(|| {
//             let conn = db.get_connection().unwrap();
//             conn.execute_batch("CREATE TABLE IF NOT EXISTS bench (id INTEGER)")
//                 .unwrap();
//             db.adapter.release_connection(conn);
//         });
//     }

//     #[bench]
//     fn bench_migrations(b: &mut Bencher) {
//         let config = SecurityConfig::default();
//         let db_url = format!("file:bench_migrations_{}?mode=memory", uuid::Uuid::new_v4());

//         b.iter(|| {
//             let db = Database::<SQLiteAdapter>::new(&db_url, &config).unwrap();
//             let migrations = vec![Migration {
//                 version: 1,
//                 description: "Bench".to_string(),
//                 up: "CREATE TABLE bench (id INTEGER)".to_string(),
//                 down: "DROP TABLE bench".to_string(),
//             }];
//             db.run_migrations(migrations.clone()).unwrap();
//         });
//     }
// }
