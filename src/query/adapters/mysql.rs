use anyhow::Result;
use mysql::{prelude::Queryable, Pool, PooledConn};
use std::fmt;

use crate::adapters::database_adapter::DatabaseAdapter;
use crate::{config::SecurityConfig, migrations::Migration};

/// MySQL database adapter implementation
///
/// This adapter provides MySQL-specific functionality while implementing the common DatabaseAdapter trait.
/// It handles connection pooling, migrations, and MySQL-specific optimizations.
///
/// # Examples
///
/// ```no_run
/// # use db::{config::SecurityConfig, database::Database, adapters::{DatabaseAdapter, mysql::MySQLAdapter}, Migration};
/// # fn main() -> anyhow::Result<()> {
/// let config = SecurityConfig::default();
/// let adapter = MySQLAdapter::connect("mysql://root:pass@localhost/test", &config)?;
/// let conn = adapter.get_connection()?;
/// # Ok(())
/// # }
/// ```
#[derive(Clone)]
pub struct MySQLAdapter {
    pool: Pool,
    migration_table: String,
    config: SecurityConfig,
    database_url: String,
}

impl fmt::Debug for MySQLAdapter {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("MySQLAdapter")
            .field("migration_table", &self.migration_table)
            .field("config", &self.config)
            .field("database_url", &self.database_url)
            .finish()
    }
}

/// Implementation of DatabaseAdapter trait for MySQL
impl DatabaseAdapter for MySQLAdapter {
    type Connection = PooledConn;

    /// Creates a new MySQL connection pool with the specified configuration
    ///
    /// # Arguments
    /// * `database_url` - MySQL connection URL (mysql://user:pass@host:port/dbname)
    /// * `config` - Security and pool configuration
    ///
    /// # Examples
    ///
    /// ```no_run
    /// use db::{config::SecurityConfig, adapters::{DatabaseAdapter, mysql::MySQLAdapter}};
    /// # fn main() -> anyhow::Result<()> {
    /// let config = SecurityConfig::default();
    /// let adapter = MySQLAdapter::connect("mysql://root:pass@localhost/test", &config)?;
    /// # Ok(())
    /// # }
    /// ```
    fn connect(database_url: &str, config: &SecurityConfig) -> Result<Self> {
        SecurityConfig::validate_migration_table_name(&config.migration_table)?;
        config.validate()?;

        let opts = mysql::Opts::from_url(database_url)
            .map_err(|e| anyhow::anyhow!("Invalid MySQL connection string: {}", e))?;
        let pool = mysql::Pool::new(opts)?;

        Ok(Self {
            pool,
            migration_table: config.migration_table.clone(),
            config: config.clone(),
            database_url: database_url.to_string(),
        })
    }

    /// Gets a connection from the pool
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{config::SecurityConfig, database::Database, adapters::mysql::MySQLAdapter};
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let db = Database::<MySQLAdapter>::new("mysql://root:pass@localhost/test", &config)?;
    /// let conn = db.get_connection()?;
    /// # Ok(())
    /// # }
    /// ```
    fn get_connection(&self) -> Result<Self::Connection> {
        self.pool.get_conn().map_err(|e| anyhow::anyhow!(e))
    }

    /// Runs database migrations in a transaction
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{config::SecurityConfig, database::Database, adapters::{DatabaseAdapter, mysql::MySQLAdapter}, Migration};
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let db = Database::<MySQLAdapter>::new("mysql://root:pass@localhost/test", &config)?;
    /// let migrations = vec![
    ///     Migration {
    ///         version: 1,
    ///         description: "Create users table".to_string(),
    ///         up: "CREATE TABLE users (id INT PRIMARY KEY)".to_string(),
    ///         down: "DROP TABLE users".to_string(),
    ///     }
    /// ];
    /// db.run_migrations(migrations)?;
    /// # Ok(())
    /// # }
    /// ```
    fn run_migrations(&self, migrations: Vec<Migration>) -> Result<()> {
        use log::debug;

        let mut conn = self.get_connection()?;
        let mut tx = conn.start_transaction(mysql::TxOpts::default())?;

        // Create migrations table with proper escaping
        tx.query_drop(format!(
            r#"CREATE TABLE IF NOT EXISTS `{}` (
                version BIGINT PRIMARY KEY,
                description TEXT NOT NULL,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"#,
            self.migration_table.replace("`", "``")
        ))?;

        for migration in migrations {
            // Check if migration already exists
            let exists: Option<i64> = tx.exec_first(
                format!("SELECT 1 FROM `{}` WHERE version = ?", self.migration_table),
                (migration.version,),
            )?;

            if exists.is_some() {
                debug!("Skipping already applied migration: {}", migration.version);
                continue;
            }

            // Execute the migration
            tx.query_drop(&migration.up)?;

            // Record the migration
            tx.exec_drop(
                format!(
                    "INSERT INTO `{}` (version, description) VALUES (?, ?)",
                    self.migration_table
                ),
                (migration.version, migration.description),
            )?;
        }

        tx.commit()?;
        Ok(())
    }

    fn release_connection(&self, _conn: Self::Connection) {
        // Connections are automatically returned to the pool when dropped
    }
}

#[cfg(test)]
mod tests {
    use crate::adapters::mysql::MySQLAdapter;
    use crate::adapters::DatabaseAdapter;
    use crate::Migration;
    use crate::{config::SecurityConfig, database::Database};
    use anyhow::Result;
    use mysql::prelude::Queryable;
    use std::path::Path;
    use std::sync::Arc;
    use tempfile;
    use uuid::Uuid;

    mod test_logger {
        use std::sync::Once;
        static INIT: Once = Once::new();

        pub fn setup() {
            INIT.call_once(|| {
                env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info"))
                    .try_init()
                    .ok();
            });
        }
    }

    struct TempDB {
        db_name: String,
        base_url: String,
        migration_table: String,
    }

    impl TempDB {
        fn new() -> Result<Self> {
            let db_name = format!("test_db_{}", Uuid::new_v4().to_string().replace("-", "_"));
            let database_url = std::env::var("TEST_MYSQL_URL")
                .unwrap_or_else(|_| "mysql://root:my-secret-pw@localhost:3306".to_string());
            let migration_table = format!(
                "test_migrations_{}",
                Uuid::new_v4().to_string().replace("-", "_")
            );

            // Parse connection options
            let opts = mysql::Opts::from_url(&database_url)
                .map_err(|e| anyhow::anyhow!("Invalid MySQL URL: {}", e))?;

            // Create connection without SSL
            let mut conn = mysql::Conn::new(opts)?;

            // Verify connection privileges
            conn.query_drop("SELECT 1")?;

            // Create the test database
            conn.query_drop(format!("CREATE DATABASE `{}`", db_name))?;

            Ok(Self {
                db_name,
                base_url: database_url,
                migration_table,
            })
        }

        fn url(&self) -> String {
            format!("{}/{}", self.base_url, self.db_name)
        }
    }

    impl Drop for TempDB {
        fn drop(&mut self) {
            // Parse the database URL
            let opts = match mysql::Opts::from_url(&self.base_url) {
                Ok(options) => options,
                Err(e) => {
                    eprintln!("Failed to parse database URL '{}': {}", self.base_url, e);
                    return;
                }
            };

            // Establish a connection to the MySQL server
            let mut conn = match mysql::Conn::new(opts) {
                Ok(connection) => connection,
                Err(e) => {
                    eprintln!(
                        "Failed to connect to MySQL server '{}': {}",
                        self.base_url, e
                    );
                    return;
                }
            };

            // Attempt to drop the temporary database
            if let Err(e) = conn.query_drop(format!("DROP DATABASE `{}`", self.db_name)) {
                eprintln!("Failed to drop test database '{}': {}", self.db_name, e);
            } else {
                println!("Successfully dropped test database '{}'", self.db_name);
            }
        }
    }

    #[test]
    fn test_full_migration_cycle() -> Result<()> {
        test_logger::setup();

        let temp_db = TempDB::new()?;
        let config = SecurityConfig {
            migration_table: temp_db.migration_table.clone(),
            ..SecurityConfig::default()
        };
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;
        let migrations = Migration::load_from_dir(Path::new("tests/test_migrations"))?;
        db.run_migrations(migrations)?;

        verify_migration_state(&db, 4, &["users", "posts", "comments"])?;
        verify_table_columns(&db, "users", &["id", "name", "email", "created_at"])?;

        // Test idempotency
        let migrations = Migration::load_from_dir(Path::new("tests/test_migrations"))?;
        db.run_migrations(migrations)?;
        let mut conn = db.get_connection()?;
        let count: Option<usize> =
            conn.query_first(format!("SELECT COUNT(*) FROM {}", config.migration_table))?;
        assert_eq!(count, Some(4), "Migrations should not be reapplied");

        Ok(())
    }

    #[test]
    fn test_migration_ordering() -> Result<()> {
        let temp_db = TempDB::new()?;
        let config = SecurityConfig {
            migration_table: temp_db.migration_table.clone(),
            ..SecurityConfig::default()
        };
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;
        let migrations = Migration::load_from_dir(Path::new("tests/test_migrations"))?;
        db.run_migrations(migrations)?;

        let mut conn = db.get_connection()?;
        let versions: Vec<i64> = conn
            .query_map(
                format!(
                    "SELECT version FROM {} ORDER BY version",
                    config.migration_table
                ),
                |row: mysql::Row| row.get::<i64, _>(0),
            )?
            .into_iter()
            .flatten()
            .collect();

        assert_eq!(
            versions,
            vec![1, 2, 3, 4],
            "Migrations should be applied in order"
        );
        Ok(())
    }

    fn verify_migration_state(
        db: &Database<MySQLAdapter>,
        expected_count: usize,
        expected_tables: &[&str],
    ) -> Result<()> {
        let mut conn = db.get_connection()?;

        // Verify migration count
        let count: Option<usize> = conn.query_first(format!(
            "SELECT COUNT(*) FROM {}",
            db.adapter.migration_table
        ))?;
        assert_eq!(
            count.unwrap_or(0),
            expected_count,
            "Unexpected number of applied migrations"
        );

        // Verify table existence
        let existing_tables: Vec<String> = conn
            .query_map(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE()",
                |row: mysql::Row| {
                    row.get::<String, _>(0)
                        .ok_or_else(|| anyhow::anyhow!("Missing column value"))
                },
            )?
            .into_iter()
            .flatten()
            .collect();

        for expected in expected_tables {
            assert!(
                existing_tables.contains(&expected.to_string()),
                "Table {} not found",
                expected
            );
        }

        Ok(())
    }

    fn verify_table_columns(
        db: &Database<MySQLAdapter>,
        table_name: &str,
        expected_columns: &[&str],
    ) -> Result<()> {
        let mut conn = db.get_connection()?;
        let columns: Vec<String> = conn.exec_map(
            format!(
                "SELECT column_name FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = ?"
            ),
            (table_name,),
            |row: mysql::Row| row.get(0),
        )?
        .into_iter()
        .flatten()
        .collect();

        for expected in expected_columns {
            assert!(
                columns.contains(&expected.to_string()),
                "Column {} not found in table {}",
                expected,
                table_name
            );
        }

        Ok(())
    }

    #[test]
    fn test_invalid_migration_files() {
        let migrations = Migration::load_from_dir(Path::new("tests/invalid_migrations/missing_up"));
        assert!(migrations.is_err());
    }

    #[test]
    fn test_failed_sql_execution() {
        let temp_db = TempDB::new().unwrap();
        let config = SecurityConfig {
            migration_table: temp_db.migration_table.clone(),
            ..SecurityConfig::default()
        };
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config).unwrap();
        let migrations =
            Migration::load_from_dir(Path::new("tests/invalid_migrations/invalid_sql")).unwrap();
        let result = db.run_migrations(migrations);

        assert!(result.is_err());
    }

    #[test]
    fn test_duplicate_versions() {
        // Create temp dir with duplicate versions
        let temp_dir = tempfile::tempdir().unwrap();
        let migrations_dir = temp_dir.path();

        // Copy valid migration
        std::fs::copy(
            "tests/test_migrations/001_initial.sql",
            migrations_dir.join("001_initial.sql"),
        )
        .unwrap();

        // Create duplicate version
        std::fs::write(
            migrations_dir.join("001_duplicate.sql"),
            "-- migrate:up\nCREATE TABLE bad_table (id INTEGER);\n-- migrate:down\nDROP TABLE bad_table;",
        ).unwrap();

        let migrations = Migration::load_from_dir(migrations_dir);
        assert!(migrations.is_err(), "Should detect duplicate versions");
        assert!(migrations
            .unwrap_err()
            .to_string()
            .contains("Duplicate migration version"));
    }

    #[test]
    fn test_rollback_on_failure() {
        let temp_db = TempDB::new().unwrap();
        let config = SecurityConfig {
            migration_table: temp_db.migration_table.clone(),
            ..SecurityConfig::default()
        };
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config).unwrap();

        // Create temp dir with one good and one bad migration
        let temp_dir = tempfile::tempdir().unwrap();
        let migrations_dir = temp_dir.path();

        // Valid migration
        std::fs::copy(
            "tests/test_migrations/001_initial.sql",
            migrations_dir.join("001_initial.sql"),
        )
        .unwrap();

        // Invalid migration
        std::fs::write(
            migrations_dir.join("002_bad.sql"),
            "-- migrate:up\nINVALID SQL STATEMENT;\n-- migrate:down\nDROP TABLE bad;",
        )
        .unwrap();

        let migrations = Migration::load_from_dir(migrations_dir).unwrap();
        let result = db.run_migrations(migrations);
        assert!(result.is_err(), "Should fail on bad migration");

        // Verify only tables from successful migrations exist
        let mut conn = db.get_connection().unwrap();
        let tables: Vec<Option<String>> = conn
            .query_map(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE()",
                |row: mysql::Row| row.get::<String, _>(0),
            )
            .into_iter()
            .flatten()
            .collect();

        assert!(
            tables.contains(&Some("users".to_string())),
            "DDL operations are auto-committed in MySQL"
        );
        assert!(
            !tables.contains(&Some("bad".to_string())),
            "Invalid table should not exist"
        );
    }

    #[test]
    fn test_explicit_release_connection() -> anyhow::Result<()> {
        test_logger::setup();

        let temp_db = TempDB::new()?;
        let config = SecurityConfig {
            migration_table: temp_db.migration_table.clone(),
            ..SecurityConfig::default()
        };

        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;

        // Obtain a connection and perform a dummy query
        let mut conn = db.get_connection()?;
        let result: Option<u8> = conn.query_first("SELECT 1")?;
        assert_eq!(result, Some(1), "Initial query should return 1");

        // Explicitly release the connection back to the pool
        db.adapter.release_connection(conn);

        // Obtain a new connection and perform the query again
        let mut conn2 = db.get_connection()?;
        let result2: Option<u8> = conn2.query_first("SELECT 1")?;
        assert_eq!(
            result2,
            Some(1),
            "Query after releasing connection should return 1"
        );

        Ok(())
    }

    // Current implementation of connection pool, we don't have option to configure pool constraints
    // #[test]
    // fn test_connection_pool_exhaustion() -> Result<()> {
    //     test_logger::setup();
    //     let temp_db = TempDB::new()?;
    //     let config = SecurityConfig {
    //         migration_table: temp_db.migration_table.clone(),
    //         max_pool_size: 2,
    //         pool_min_idle: 2,
    //         ..SecurityConfig::default()
    //     };
    //     let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;

    //     // Acquire max allowed connections
    //     let conn1 = db.get_connection()?;
    //     let conn2 = db.get_connection()?;

    //     // Attempt to get third connection
    //     let result = db.get_connection();
    //     if let Err(e) = result {
    //         assert_eq!(
    //             e.to_string(),
    //             "Connection pool exhausted",
    //             "Should return pool exhausted error"
    //         );
    //     } else {
    //         panic!("Expected connection pool exhaustion error");
    //     }

    //     // Release one connection and try again
    //     db.adapter.release_connection(conn1);
    //     let conn3 = db.get_connection()?;
    //     drop(conn2);
    //     drop(conn3);
    //     Ok(())
    // }

    #[test]
    fn test_connection_reuse() -> Result<()> {
        test_logger::setup();
        let temp_db = TempDB::new()?;
        let config = SecurityConfig {
            migration_table: temp_db.migration_table.clone(),
            max_pool_size: 2,
            pool_min_idle: 1,
            ..SecurityConfig::default()
        };
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;

        // Get and release connection
        let conn = db.get_connection()?;
        db.adapter.release_connection(conn);

        // Get connection again
        let _conn2 = db.get_connection()?;
        Ok(())
    }

    #[test]
    fn test_pool_growth() -> Result<()> {
        test_logger::setup();
        let temp_db = TempDB::new()?;
        let config = SecurityConfig {
            migration_table: temp_db.migration_table.clone(),
            max_pool_size: 5,
            pool_min_idle: 2,
            ..SecurityConfig::default()
        };
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;

        let mut connections = Vec::new();
        for _ in 0..5 {
            connections.push(db.get_connection()?);
        }

        // Releasing all connections should allow pool to grow to max size
        for conn in connections {
            db.adapter.release_connection(conn);
        }
        Ok(())
    }

    #[test]
    fn test_migration_table_escaping() -> Result<()> {
        test_logger::setup();
        let temp_db = TempDB::new()?;
        let config = SecurityConfig {
            migration_table: "migration_table".to_string(),
            ..SecurityConfig::default()
        };
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;

        let migrations = vec![Migration {
            version: 1,
            description: "Test".to_string(),
            up: "CREATE TABLE test (id INT PRIMARY KEY)".to_string(),
            down: "DROP TABLE test".to_string(),
        }];

        db.run_migrations(migrations)?;

        // Verify migration table exists with escaped name
        let mut conn = db.get_connection()?;
        let count: Option<u64> = conn.query_first(format!(
            "SELECT COUNT(*) FROM information_schema.tables 
             WHERE table_schema = DATABASE() 
             AND table_name = '{}'",
            config.migration_table
        ))?;
        let exists = count.unwrap_or(0) > 0;
        assert!(exists, "Migration table with special name should exist");
        Ok(())
    }

    #[test]
    fn test_empty_migration() -> Result<()> {
        test_logger::setup();
        let temp_db = TempDB::new()?;
        let config = SecurityConfig::default();
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;

        let migrations = vec![Migration {
            version: 1,
            description: "Empty".to_string(),
            up: "-- No operation".to_string(),
            down: "-- No operation".to_string(),
        }];

        db.run_migrations(migrations)?;

        // Verify no tables created
        let mut conn = db.get_connection()?;
        let tables: Vec<String> = conn
            .exec_map(
                "SELECT table_name FROM information_schema.tables 
             WHERE table_schema = DATABASE() 
             AND table_name != ?",
                (config.migration_table,),
                |row: mysql::Row| row.get(0),
            )?
            .into_iter()
            .flatten()
            .collect();
        assert_eq!(
            tables.len(),
            0,
            "No tables should be created by empty migration"
        );
        Ok(())
    }

    #[test]
    fn test_concurrent_migrations() -> Result<()> {
        test_logger::setup();
        let temp_db = TempDB::new()?;
        let config = SecurityConfig::default();
        let db = Arc::new(Database::<MySQLAdapter>::new(&temp_db.url(), &config)?);

        let migrations = vec![Migration {
            version: 1,
            description: "Concurrent test".to_string(),
            up: "CREATE TABLE IF NOT EXISTS concurrent (id INT PRIMARY KEY)".to_string(),
            down: "DROP TABLE concurrent".to_string(),
        }];

        let handles: Vec<_> = (0..4)
            .map(|_| {
                let db = db.clone();
                let migrations = migrations.clone();
                std::thread::spawn(move || match db.run_migrations(migrations.clone()) {
                    Ok(_) => Ok(()),
                    Err(e)
                        if e.to_string().contains("Lock wait timeout exceeded")
                            || e.to_string().contains("already exists")
                            || e.to_string().contains("Duplicate entry") =>
                    {
                        Ok(())
                    }
                    Err(e) => Err(e),
                })
            })
            .collect();

        for handle in handles {
            handle.join().unwrap()?;
        }

        let mut conn = db.get_connection()?;
        let count: u64 = conn
            .query_first(format!("SELECT COUNT(*) FROM `{}`", config.migration_table))?
            .unwrap();
        assert_eq!(count, 1, "Migration should be applied exactly once");

        let table_count: u64 = conn
            .query_first(
                "SELECT COUNT(*) FROM information_schema.tables 
             WHERE table_schema = DATABASE() AND table_name = 'concurrent'",
            )?
            .unwrap();
        assert_eq!(table_count, 1, "Table should exist after migration");

        Ok(())
    }

    #[test]
    fn test_transaction_isolation() -> Result<()> {
        test_logger::setup();
        let temp_db = TempDB::new()?;
        let config = SecurityConfig::default();
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;

        let migrations = vec![Migration {
            version: 1,
            description: "Isolation test".to_string(),
            up: "CREATE TABLE test (id INT PRIMARY KEY) ENGINE=InnoDB".to_string(),
            down: "DROP TABLE test".to_string(),
        }];

        db.run_migrations(migrations)?;

        let mut conn1 = db.get_connection()?;
        let mut conn2 = db.get_connection()?;

        // Use raw query instead of prepared statement for transaction start
        conn1.query_drop("START TRANSACTION")?;
        conn1.query_drop("INSERT INTO test VALUES (1)")?;

        let count: Option<u64> = conn2.query_first("SELECT COUNT(*) FROM test")?;
        assert_eq!(
            count,
            Some(0),
            "Isolated transactions should not see uncommitted changes"
        );

        conn1.query_drop("COMMIT")?;
        Ok(())
    }

    #[test]
    fn test_server_going_away() -> Result<()> {
        test_logger::setup();
        let temp_db = TempDB::new()?;
        let config = SecurityConfig::default();
        let db = Database::<MySQLAdapter>::new(&temp_db.url(), &config)?;

        let mut conn = db.get_connection()?;
        conn.query_drop("SET SESSION wait_timeout = 1")?; // 1 second timeout
        std::thread::sleep(std::time::Duration::from_secs(2));

        // Should handle server closing connection
        let result = conn.query_drop("SELECT 1");
        assert!(result.is_err());
        let error_msg = result.unwrap_err().to_string();
        assert!(
            error_msg.contains("gone away")
                || error_msg.contains("Lost connection")
                || error_msg.contains("Packets out of sync"),
            "Unexpected error message: {}",
            error_msg
        );

        // Release the dead connection before getting a new one
        db.adapter.release_connection(conn);

        // Should get fresh connection from pool
        let mut new_conn = db.get_connection()?;
        new_conn.query_drop("SELECT 1")?;
        Ok(())
    }
}
