//! Database adapters for different database backends
//!
//! This module provides a set of database adapters that implement the common `DatabaseAdapter` trait.
//! Each adapter provides specific functionality for its respective database system while maintaining
//! a consistent interface for database operations.
//!
//! # Available Adapters
//!
//! - [`MySQLAdapter`]: Adapter for MySQL/MariaDB databases
//! - [`PostgresAdapter`]: Adapter for PostgreSQL databases with TLS support
//! - [`SQLiteAdapter`]: Adapter for SQLite databases (file-based and in-memory)
//!
//! # Common Features
//!
//! All adapters provide:
//! - Connection pooling via r2d2
//! - Migration management
//! - Transaction support
//! - Security validations
//!
//! # Examples
//!
//! Using MySQL adapter:
//! ```no_run
//! # use db::{config::SecurityConfig, database::Database, adapters::mysql::MySQLAdapter};
//! # fn main() -> anyhow::Result<()> {
//! let config = SecurityConfig::default();
//! let db = Database::<MySQLAdapter>::new("mysql://user:pass@localhost/db", &config)?;
//! # Ok(())
//! # }
//! ```
//!
//! Using PostgreSQL adapter:
//! ```no_run
//! # use db::{config::SecurityConfig, database::Database, adapters::postgres::PostgresAdapter};
//! # fn main() -> anyhow::Result<()> {
//! let config = SecurityConfig::default();
//! let db = Database::<PostgresAdapter>::new("postgres://user:pass@localhost/db", &config)?;
//! # Ok(())
//! # }
//! ```
//!
//! Using SQLite adapter:
//! ```no_run
//! # use db::{config::SecurityConfig, database::Database, adapters::sqlite::SQLiteAdapter};
//! # fn main() -> anyhow::Result<()> {
//! let config = SecurityConfig::default();
//! let db = Database::<SQLiteAdapter>::new("file:db.sqlite3", &config)?;
//! # Ok(())
//! # }
//! ```

pub mod database_adapter;
pub use database_adapter::DatabaseAdapter;

pub mod mysql;
pub mod postgres;
pub mod sqlite;

// Re-export adapters for convenience
pub use mysql::MySQLAdapter;
pub use postgres::PostgresAdapter;
pub use sqlite::SQLiteAdapter;

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config::SecurityConfig, database::Database, Migration};
    use env_logger;
    use log;
    use tempfile::tempdir;

    #[test]
    fn test_adapter_interchangeability() {
        // Test that all adapters can be used with the same Database type
        let _: Database<MySQLAdapter>;
        let _: Database<PostgresAdapter>;
        let _: Database<SQLiteAdapter>;
    }

    #[test]
    fn test_migration_compatibility() -> anyhow::Result<()> {
        // Create test migrations
        let temp_dir = tempdir()?;
        let migration_path = temp_dir.path();

        std::fs::write(
            migration_path.join("001_test.sql"),
            r#"-- migrate:up
            CREATE TABLE test (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL
            );
            -- migrate:down
            DROP TABLE test;
            "#,
        )?;

        // Load migrations
        let migrations = Migration::load_from_dir(migration_path)?;

        // Test migrations work with SQLite (in-memory for testing)
        let config = SecurityConfig::default();
        let db = Database::<SQLiteAdapter>::new("file::memory:", &config)?;
        assert!(db.run_migrations(migrations).is_ok());

        Ok(())
    }

    #[test]
    fn test_connection_pool_limits() -> anyhow::Result<()> {
        use log::{debug, info, warn};
        // Initialize test logger
        let _ = env_logger::builder().is_test(true).try_init();

        info!("Starting connection pool limits test");
        let config = SecurityConfig {
            max_pool_size: 1, // Set very low for testing
            ..SecurityConfig::default()
        };
        debug!(
            "Created config with max_pool_size: {}",
            config.max_pool_size
        );

        // Use shared in-memory database to allow connection reuse
        info!("Creating SQLite database with shared cache");
        let db = Database::<SQLiteAdapter>::new("file::memory:?cache=shared", &config)?;
        debug!("Database created successfully");

        // Get first connection
        info!("Attempting to get first connection");
        let conn1 = db.get_connection()?;
        debug!("Successfully acquired first connection");

        // Second connection should fail due to pool exhaustion
        info!("Attempting to get second connection (should fail)");
        let conn2_result = db.get_connection();
        match &conn2_result {
            Ok(_) => warn!("Unexpectedly got second connection when pool should be exhausted"),
            Err(e) => debug!("Expected error getting second connection: {}", e),
        }
        assert!(conn2_result.is_err(), "Should fail due to pool exhaustion");

        // Release first connection back to the pool
        info!("Releasing first connection back to pool");
        db.adapter.release_connection(conn1);
        debug!("Connection released");

        // Add a small delay to allow the pool to recycle the connection
        let delay = std::time::Duration::from_millis(50); // Increased delay for more reliability
        debug!("Waiting {:?} for connection recycling", delay);
        std::thread::sleep(delay);

        // Now should be able to get a connection again
        info!("Attempting to get new connection after release");
        let conn3 = match db.get_connection() {
            Ok(conn) => {
                debug!("Successfully acquired new connection");
                conn
            }
            Err(e) => {
                warn!("Failed to get connection after release: {}", e);
                return Err(e);
            }
        };

        info!("Testing connection with SELECT 1");
        match conn3.query_row("SELECT 1", [], |_| Ok(())) {
            Ok(_) => debug!("SELECT 1 query executed successfully"),
            Err(e) => warn!("SELECT 1 query failed: {}", e),
        }
        assert!(conn3.query_row("SELECT 1", [], |_| Ok(())).is_ok());

        info!("Connection pool test completed successfully");
        Ok(())
    }
}
