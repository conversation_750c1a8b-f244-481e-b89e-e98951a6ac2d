use crate::{config::SecurityConfig, migrations::Migration};
use anyhow::Result;
use postgres::NoTls;
use r2d2::Pool;
use r2d2_postgres::PostgresConnectionManager;

/// PostgreSQL database adapter implementation
///
/// This adapter provides PostgreSQL-specific functionality while implementing the common DatabaseAdapter trait.
/// It handles connection pooling, migrations, and PostgreSQL-specific features like native TLS support.
///
/// # Examples
///
/// ```no_run
/// # use db::{config::SecurityConfig, database::Database, adapters::{DatabaseAdapter, postgres::PostgresAdapter}, Migration};
/// # fn main() -> anyhow::Result<()> {
/// let config = SecurityConfig::default();
/// let adapter = PostgresAdapter::connect("postgres://user:pass@localhost/test", &config)?;
/// let conn = adapter.get_connection()?;
/// # Ok(())
/// # }
/// ```
#[derive(Debug, Clone)]
pub struct PostgresAdapter {
    pool: Pool<PostgresConnectionManager<NoTls>>,
    migration_table: String,
}

impl crate::adapters::database_adapter::DatabaseAdapter for PostgresAdapter {
    type Connection = r2d2::PooledConnection<PostgresConnectionManager<NoTls>>;

    /// Creates a new PostgreSQL connection pool with TLS support
    ///
    /// # Arguments
    /// * `database_url` - PostgreSQL connection URL (postgres://user:pass@host:port/dbname)
    /// * `config` - Security and pool configuration
    ///
    /// # Examples
    ///
    /// ```no_run
    /// use db::{config::SecurityConfig, adapters::{DatabaseAdapter, postgres::PostgresAdapter}};
    /// # fn main() -> anyhow::Result<()> {
    /// let config = SecurityConfig::default();
    /// let adapter = PostgresAdapter::connect("postgres://user:pass@localhost/test", &config)?;
    /// # Ok(())
    /// # }
    /// ```
    fn connect(database_url: &str, config: &SecurityConfig) -> Result<Self> {
        let manager = PostgresConnectionManager::new(database_url.parse()?, NoTls);

        let pool = Pool::builder()
            .max_size(config.max_pool_size as u32)
            .min_idle(Some(config.pool_min_idle as u32))
            .connection_timeout(std::time::Duration::from_millis(
                config.connection_timeout.as_millis() as u64,
            ))
            .build(manager)?;

        Ok(Self {
            pool,
            migration_table: config.migration_table.clone(),
        })
    }

    /// Gets a connection from the pool
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{config::SecurityConfig, adapters::{DatabaseAdapter, postgres::PostgresAdapter}};
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let adapter = PostgresAdapter::connect("postgres://user:pass@localhost/test", &config)?;
    /// let conn = adapter.get_connection()?;
    /// # Ok(())
    /// # }
    /// ```
    fn get_connection(&self) -> Result<Self::Connection> {
        self.pool.get().map_err(Into::into)
    }

    /// Runs database migrations in a serializable transaction
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{config::SecurityConfig, adapters::{DatabaseAdapter, postgres::PostgresAdapter}, Migration};
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let adapter = PostgresAdapter::connect("postgres://user:pass@localhost/test", &config)?;
    /// let migrations = vec![
    ///     Migration {
    ///         version: 1,
    ///         description: "Create users table".to_string(),
    ///         up: "CREATE TABLE users (id SERIAL PRIMARY KEY)".to_string(),
    ///         down: "DROP TABLE users".to_string(),
    ///     }
    /// ];
    /// adapter.run_migrations(migrations)?;
    /// # Ok(())
    /// # }
    /// ```
    fn run_migrations(&self, migrations: Vec<Migration>) -> Result<()> {
        use log::debug;
        let escaped_table = self.migration_table.replace("\"", "\"\"");
        let mut conn = self.pool.get()?;
        let mut tx = conn.transaction()?;

        tx.execute(
            &format!(
                "CREATE TABLE IF NOT EXISTS \"{}\" (\n                version BIGINT PRIMARY KEY,\n                description TEXT NOT NULL,\n                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n            )",
                escaped_table
            ),
            &[],
        )?;

        let mut sorted_migrations = migrations;
        sorted_migrations.sort_by_key(|m| m.version);

        for migration in sorted_migrations {
            let row = tx.query_opt(
                &format!("SELECT 1 FROM \"{}\" WHERE version = $1", escaped_table),
                &[&migration.version],
            )?;

            if row.is_some() {
                debug!("Skipping already applied migration: {}", migration.version);
                continue;
            }

            for statement in migration.up.split(';').filter(|s| !s.trim().is_empty()) {
                tx.execute(statement.trim(), &[])?;
            }

            tx.execute(
                &format!(
                    "INSERT INTO \"{}\" (version, description) VALUES ($1, $2)",
                    escaped_table
                ),
                &[&migration.version, &migration.description],
            )?;
        }

        tx.commit()?;
        Ok(())
    }

    fn release_connection(&self, _conn: Self::Connection) {
        // Connections are automatically returned to the pool when dropped
    }
}

#[cfg(test)]
mod tests {
    use crate::adapters::postgres::PostgresAdapter;
    use crate::Migration;
    use crate::{config::SecurityConfig, database::Database};
    use anyhow::Result;
    use log::debug;
    use postgres::{Client, NoTls};
    use std::path::Path;
    use std::sync::Arc;
    use tempfile;
    use uuid::Uuid;

    mod test_logger {
        use std::sync::Once;
        static INIT: Once = Once::new();

        pub fn setup() {
            INIT.call_once(|| {
                env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info"))
                    .try_init()
                    .ok();
            });
        }
    }

    fn get_connection_string(db_name: &str) -> String {
        format!(
            "postgres://{}:{}@{}:{}/{}",
            std::env::var("POSTGRES_USER").unwrap_or_else(|_| "postgres".to_string()),
            std::env::var("POSTGRES_PASSWORD").unwrap_or_else(|_| "postgres".to_string()),
            std::env::var("POSTGRES_HOST").unwrap_or_else(|_| "localhost".to_string()),
            std::env::var("POSTGRES_PORT").unwrap_or_else(|_| "5432".to_string()),
            db_name
        )
    }

    fn create_test_db() -> Result<String> {
        let admin_conn_str = get_connection_string("postgres");

        let db_name = format!("test_db_{}", Uuid::new_v4().to_string().replace("-", "_"));

        // Verify we can connect to the admin database first
        let mut client = match Client::connect(&admin_conn_str, NoTls) {
            Ok(c) => c,
            Err(e) => {
                eprintln!(
                    "Skipping PostgreSQL tests - could not connect to admin database: {}",
                    e
                );
                return Err(e.into());
            }
        };

        client.execute(&format!("CREATE DATABASE {};", db_name), &[])?;
        Ok(db_name)
    }

    fn cleanup_test_db(db_name: &str) -> Result<()> {
        let admin_conn_str = get_connection_string("postgres");
        let mut client = Client::connect(&admin_conn_str, NoTls)?;

        // More aggressive connection termination
        client.execute(
            &format!(
                "
                SELECT pg_terminate_backend(pg_stat_activity.pid) 
                FROM pg_stat_activity 
                WHERE pg_stat_activity.datname = '{}'
                AND pid <> pg_backend_pid();
            ",
                db_name
            ),
            &[],
        )?;

        // Retry logic for dropping database
        let mut attempts = 0;
        loop {
            let result = client.execute(&format!("DROP DATABASE IF EXISTS {}", db_name), &[]);
            match result {
                Ok(_) => break Ok(()),
                Err(_e) if attempts < 3 => {
                    attempts += 1;
                    std::thread::sleep(std::time::Duration::from_millis(200 * attempts as u64));
                }
                Err(e) => return Err(e.into()),
            }
        }
    }

    #[test]
    fn test_full_migration_cycle() -> Result<()> {
        test_logger::setup();
        let db_name = match create_test_db() {
            Ok(name) => name,
            Err(e) => {
                eprintln!("Skipping PostgreSQL test: {}", e);
                return Ok(());
            }
        };

        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            ..SecurityConfig::default()
        };

        let conn_str = get_connection_string(&db_name);

        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;
        let migrations = Migration::load_from_dir(Path::new("tests/test_migrations"))?;
        db.run_migrations(migrations)?;

        verify_migration_state(
            &conn_str,
            &config.migration_table,
            4,
            &["users", "posts", "comments"],
        )?;
        verify_table_columns(&db, "users", &["id", "name", "email", "created_at"])?;

        // Test idempotency
        let migrations = Migration::load_from_dir(Path::new("tests/test_migrations"))?;
        db.run_migrations(migrations)?;
        let mut conn = db.get_connection()?;
        let count: i64 = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", config.migration_table),
                &[],
            )?
            .get(0);
        assert_eq!(count, 4, "Migrations should not be reapplied");

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_migration_ordering() -> Result<()> {
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            ..SecurityConfig::default()
        };

        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;
        let migrations = Migration::load_from_dir(Path::new("tests/test_migrations"))?;
        db.run_migrations(migrations)?;

        let mut conn = db.get_connection()?;
        let rows = conn.query(
            &format!(
                "SELECT version FROM {} ORDER BY version",
                config.migration_table
            ),
            &[],
        )?;

        let versions: Vec<i64> = rows.iter().map(|row| row.get(0)).collect();
        assert_eq!(
            versions,
            vec![1, 2, 3, 4],
            "Migrations should be applied in order"
        );

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    // Similar verification functions but adapted for PostgreSQL
    fn verify_migration_state(
        conn_str: &str,
        migration_table: &str,
        expected_count: usize,
        expected_tables: &[&str],
    ) -> Result<()> {
        let mut client = Client::connect(conn_str, NoTls)?;

        // First check if migration table exists
        let table_exists: bool = client
            .query_one(
                "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = $1)",
                &[&migration_table],
            )?
            .get(0);

        if table_exists {
            // Verify migration count
            let count: i64 = client
                .query_one(&format!("SELECT COUNT(*) FROM {}", migration_table), &[])?
                .get(0);
            assert_eq!(
                count as usize, expected_count,
                "Unexpected number of applied migrations"
            );
        } else {
            assert_eq!(
                0, expected_count,
                "Migrations table missing but expected {} migrations",
                expected_count
            );
        }

        // Verify table existence
        for table in expected_tables {
            let exists: bool = client
                .query_one(
                    "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = $1)",
                    &[&table],
                )?
                .get(0);
            assert!(exists, "Table {} not found", table);
        }

        Ok(())
    }

    fn verify_table_columns(
        db: &Database<PostgresAdapter>,
        table_name: &str,
        expected_columns: &[&str],
    ) -> Result<()> {
        let mut conn = db.get_connection()?;
        let rows = conn.query(
            "SELECT column_name FROM information_schema.columns WHERE table_name = $1",
            &[&table_name],
        )?;

        let columns: Vec<String> = rows.iter().map(|row| row.get(0)).collect();

        for expected in expected_columns {
            assert!(
                columns.contains(&expected.to_string()),
                "Column {} not found in table {}",
                expected,
                table_name
            );
        }

        Ok(())
    }

    #[test]
    fn test_invalid_migration_files() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            ..SecurityConfig::default()
        };

        let conn_str = get_connection_string(&db_name);

        // Create temp dir with invalid migration files
        let temp_dir = tempfile::tempdir()?;
        let migrations_dir = temp_dir.path();

        // Invalid file name (no version number)
        std::fs::write(
            migrations_dir.join("invalid_migration.up.sql"),
            "CREATE TABLE invalid();",
        )?;

        let migrations = Migration::load_from_dir(migrations_dir);

        assert!(
            migrations.is_err(),
            "Should error on invalid migration files"
        );

        // Verify no migrations were applied
        verify_migration_state(&conn_str, &config.migration_table, 0, &[])?;

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_failed_sql_execution() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            ..SecurityConfig::default()
        };

        let conn_str = get_connection_string(&db_name);

        // Create temp dir with invalid SQL
        let temp_dir = tempfile::tempdir()?;
        let migrations_dir = temp_dir.path();

        std::fs::write(
            migrations_dir.join("001_invalid_sql.up.sql"),
            "CREATE TABLE valid(); INVALID SQL SYNTAX;",
        )?;

        let migrations = Migration::load_from_dir(migrations_dir);
        assert!(migrations.is_err(), "Should propagate SQL execution error");

        // Verify no migrations were applied
        verify_migration_state(&conn_str, &config.migration_table, 0, &[])?;

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_rollback_on_failure() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            ..SecurityConfig::default()
        };

        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

        // Create temp dir with 3 migrations where the 3rd will fail
        let temp_dir = tempfile::tempdir()?;
        let migrations_dir = temp_dir.path();

        // Valid migration 1
        std::fs::write(
            migrations_dir.join("001_valid1.up.sql"),
            "CREATE TABLE table1 (id SERIAL PRIMARY KEY);",
        )?;

        // Valid migration 2
        std::fs::write(
            migrations_dir.join("002_valid2.up.sql"),
            "CREATE TABLE table2 (id SERIAL PRIMARY KEY);",
        )?;

        // Invalid migration 3
        std::fs::write(
            migrations_dir.join("003_invalid.up.sql"),
            "CREATE TABLE table3 (id SERIAL PRIMARY KEY); INVALID SQL;",
        )?;

        let migrations = Migration::load_from_dir(migrations_dir);
        assert!(migrations.is_err(), "Should fail on third migration");

        // Verify no migrations were applied (transaction rolled back)
        verify_migration_state(&conn_str, &config.migration_table, 0, &[])?;

        let mut conn = db.get_connection()?;
        let exists: bool = conn
            .query_one(
                "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'table1')",
                &[],
            )?
            .get(0);
        assert!(!exists, "Table1 should not exist after rollback");

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_explicit_release_connection() -> anyhow::Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;

        {
            let config = SecurityConfig {
                migration_table: "test_migrations".to_string(),
                ..SecurityConfig::default()
            };
            let conn_str = get_connection_string(&db_name);
            let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

            // Obtain a connection and perform a dummy query
            let mut conn = db.get_connection()?;
            let row = conn.query_one("SELECT 1", &[])?;
            let value: i32 = row.get(0);
            assert_eq!(value, 1, "Initial query should return 1");

            // Explicitly drop DB instance before cleanup
            drop(db);
            // Add small delay to ensure connections are closed
            std::thread::sleep(std::time::Duration::from_millis(500));
        }

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_connection_pool_exhaustion() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            max_pool_size: 2,
            pool_min_idle: 2,
            connection_timeout: std::time::Duration::from_secs(1),
            ..SecurityConfig::default()
        };
        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

        // Acquire max allowed connections
        let conn1 = db.get_connection()?;
        let conn2 = db.get_connection()?;

        // Attempt to get third connection
        let result = db.get_connection();
        if let Err(e) = result {
            assert!(
                e.to_string().contains("timed out waiting for connection"),
                "Should return connection timeout error, got: {}",
                e
            );
        } else {
            panic!("Expected connection pool exhaustion error");
        }

        // Release one connection and try again
        drop(conn1);
        let conn3 = db.get_connection()?;
        drop(conn2);
        drop(conn3);

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_connection_reuse() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            max_pool_size: 2,
            pool_min_idle: 1,
            ..SecurityConfig::default()
        };
        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

        // Get and release connection
        let conn = db.get_connection()?;
        drop(conn);

        // Get connection again
        let _conn2 = db.get_connection()?;

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_pool_growth() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "test_migrations".to_string(),
            max_pool_size: 5,
            pool_min_idle: 2,
            ..SecurityConfig::default()
        };
        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

        let mut connections = Vec::new();
        for _ in 0..5 {
            connections.push(db.get_connection()?);
        }

        // Releasing all connections should allow pool to grow to max size
        for conn in connections {
            drop(conn);
        }

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_migration_table_escaping() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "strange name!".to_string(),
            ..SecurityConfig::default()
        };
        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

        let migrations = vec![Migration {
            version: 1,
            description: "Test".to_string(),
            up: "CREATE TABLE test (id SERIAL PRIMARY KEY)".to_string(),
            down: "DROP TABLE test".to_string(),
        }];

        db.run_migrations(migrations)?;

        // Verify migration table exists with escaped name
        let mut conn = db.get_connection()?;
        let exists: bool = conn
            .query_one(
                "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = $1)",
                &[&config.migration_table],
            )?
            .get(0);
        assert!(exists, "Migration table with special name should exist");

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_empty_migration() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig::default();
        let conn_str = get_connection_string(&db_name);

        {
            let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;
            let migrations = vec![Migration {
                version: 1,
                description: "Empty".to_string(),
                up: "-- No operation".to_string(),
                down: "-- No operation".to_string(),
            }];

            db.run_migrations(migrations)?;

            // Verify no tables created
            let mut conn = db.get_connection()?;
            let rows = conn.query(
                "SELECT table_name FROM information_schema.tables 
                 WHERE table_schema = 'public' 
                 AND table_name != $1",
                &[&config.migration_table],
            )?;
            assert_eq!(
                rows.len(),
                0,
                "No tables should be created by empty migration"
            );
        } // Database instance drops here

        // Add more robust cleanup wait
        std::thread::sleep(std::time::Duration::from_millis(500));
        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_concurrent_migrations() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig::default();
        let conn_str = get_connection_string(&db_name);
        let db = Arc::new(Database::<PostgresAdapter>::new(&conn_str, &config)?);

        let migrations = vec![Migration {
            version: 1,
            description: "Concurrent test".to_string(),
            up: "CREATE TABLE concurrent (id SERIAL PRIMARY KEY)".to_string(),
            down: "DROP TABLE concurrent".to_string(),
        }];

        let handles: Vec<_> = (0..4)
            .map(|_| {
                let db = db.clone();
                let migrations = migrations.clone();
                std::thread::spawn(move || match db.run_migrations(migrations.clone()) {
                    Ok(_) => Ok(()),
                    Err(e) if e.to_string().contains("could not serialize access") => Ok(()),
                    Err(e) => Err(e),
                })
            })
            .collect();

        for handle in handles {
            handle.join().unwrap()?;
        }

        let mut conn = db.get_connection()?;
        let count: i64 = conn
            .query_one(
                &format!("SELECT COUNT(*) FROM {}", config.migration_table),
                &[],
            )?
            .get(0);
        assert_eq!(count, 1, "Migration should be applied exactly once");

        let table_count: i64 = conn
            .query_one(
                "SELECT COUNT(*) FROM information_schema.tables 
             WHERE table_schema = 'public' AND table_name = 'concurrent'",
                &[],
            )?
            .get(0);
        assert_eq!(table_count, 1, "Table should exist after migration");

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_stale_connection_handling() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            max_pool_size: 2,
            pool_min_idle: 1,
            ..SecurityConfig::default()
        };
        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

        // Get and kill connections
        let mut conn = db.get_connection()?;
        let kill_result = conn.execute("SELECT pg_terminate_backend(pg_backend_pid())", &[]);

        // Connection should be invalid after termination
        assert!(
            kill_result.is_err(),
            "Should error after killing connection: {:?}",
            kill_result
        );

        // Let the dead connection drop out of scope without returning to pool
        drop(conn);

        // Should get fresh connection from pool
        let mut new_conn = db.get_connection()?;
        debug!(
            "Acquired new connection, is_closed: {}",
            new_conn.is_closed()
        );
        new_conn.execute("SELECT 1", &[])?;
        debug!("Successfully executed query on new connection");

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_error_message_propagation() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig::default();
        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

        let result = db.get_connection()?.execute("INVALID SQL SYNTAX", &[]);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("syntax error"));

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_unicode_handling() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig::default();
        let conn_str = get_connection_string(&db_name);
        let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;

        let migrations = vec![Migration {
            version: 1,
            description: "测试 Unicode 😀".to_string(),
            up: "CREATE TABLE 测试 (id SERIAL PRIMARY KEY)".to_string(),
            down: "DROP TABLE 测试".to_string(),
        }];

        db.run_migrations(migrations)?;

        let mut conn = db.get_connection()?;
        conn.execute("INSERT INTO 测试 DEFAULT VALUES", &[])?;

        let count: i64 = conn.query_one("SELECT COUNT(*) FROM 测试", &[])?.get(0);
        assert_eq!(count, 1);

        cleanup_test_db(&db_name)?;
        Ok(())
    }

    #[test]
    fn test_case_sensitive_handling() -> Result<()> {
        test_logger::setup();
        let db_name = create_test_db()?;
        let config = SecurityConfig {
            migration_table: "MigrationTable".to_string(),
            ..SecurityConfig::default()
        };
        let conn_str = get_connection_string(&db_name);

        {
            let db = Database::<PostgresAdapter>::new(&conn_str, &config)?;
            let migrations = vec![Migration {
                version: 1,
                description: "Case sensitive test".to_string(),
                up: "CREATE TABLE \"MixedCase\" (id SERIAL PRIMARY KEY)".to_string(),
                down: "DROP TABLE \"MixedCase\"".to_string(),
            }];

            db.run_migrations(migrations)?;

            let mut conn = db.get_connection()?;
            let exists: bool = conn
                .query_one(
                    "SELECT EXISTS (SELECT 1 FROM information_schema.tables 
                 WHERE table_name = 'MixedCase')",
                    &[],
                )?
                .get(0);
            assert!(exists, "Should preserve case sensitivity");
        } // Database instance drops here, closing all connections

        // Add small delay to ensure connections are closed
        std::thread::sleep(std::time::Duration::from_millis(500));
        cleanup_test_db(&db_name)?;
        Ok(())
    }
}
