//! Database adapter trait for abstracting database operations
//!
//! This trait defines the common interface that all database adapters must implement.
//! It provides methods for:
//! - Establishing connection pools
//! - Managing migrations
//! - Handling database connections

use crate::{config::SecurityConfig, Migration};

/// Database adapter trait for abstracting database operations
///
/// This trait defines the common interface that all database adapters must implement.
/// It provides methods for establishing connection pools, managing migrations, and handling
/// database connections.
///
/// # Examples
///
/// ```no_run
/// # use db::{adapters::{DatabaseAdapter, sqlite::SQLiteAdapter}, config::SecurityConfig, database::Database};
/// # use std::time::Duration;
/// # fn main() -> anyhow::Result<()> {
/// let config = SecurityConfig {
///     migration_table: "migrations".to_string(),
///     max_pool_size: 10,
///     pool_min_idle: 1,
///     pool_max_lifetime: Duration::from_secs(3600),
///     connection_timeout: Duration::from_secs(30),
///     max_query_depth: 10,
///     max_page_size: 100,
///     max_string_length: 1000,
///     cursor_expiry: Duration::from_secs(3600),
///     max_array_size: 1000,
///     hmac_secret: "test_secret".to_string().into(),
/// };
///
/// let db = Database::<SQLiteAdapter>::new("file:db.sqlite3", &config)?;
/// # Ok(())
/// # }
/// ```
pub trait DatabaseAdapter {
    /// The connection type that this adapter uses. Must implement Send for thread safety.
    type Connection: Send;

    /// Creates a new connection pool and validates migration table name
    ///
    /// # Arguments
    /// * `database_url` - Database connection string
    /// * `config` - Security configuration parameters
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{adapters::{DatabaseAdapter, mysql::MySQLAdapter}, config::SecurityConfig};
    /// # fn main() -> anyhow::Result<()> {
    /// let config = SecurityConfig::default();
    /// let adapter = MySQLAdapter::connect("mysql://user:pass@localhost/db", &config)?;
    /// # Ok(())
    /// # }
    /// ```
    ///
    /// # Errors
    /// Returns error if connection fails or migration table name is invalid
    fn connect(database_url: &str, config: &SecurityConfig) -> anyhow::Result<Self>
    where
        Self: Sized;

    /// Retrieves a connection from the pool
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{adapters::{DatabaseAdapter, sqlite::SQLiteAdapter}, config::SecurityConfig, database::Database};
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let db = Database::<SQLiteAdapter>::new("file::memory:", &config)?;
    /// let conn = db.get_connection()?;
    /// # Ok(())
    /// # }
    /// ```
    ///
    /// # Errors
    /// Returns error if no connections available in the pool
    fn get_connection(&self) -> anyhow::Result<Self::Connection>;

    /// Explicitly releases a connection back to the pool.
    ///
    /// While not strictly necessary since dropping the connection will automatically return it to the pool,
    /// using this method makes the intention explicit and helps prevent connection leaks.
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{adapters::{DatabaseAdapter, sqlite::SQLiteAdapter}, config::SecurityConfig, database::Database};
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let db = Database::<SQLiteAdapter>::new("file::memory:", &config)?;
    /// let conn = db.get_connection()?;
    /// db.adapter.release_connection(conn);
    /// # Ok(())
    /// # }
    /// ```
    fn release_connection(&self, conn: Self::Connection) {
        drop(conn);
    }

    /// Applies migrations from directory to database
    ///
    /// # Arguments
    /// * `migrations` - Vector of migrations to apply
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use db::{adapters::{DatabaseAdapter, sqlite::SQLiteAdapter}, config::SecurityConfig, database::Database, Migration};
    /// # use std::path::Path;
    /// # fn main() -> anyhow::Result<()> {
    /// # let config = SecurityConfig::default();
    /// # let db = Database::<SQLiteAdapter>::new("file::memory:", &config)?;
    /// let migrations = Migration::load_from_dir(Path::new("migrations"))?;
    /// db.run_migrations(migrations)?;
    /// # Ok(())
    /// # }
    /// ```
    ///
    /// # Features
    /// - Transactions: Runs all migrations in a single transaction
    /// - Idempotency: Skips already applied migrations
    /// - Validation: Checks migration file naming and structure
    ///
    /// # Errors
    /// Returns error if migrations fail to apply
    fn run_migrations(&self, migrations: Vec<Migration>) -> anyhow::Result<()>;
}
