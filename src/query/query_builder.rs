//! Query builder for translating Query structures into SQL
//!
//! This module contains the QueryBuilder which translates the dialect-agnostic Query
//! structure into SQL for a specific database dialect. It also defines the Dialect
//! trait for dialect-specific behavior.
//!
//! # Overview
//!
//! The query builder takes a [Query] structure and converts it into SQL for a specific
//! database dialect. It handles:
//! - SELECT statements with field selection and aliases
//! - JOINs (INNER, LEFT, and many-to-many)
//! - WHERE clauses with complex filtering
//! - GROUP BY and ORDER BY clauses
//! - Pagination (both offset and cursor-based)
//! - Aggregation operations
//!
//! # Example
//!
//! ```rust
//! use db::query_builder::{QueryBuilder, SQLiteDialect};
//! use db::models::Query;
//!
//! fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     let query = Query {
//!         collection: "users".to_string(),
//!         filters: None,
//!         selection: None,
//!         ordering: None,
//!         pagination: None,
//!         joins: None,
//!         aggregations: None,
//!         extensions: None,
//!     };
//!     let dialect = SQLiteDialect {};
//!     let builder = QueryBuilder::new(&query, &dialect);
//!     let (sql, params) = builder.build()?;
//!     Ok(())
//! }
//! ```

use crate::{
    AggregationStage, Filter, Join, JoinCondition, Number, OrderDirection, Pagination,
    PaginationDirection, Query, Select, SqlError, Value,
};
use log::{debug, error, trace, warn};

/// Trait defining SQL dialect-specific behavior
///
/// Implement this trait to add support for new SQL dialects. The trait provides
/// methods for handling dialect-specific features like:
/// - Parameter placeholders
/// - Identifier quoting
/// - Pagination syntax
pub trait Dialect {
    /// Returns the prefix for parameter placeholders
    ///
    /// For example:
    /// - SQLite: "?"
    /// - PostgreSQL: "$"
    fn placeholder_prefix(&self) -> &'static str;

    /// Generates a parameter placeholder for the given index
    ///
    /// # Arguments
    /// * `index` - The parameter index (1-based)
    fn placeholder(&self, index: usize) -> String;

    /// Quotes an identifier according to the dialect's rules
    ///
    /// # Arguments
    /// * `identifier` - The identifier to quote
    fn quote_identifier(&self, identifier: &str) -> String;

    /// Returns the keyword for LIMIT clause
    fn limit_keyword(&self) -> &'static str {
        "LIMIT"
    }

    /// Returns the keyword for OFFSET clause
    fn offset_keyword(&self) -> &'static str {
        "OFFSET"
    }
}

/// SQLite dialect implementation
///
/// Implements the Dialect trait for SQLite-specific SQL syntax
pub struct SQLiteDialect {}

impl Dialect for SQLiteDialect {
    fn placeholder_prefix(&self) -> &'static str {
        "?"
    }

    fn placeholder(&self, _index: usize) -> String {
        self.placeholder_prefix().to_string()
    }

    fn quote_identifier(&self, identifier: &str) -> String {
        format!("\"{}\"", identifier.replace('"', "\"\""))
    }
}

/// PostgreSQL dialect implementation
///
/// Implements the Dialect trait for PostgreSQL-specific SQL syntax
pub struct PostgresDialect {}

impl Dialect for PostgresDialect {
    fn placeholder_prefix(&self) -> &'static str {
        "$"
    }

    fn placeholder(&self, index: usize) -> String {
        format!("{}{}", self.placeholder_prefix(), index)
    }

    fn quote_identifier(&self, identifier: &str) -> String {
        format!("\"{}\"", identifier.replace('"', "\"\""))
    }
}

/// MySQL dialect implementation
///
/// Implements the Dialect trait for MySQL-specific SQL syntax
pub struct MySQLDialect {}

impl Dialect for MySQLDialect {
    fn placeholder_prefix(&self) -> &'static str {
        "?"
    }

    fn placeholder(&self, _index: usize) -> String {
        self.placeholder_prefix().to_string()
    }

    fn quote_identifier(&self, identifier: &str) -> String {
        format!("`{}`", identifier.replace('`', "``"))
    }
}

/// SQL query construction from domain models
///
/// Translates the [Query] structure into database-specific SQL through:
/// - Parameterized query generation
/// - Dialect-specific SQL variations
/// - Security-conscious value handling
///
/// # Guarantees
/// - Type-safe parameter binding
/// - SQL injection prevention
/// - Query validation before execution
pub struct QueryBuilder<'a> {
    /// The query being built
    query: &'a Query,
    /// The dialect to use for SQL generation
    dialect: &'a dyn Dialect,
    /// Collected parameter values
    params: Vec<Value>,
    /// SQL fragments being assembled
    sql_parts: Vec<String>,
    /// Parameter counter for unique placeholders
    param_counter: usize,
}

impl<'a> QueryBuilder<'a> {
    /// Creates a new QueryBuilder instance
    ///
    /// # Arguments
    /// * `query` - The query structure to build from
    /// * `dialect` - Database dialect to use for SQL generation
    pub fn new(query: &'a Query, dialect: &'a dyn Dialect) -> Self {
        Self {
            query,
            dialect,
            params: Vec::new(),
            sql_parts: Vec::new(),
            param_counter: 1,
        }
    }

    /// Constructs the final SQL query and parameters
    ///
    /// # Steps
    /// 1. SELECT clause construction
    /// 2. JOIN handling
    /// 3. WHERE clause generation
    /// 4. ORDER BY sorting
    /// 5. Pagination (LIMIT/OFFSET or cursor)
    ///
    /// # Returns
    /// Tuple of (SQL String, Parameter Values)
    pub fn build(mut self) -> Result<(String, Vec<Value>), SqlError> {
        // Reset state for new query building, in case of multiple builds
        self.sql_parts.clear();
        self.param_counter = 1;
        self.params.clear();

        debug!(
            "Building SQL query for collection: {}",
            self.query.collection
        );
        self.build_select()?;
        self.build_from()?;
        self.build_joins()?;
        self.build_where()?;
        self.build_group_by()?;
        self.build_order_by()?;
        self.build_pagination()?;

        trace!("Final SQL parts: {:?}", self.sql_parts);
        debug!("Query built with {} parameters", self.params.len());
        Ok((self.sql_parts.join(" "), self.params))
    }

    fn build_select(&mut self) -> Result<(), SqlError> {
        let mut selections = Vec::new();

        // Start with explicit selections from query
        if let Some(query_selections) = &self.query.selection {
            selections.extend(query_selections.iter().cloned());
        }

        // Add GROUP BY fields and aggregations if they exist
        if let Some(aggregations) = &self.query.aggregations {
            for stage in aggregations {
                if let AggregationStage::Group { by, fields } = stage {
                    // Add group by columns as explicit fields
                    for column in by {
                        selections.push(Select::Field {
                            name: column.clone(),
                            alias: None,
                        });
                    }
                    // Add aggregation fields
                    selections.extend(fields.iter().cloned());
                }
            }
        }

        let mut parts = Vec::new();
        for select in &selections {
            let expr = self.process_select(select)?;
            parts.push(expr);
        }

        if parts.is_empty() {
            parts.push("*".to_string());
        }

        self.sql_parts.push(format!("SELECT {}", parts.join(", ")));
        Ok(())
    }

    fn process_select(&mut self, select: &Select) -> Result<String, SqlError> {
        match select {
            Select::Field { name, alias } => {
                let quoted = self.dialect.quote_identifier(name);
                Ok(if let Some(alias) = alias {
                    format!("{} AS {}", quoted, self.dialect.quote_identifier(alias))
                } else {
                    quoted
                })
            }
            Select::Count { field, alias } => {
                let quoted = self.dialect.quote_identifier(field);
                let expr = format!("COUNT({})", quoted);
                Ok(if let Some(alias) = alias {
                    format!("{} AS {}", expr, self.dialect.quote_identifier(alias))
                } else {
                    expr
                })
            }
            Select::Sum { field, alias } => {
                let quoted = self.dialect.quote_identifier(field);
                let expr = format!("SUM({})", quoted);
                Ok(if let Some(alias) = alias {
                    format!("{} AS {}", expr, self.dialect.quote_identifier(alias))
                } else {
                    expr
                })
            }
            Select::Avg { field, alias } => {
                let quoted = self.dialect.quote_identifier(field);
                let expr = format!("AVG({})", quoted);
                Ok(if let Some(alias) = alias {
                    format!("{} AS {}", expr, self.dialect.quote_identifier(alias))
                } else {
                    expr
                })
            }
            Select::Raw {
                dialect,
                expression,
                alias,
            } => {
                if !dialect.starts_with(self.dialect.placeholder_prefix()) {
                    return Err(SqlError::UnsupportedOperation(
                        "Raw expression dialect mismatch".to_string(),
                    ));
                }
                Ok(if let Some(alias) = alias {
                    format!("{} AS {}", expression, self.dialect.quote_identifier(alias))
                } else {
                    expression.clone()
                })
            }
            Select::Project { expression, alias } => Ok(if let Some(alias) = alias {
                format!("{} AS {}", expression, self.dialect.quote_identifier(alias))
            } else {
                expression.clone()
            }),
        }
    }

    fn build_from(&mut self) -> Result<(), SqlError> {
        let from = self.dialect.quote_identifier(&self.query.collection);
        self.sql_parts.push(format!("FROM {}", from));
        Ok(())
    }

    fn build_joins(&mut self) -> Result<(), SqlError> {
        if let Some(joins) = &self.query.joins {
            for join in joins {
                let clause = self.process_join(join)?;
                self.sql_parts.push(clause);
            }
        }
        Ok(())
    }

    fn process_join(&mut self, join: &Join) -> Result<String, SqlError> {
        match join {
            Join::Inner {
                collection,
                condition,
            } => {
                let table = self.dialect.quote_identifier(collection);
                let condition_clause = match self.process_join_condition(condition)? {
                    s if s.starts_with("USING") || s.starts_with("ON") => s,
                    s => format!("ON {}", s),
                };
                Ok(format!("INNER JOIN {} {}", table, condition_clause))
            }
            Join::Left {
                collection,
                condition,
            } => {
                let table = self.dialect.quote_identifier(collection);
                let condition_clause = match self.process_join_condition(condition)? {
                    s if s.starts_with("USING") => s,
                    s => format!("ON {}", s),
                };
                Ok(format!("LEFT JOIN {} {}", table, condition_clause))
            }
            Join::ManyToMany {
                target,
                through,
                from_key,
                to_key,
                main_id,
                target_id,
            } => {
                warn!("Many-to-many joins may impact performance, consider optimizing");
                let through_table = self.dialect.quote_identifier(through);
                let target_table = self.dialect.quote_identifier(target);

                Ok(format!(
                    "INNER JOIN {through} ON {main}.{from} = {through}.{from_key} \
                     INNER JOIN {target} ON {through}.{to_key} = {target}.{to}",
                    through = through_table,
                    target = target_table,
                    main = self.dialect.quote_identifier(&self.query.collection),
                    from = self.dialect.quote_identifier(main_id),
                    from_key = self.dialect.quote_identifier(from_key),
                    to_key = self.dialect.quote_identifier(to_key),
                    to = self.dialect.quote_identifier(target_id)
                ))
            }
        }
    }

    fn process_join_condition(&self, condition: &JoinCondition) -> Result<String, SqlError> {
        match condition {
            JoinCondition::On { left, right } => {
                // Split qualified names and quote each component
                let quote_components = |s: &str| {
                    s.split('.')
                        .map(|part| self.dialect.quote_identifier(part))
                        .collect::<Vec<_>>()
                        .join(".")
                };

                let left = quote_components(left);
                let right = quote_components(right);
                Ok(format!("{} = {}", left, right))
            }
            JoinCondition::Using { field } => {
                let field = self.dialect.quote_identifier(field);
                Ok(format!("USING ({})", field))
            }
            JoinCondition::Raw { expression } => Ok(expression.clone()),
        }
    }

    fn build_where(&mut self) -> Result<(), SqlError> {
        let mut all_filters = Vec::new();

        // Add main query filters (using cloned() to get owned copies)
        if let Some(filters) = &self.query.filters {
            all_filters.extend(filters.iter().cloned());
        }

        // Add aggregation match stages (using cloned() to get owned copies)
        if let Some(aggregations) = &self.query.aggregations {
            for stage in aggregations {
                if let AggregationStage::Match { filters } = stage {
                    all_filters.extend(filters.iter().cloned());
                }
            }
        }

        if !all_filters.is_empty() {
            let where_clause = self.process_filters(&all_filters)?;
            self.sql_parts.push(format!("WHERE {}", where_clause));
        }
        Ok(())
    }

    fn process_filters(&mut self, filters: &[Filter]) -> Result<String, SqlError> {
        let mut conditions = Vec::new();
        for filter in filters {
            conditions.push(self.process_filter(filter)?);
        }
        Ok(conditions.join(" AND "))
    }

    fn process_filter(&mut self, filter: &Filter) -> Result<String, SqlError> {
        trace!("Processing filter: {:?}", filter);
        match filter {
            Filter::Equal { field, value } => {
                let ph = self.next_placeholder();
                self.params.push(value.clone());
                Ok(format!("{} = {}", self.dialect.quote_identifier(field), ph))
            }
            Filter::And { conditions } => {
                let sub: Result<Vec<_>, _> =
                    conditions.iter().map(|c| self.process_filter(c)).collect();
                Ok(format!("({})", sub?.join(" AND ")))
            }
            Filter::Or { conditions } => {
                let sub: Result<Vec<_>, _> =
                    conditions.iter().map(|c| self.process_filter(c)).collect();
                Ok(format!("({})", sub?.join(" OR ")))
            }
            Filter::In { field, values } => {
                let phs: Vec<String> = (0..values.len()).map(|_| self.next_placeholder()).collect();
                for v in values {
                    self.params.push(v.clone());
                }
                Ok(format!(
                    "{} IN ({})",
                    self.dialect.quote_identifier(field),
                    phs.join(", ")
                ))
            }
            Filter::Raw {
                dialect,
                expression,
            } => {
                if !dialect.starts_with(self.dialect.placeholder_prefix()) {
                    return Err(SqlError::UnsupportedOperation(
                        "Raw filter dialect mismatch".to_string(),
                    ));
                }
                if let Value::String(s) = expression {
                    Ok(s.clone())
                } else {
                    Err(SqlError::Other(
                        "Raw filter expression must be a string value".to_string(),
                    ))
                }
            }
            Filter::NotEqual { field, value } => {
                let ph = self.next_placeholder();
                self.params.push(value.clone());
                Ok(format!(
                    "{} != {}",
                    self.dialect.quote_identifier(field),
                    ph
                ))
            }
            Filter::GreaterThan { field, value } => {
                let ph = self.next_placeholder();
                self.params.push(value.clone());
                Ok(format!("{} > {}", self.dialect.quote_identifier(field), ph))
            }
            Filter::LessThan { field, value } => {
                let ph = self.next_placeholder();
                self.params.push(value.clone());
                Ok(format!("{} < {}", self.dialect.quote_identifier(field), ph))
            }
            Filter::Contains { field, value } => {
                let ph = self.next_placeholder();
                // Extract raw string value and escape LIKE special characters
                let raw_value = match value {
                    Value::String(s) => s.clone(),
                    _ => value.to_string(),
                };
                // Escape SQL LIKE special characters (% and _)
                let escaped = raw_value.replace('%', r"\%").replace('_', r"\_");
                let pattern = format!("%{}%", escaped);
                self.params.push(Value::String(pattern));
                Ok(format!(
                    "{} LIKE {}",
                    self.dialect.quote_identifier(field),
                    ph
                ))
            }
            Filter::Exists { field } => Ok(format!(
                "{} IS NOT NULL",
                self.dialect.quote_identifier(field)
            )),
            Filter::ElemMatch { field, conditions } => {
                let sub_conditions = self.process_filters(conditions)?;
                Ok(format!(
                    "EXISTS (SELECT 1 FROM UNNEST({}) AS elem WHERE {})",
                    self.dialect.quote_identifier(field),
                    sub_conditions
                ))
            }
            Filter::TextSearch { fields, query } => {
                let ts_vector = fields
                    .iter()
                    .map(|f| self.dialect.quote_identifier(f))
                    .collect::<Vec<_>>()
                    .join(" || ' ' || ");

                self.params.push(Value::String(query.clone()));
                let placeholder = self.next_placeholder();

                match self.dialect.placeholder_prefix() {
                    "$" => Ok(format!(
                        "to_tsvector('english', {}) @@ to_tsquery('english', {})",
                        ts_vector, placeholder
                    )),
                    _ => Ok(format!("CONCAT({}) LIKE {}", ts_vector, placeholder)),
                }
            }
        }
    }

    fn build_group_by(&mut self) -> Result<(), SqlError> {
        if let Some(aggregations) = &self.query.aggregations {
            for stage in aggregations {
                if let AggregationStage::Group { by, .. } = stage {
                    let columns: Vec<String> = by
                        .iter()
                        .map(|col| self.dialect.quote_identifier(col))
                        .collect();
                    self.sql_parts
                        .push(format!("GROUP BY {}", columns.join(", ")));
                    break;
                }
            }
        }
        Ok(())
    }

    fn build_order_by(&mut self) -> Result<(), SqlError> {
        let mut all_ordering = Vec::new();

        if let Some(ordering) = &self.query.ordering {
            all_ordering.extend(ordering);
        }

        if let Some(aggregations) = &self.query.aggregations {
            for stage in aggregations {
                if let AggregationStage::Sort { fields } = stage {
                    all_ordering.extend(fields);
                }
            }
        }

        if !all_ordering.is_empty() {
            let mut clauses = Vec::new();
            for order in all_ordering {
                // Split and quote each component of the field
                let field = order
                    .field
                    .split('.')
                    .map(|part| self.dialect.quote_identifier(part))
                    .collect::<Vec<_>>()
                    .join(".");
                let dir = match order.direction {
                    OrderDirection::Asc => "ASC",
                    OrderDirection::Desc => "DESC",
                };
                clauses.push(format!("{} {}", field, dir));
            }
            self.sql_parts
                .push(format!("ORDER BY {}", clauses.join(", ")));
        }
        Ok(())
    }

    fn build_pagination(&mut self) -> Result<(), SqlError> {
        // Process main query pagination first
        if let Some(pagination) = &self.query.pagination {
            self.process_pagination(pagination)?;
        }

        // Process aggregation limit stages (last one wins)
        if let Some(aggregations) = &self.query.aggregations {
            if let Some(last_limit) = aggregations.iter().rev().find_map(|stage| {
                if let AggregationStage::Limit(limit) = stage {
                    Some(limit)
                } else {
                    None
                }
            }) {
                // Last Limit stage overrides any previous limit
                let pagination = Pagination::Offset {
                    limit: *last_limit,
                    offset: None,
                };
                self.process_pagination(&pagination)?;
            }
        }

        Ok(())
    }

    fn process_pagination(&mut self, pagination: &Pagination) -> Result<(), SqlError> {
        match pagination {
            Pagination::Offset { limit, offset } => {
                let limit_ph = self.next_placeholder();
                self.sql_parts
                    .push(format!("{} {}", self.dialect.limit_keyword(), limit_ph));
                self.params.push(Value::Number(Number::Unsigned(*limit)));

                if let Some(offset) = offset {
                    let offset_ph = self.next_placeholder();
                    self.sql_parts
                        .push(format!("{} {}", self.dialect.offset_keyword(), offset_ph));
                    self.params.push(Value::Number(Number::Unsigned(*offset)));
                }
                Ok(())
            }
            Pagination::Cursor {
                cursor,
                limit,
                direction,
            } => {
                // Handle empty cursor for initial request
                if cursor.is_none() {
                    // For initial request without cursor
                    self.params.push(Value::Number(Number::Unsigned(*limit)));
                    let limit_ph = self.next_placeholder();
                    self.sql_parts
                        .push(format!("{} {}", self.dialect.limit_keyword(), limit_ph));
                    return Ok(());
                }

                // Existing cursor validation
                if cursor.as_ref().unwrap().is_empty() {
                    return Err(SqlError::ValidationError(
                        "Cursor value cannot be empty".to_string(),
                    ));
                }

                // Collect all ordering fields from query and aggregations
                let mut all_ordering = Vec::new();
                if let Some(ordering) = &self.query.ordering {
                    all_ordering.extend(ordering.clone()); // Cloned here to avoid borrow issues later
                }
                if let Some(aggregations) = &self.query.aggregations {
                    for stage in aggregations {
                        if let AggregationStage::Sort { fields } = stage {
                            all_ordering.extend(fields.clone()); // Cloned here too
                        }
                    }
                }

                if all_ordering.is_empty() {
                    error!("Cursor pagination requires at least one ordering field");
                    return Err(SqlError::UnsupportedOperation(
                        "Cursor pagination requires at least one ordering field".to_string(),
                    ));
                }

                // Handle composite cursor pagination (not yet supported)
                if all_ordering.len() > 1 {
                    return Err(SqlError::UnsupportedOperation(
                        "Composite cursor pagination not implemented".to_string(),
                    ));
                }

                // Get the first OrderBy from the original ordering
                let first_order = &all_ordering[0];

                // Split qualified field names and quote each component
                let quote_qualified_field = |field: &str| {
                    field
                        .split('.')
                        .map(|part| self.dialect.quote_identifier(part))
                        .collect::<Vec<_>>()
                        .join(".")
                };

                let quoted_field = quote_qualified_field(&first_order.field);

                // Determine the operator based on the original direction and pagination direction
                let operator = match (first_order.direction.clone(), direction) {
                    (OrderDirection::Asc, PaginationDirection::Next) => ">",
                    (OrderDirection::Asc, PaginationDirection::Previous) => "<",
                    (OrderDirection::Desc, PaginationDirection::Next) => "<",
                    (OrderDirection::Desc, PaginationDirection::Previous) => ">",
                };

                // Add the cursor value as a parameter
                self.params
                    .push(Value::String(cursor.as_ref().unwrap().clone()));
                let placeholder = self.next_placeholder();

                let condition_str = format!("{} {} {}", quoted_field, operator, placeholder);

                // Find the existing WHERE clause and append the condition
                let where_pos = self.sql_parts.iter().position(|s| s.starts_with("WHERE "));

                if let Some(pos) = where_pos {
                    let existing_where = self.sql_parts.remove(pos);
                    let new_where = format!("{} AND {}", existing_where, condition_str);
                    self.sql_parts.insert(pos, new_where);
                } else {
                    // Insert the WHERE clause before ORDER BY
                    let order_by_pos = self
                        .sql_parts
                        .iter()
                        .position(|s| s.starts_with("ORDER BY"));

                    if let Some(pos) = order_by_pos {
                        self.sql_parts
                            .insert(pos, format!("WHERE {}", condition_str));
                    } else {
                        self.sql_parts.push(format!("WHERE {}", condition_str));
                    }
                }

                // For Previous direction, reverse the ORDER BY clause
                if direction == &PaginationDirection::Previous {
                    // Remove existing ORDER BY if present
                    let order_by_pos = self
                        .sql_parts
                        .iter()
                        .position(|s| s.starts_with("ORDER BY"));
                    if let Some(pos) = order_by_pos {
                        self.sql_parts.remove(pos);
                    }

                    let mut clauses = Vec::new();
                    for order in &all_ordering {
                        let field = order
                            .field
                            .split('.')
                            .map(|part| self.dialect.quote_identifier(part))
                            .collect::<Vec<_>>()
                            .join(".");
                        // Reverse the direction for Previous pagination
                        let dir = match order.direction {
                            OrderDirection::Asc => "DESC",
                            OrderDirection::Desc => "ASC",
                        };
                        clauses.push(format!("{} {}", field, dir));
                    }
                    self.sql_parts
                        .push(format!("ORDER BY {}", clauses.join(", ")));
                }

                // Add the LIMIT clause
                self.params.push(Value::Number(Number::Unsigned(*limit)));
                let limit_ph = self.next_placeholder();
                self.sql_parts
                    .push(format!("{} {}", self.dialect.limit_keyword(), limit_ph));

                Ok(())
            }
        }
    }

    fn next_placeholder(&mut self) -> String {
        let current = self.param_counter;
        self.param_counter += 1;
        self.dialect.placeholder(current)
    }

    /// Builds a COUNT query that matches the same conditions as the main query but without ORDER BY and pagination clauses.
    ///
    /// # Returns
    /// SQL fragments for the FROM and WHERE clauses of the COUNT query
    pub fn build_count(&mut self) -> Result<(String, Vec<Value>), SqlError> {
        debug!(
            "Building COUNT query for collection: {}",
            self.query.collection
        );

        // Reset state for new query building
        self.sql_parts.clear();
        self.param_counter = 1;
        self.params.clear();

        self.sql_parts.push("SELECT COUNT(*) AS total".to_string());

        // Build the FROM clause
        self.build_from()?;

        // Build JOINs if present
        self.build_joins()?;

        // Build WHERE clause
        self.build_where()?;

        // Build GROUP BY if present (needed for accurate counts with grouping)
        self.build_group_by()?;

        trace!("Final COUNT SQL parts: {:?}", self.sql_parts);
        debug!("COUNT query built with {} parameters", self.params.len());

        Ok((self.sql_parts.join(" "), self.params.clone()))
    }
}

#[cfg(test)]
mod tests {
    use super::{MySQLDialect, PostgresDialect, Query, QueryBuilder, SQLiteDialect, SqlError};

    use super::{
        AggregationStage, Filter, Join, JoinCondition, Number, OrderDirection, Pagination,
        PaginationDirection, Select, Value,
    };

    use proptest::prelude::*;

    use crate::{test_logger, OrderBy};

    #[test]
    fn test_basic_select() {
        test_logger::setup();
        let query = Query {
            collection: "users".to_string(),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\"");
        assert!(params.is_empty());
    }

    #[test]
    fn test_select_with_aliases() {
        let query = Query {
            collection: "users".to_string(),
            selection: Some(vec![
                Select::Field {
                    name: "name".to_string(),
                    alias: Some("username".to_string()),
                },
                Select::Field {
                    name: "age".to_string(),
                    alias: None,
                },
            ]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT \"name\" AS \"username\", \"age\" FROM \"users\""
        );
    }

    #[test]
    fn test_aggregations() {
        let query = Query {
            collection: "orders".to_string(),
            selection: Some(vec![
                Select::Count {
                    field: "id".to_string(),
                    alias: Some("total_orders".to_string()),
                },
                Select::Sum {
                    field: "amount".to_string(),
                    alias: None,
                },
            ]),
            ..Default::default()
        };

        let dialect = MySQLDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT COUNT(`id`) AS `total_orders`, SUM(`amount`) FROM `orders`"
        );
    }

    #[test]
    fn test_raw_select() {
        let query = Query {
            collection: "data".to_string(),
            selection: Some(vec![Select::Raw {
                dialect: "?".to_string(),
                expression: "COUNT(*) * 2".to_string(),
                alias: Some("double_count".to_string()),
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT COUNT(*) * 2 AS \"double_count\" FROM \"data\"");
    }

    #[test]
    fn test_joins() {
        let query = Query {
            collection: "users".to_string(),
            joins: Some(vec![
                Join::Inner {
                    collection: "orders".to_string(),
                    condition: JoinCondition::On {
                        left: "users.id".to_string(),
                        right: "orders.user_id".to_string(),
                    },
                },
                Join::Left {
                    collection: "profiles".to_string(),
                    condition: JoinCondition::Using {
                        field: "user_id".to_string(),
                    },
                },
            ]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" INNER JOIN \"orders\" ON \"users\".\"id\" = \"orders\".\"user_id\" LEFT JOIN \"profiles\" USING (\"user_id\")");
    }

    #[test]
    fn test_complex_where() {
        let query = Query {
            collection: "products".to_string(),
            filters: Some(vec![Filter::And {
                conditions: vec![
                    Filter::Equal {
                        field: "category".to_string(),
                        value: Value::String("electronics".to_string()),
                    },
                    Filter::Or {
                        conditions: vec![
                            Filter::In {
                                field: "price".to_string(),
                                values: vec![
                                    Value::Number(Number::Integer(100)),
                                    Value::Number(Number::Integer(200)),
                                ],
                            },
                            Filter::Raw {
                                dialect: "$1".to_string(),
                                expression: Value::String("discount IS NOT NULL".to_string()),
                            },
                        ],
                    },
                ],
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
      sql,
      "SELECT * FROM \"products\" WHERE (\"category\" = $1 AND (\"price\" IN ($2, $3) OR discount IS NOT NULL))"
  );
        assert_eq!(params.len(), 3);
    }

    #[test]
    fn test_group_by() {
        let query = Query {
            collection: "sales".to_string(),
            aggregations: Some(vec![AggregationStage::Group {
                by: vec!["region".to_string(), "year".to_string()],
                fields: vec![Select::Sum {
                    field: "amount".to_string(),
                    alias: Some("total".to_string()),
                }],
            }]),
            ..Default::default()
        };

        let dialect = MySQLDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(
        sql,
        "SELECT `region`, `year`, SUM(`amount`) AS `total` FROM `sales` GROUP BY `region`, `year`"
    );
    }

    #[test]
    fn test_order_by() {
        let query = Query {
            collection: "employees".to_string(),
            ordering: Some(vec![OrderBy {
                field: "name".to_string(),
                direction: OrderDirection::Asc,
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"employees\" ORDER BY \"name\" ASC");
    }

    #[test]
    fn test_pagination() {
        let query = Query {
            collection: "posts".to_string(),
            pagination: Some(Pagination::Offset {
                limit: 10,
                offset: Some(20),
            }),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"posts\" LIMIT $1 OFFSET $2");
        assert_eq!(
            params,
            vec![
                Value::Number(Number::Unsigned(10)),
                Value::Number(Number::Unsigned(20)),
            ]
        );
    }

    #[test]
    fn test_dialect_specifics() {
        let query = Query {
            collection: "test".to_string(),
            filters: Some(vec![Filter::Equal {
                field: "id".to_string(),
                value: Value::Number(Number::Integer(1)),
            }]),
            pagination: Some(Pagination::Offset {
                limit: 5,
                offset: Some(0),
            }),
            ..Default::default()
        };

        // Test SQLite
        let builder = QueryBuilder::new(&query, &SQLiteDialect {});
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
            sql,
            "SELECT * FROM \"test\" WHERE \"id\" = ? LIMIT ? OFFSET ?"
        );

        // Test PostgreSQL
        let builder = QueryBuilder::new(&query, &PostgresDialect {});
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
            sql,
            "SELECT * FROM \"test\" WHERE \"id\" = $1 LIMIT $2 OFFSET $3"
        );

        // Test MySQL
        let builder = QueryBuilder::new(&query, &MySQLDialect {});
        let (sql, _) = builder.build().unwrap();
        assert_eq!(sql, "SELECT * FROM `test` WHERE `id` = ? LIMIT ? OFFSET ?");
    }

    #[test]
    fn test_error_cases() {
        // Test raw expression dialect mismatch
        let query = Query {
            collection: "test".to_string(),
            selection: Some(vec![Select::Raw {
                dialect: "$1".to_string(),
                expression: "invalid".to_string(),
                alias: None,
            }]),
            ..Default::default()
        };

        let builder = QueryBuilder::new(&query, &SQLiteDialect {});
        let result = builder.build();
        assert!(matches!(result, Err(SqlError::UnsupportedOperation(_))));

        // Test cursor pagination error
        let query = Query {
            collection: "test".to_string(),
            pagination: Some(Pagination::Cursor {
                cursor: Some("abc".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let builder = QueryBuilder::new(&query, &PostgresDialect {});
        let result = builder.build();
        assert!(matches!(result, Err(SqlError::UnsupportedOperation(_))));
    }

    #[test]
    fn test_nested_filters() {
        let query = Query {
            collection: "data".to_string(),
            filters: Some(vec![Filter::Or {
                conditions: vec![
                    Filter::And {
                        conditions: vec![
                            Filter::Equal {
                                field: "a".to_string(),
                                value: Value::Number(Number::Integer(1)),
                            },
                            Filter::Equal {
                                field: "b".to_string(),
                                value: Value::Number(Number::Integer(2)),
                            },
                        ],
                    },
                    Filter::Equal {
                        field: "c".to_string(),
                        value: Value::Number(Number::Integer(3)),
                    },
                ],
            }]),
            ..Default::default()
        };

        let dialect = MySQLDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM `data` WHERE ((`a` = ? AND `b` = ?) OR `c` = ?)"
        );
        assert_eq!(params.len(), 3);

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"data\" WHERE ((\"a\" = ? AND \"b\" = ?) OR \"c\" = ?)"
        );
        assert_eq!(params.len(), 3);

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"data\" WHERE ((\"a\" = $1 AND \"b\" = $2) OR \"c\" = $3)"
        );
        assert_eq!(params.len(), 3);
    }

    #[test]
    fn test_empty_group_by() {
        let query = Query {
            collection: "test".to_string(),
            aggregations: Some(vec![AggregationStage::Match {
                filters: vec![Filter::Equal {
                    field: "active".to_string(),
                    value: Value::Bool(true),
                }],
            }]),
            ..Default::default()
        };

        let builder = QueryBuilder::new(&query, &PostgresDialect {});
        let (sql, _) = builder.build().unwrap();
        assert!(!sql.contains("GROUP BY"));
    }

    #[test]
    fn test_multiple_aggregation_stages() {
        let query = Query {
            collection: "orders".to_string(),
            aggregations: Some(vec![
                AggregationStage::Match {
                    filters: vec![Filter::Equal {
                        field: "status".to_string(),
                        value: Value::String("completed".to_string()),
                    }],
                },
                AggregationStage::Group {
                    by: vec!["region".to_string()],
                    fields: vec![Select::Sum {
                        field: "amount".to_string(),
                        alias: Some("total".to_string()),
                    }],
                },
                AggregationStage::Sort {
                    fields: vec![OrderBy {
                        field: "total".to_string(),
                        direction: OrderDirection::Desc,
                    }],
                },
                AggregationStage::Limit(10),
            ]),
            ..Default::default()
        };

        let builder = QueryBuilder::new(&query, &SQLiteDialect {});
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
        sql,
        "SELECT \"region\", SUM(\"amount\") AS \"total\" FROM \"orders\" WHERE \"status\" = ? GROUP BY \"region\" ORDER BY \"total\" DESC LIMIT ?"
    );
        assert_eq!(params.len(), 2);
    }

    #[test]
    fn test_select_clause() {
        let query = Query {
            selection: Some(vec![Select::Field {
                name: "name".to_string(),
                alias: None,
            }]),
            collection: "users".to_string(),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT \"name\" FROM \"users\"");
    }

    #[test]
    fn test_select_alias() {
        let query = Query {
            selection: Some(vec![Select::Field {
                name: "name".to_string(),
                alias: Some("username".to_string()),
            }]),
            collection: "users".to_string(),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT \"name\" AS \"username\" FROM \"users\"");
    }

    #[test]
    fn test_select_count() {
        let query = Query {
            selection: Some(vec![Select::Count {
                field: "id".to_string(),
                alias: None,
            }]),
            collection: "users".to_string(),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT COUNT(\"id\") FROM \"users\"");
    }

    #[test]
    fn test_select_sum() {
        let query = Query {
            selection: Some(vec![Select::Sum {
                field: "score".to_string(),
                alias: None,
            }]),
            collection: "users".to_string(),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT SUM(\"score\") FROM \"users\"");
    }

    #[test]
    fn test_select_avg() {
        let query = Query {
            selection: Some(vec![Select::Avg {
                field: "rating".to_string(),
                alias: None,
            }]),
            collection: "users".to_string(),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT AVG(\"rating\") FROM \"users\"");
    }

    #[test]
    fn test_select_raw() {
        let query = Query {
            selection: Some(vec![Select::Raw {
                dialect: "$".to_string(),
                expression: "CONCAT(name, ' ', surname)".to_string(),
                alias: None,
            }]),
            collection: "users".to_string(),
            ..Default::default()
        };

        // Test PostgreSQL
        let builder = QueryBuilder::new(&query, &PostgresDialect {});
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT CONCAT(name, ' ', surname) FROM \"users\"");

        // Test MySQL (should fail)
        let builder = QueryBuilder::new(&query, &MySQLDialect {});
        assert!(matches!(
            builder.build(),
            Err(SqlError::UnsupportedOperation(_))
        ));
    }

    #[test]
    fn test_from_clause() {
        let query = Query {
            collection: "users".to_string(),
            ..Default::default()
        };

        let dialect = MySQLDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM `users`");
    }

    #[test]
    fn test_inner_join() {
        let query = Query {
            collection: "users".to_string(),
            joins: Some(vec![Join::Inner {
                collection: "profiles".to_string(),
                condition: JoinCondition::On {
                    left: "user_id".to_string(),
                    right: "id".to_string(),
                },
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" INNER JOIN \"profiles\" ON \"user_id\" = \"id\""
        );
    }

    #[test]
    fn test_left_join() {
        let query = Query {
            collection: "users".to_string(),
            joins: Some(vec![Join::Left {
                collection: "profiles".to_string(),
                condition: JoinCondition::On {
                    left: "user_id".to_string(),
                    right: "id".to_string(),
                },
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" LEFT JOIN \"profiles\" ON \"user_id\" = \"id\""
        );
    }

    #[test]
    fn test_join_using() {
        let query = Query {
            collection: "users".to_string(),
            joins: Some(vec![Join::Inner {
                collection: "profiles".to_string(),
                condition: JoinCondition::Using {
                    field: "user_id".to_string(),
                },
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" INNER JOIN \"profiles\" USING (\"user_id\")"
        );
    }

    #[test]
    fn test_where_equal() {
        let query = Query {
            collection: "users".to_string(),
            filters: Some(vec![Filter::Equal {
                field: "name".to_string(),
                value: Value::String("Alice".to_string()),
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" WHERE \"name\" = ?");
        assert_eq!(params, vec![Value::String("Alice".to_string())]);
    }

    #[test]
    fn test_where_and() {
        let query = Query {
            collection: "users".to_string(),
            filters: Some(vec![Filter::And {
                conditions: vec![
                    Filter::Equal {
                        field: "name".to_string(),
                        value: Value::String("Alice".to_string()),
                    },
                    Filter::Equal {
                        field: "age".to_string(),
                        value: Value::Number(Number::Unsigned(30)),
                    },
                ],
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" WHERE (\"name\" = ? AND \"age\" = ?)"
        );
        assert_eq!(
            params,
            vec![
                Value::String("Alice".to_string()),
                Value::Number(Number::Unsigned(30)),
            ]
        );
    }

    #[test]
    fn test_where_or() {
        let query = Query {
            collection: "users".to_string(),
            filters: Some(vec![Filter::Or {
                conditions: vec![
                    Filter::Equal {
                        field: "name".to_string(),
                        value: Value::String("Alice".to_string()),
                    },
                    Filter::Equal {
                        field: "name".to_string(),
                        value: Value::String("Bob".to_string()),
                    },
                ],
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" WHERE (\"name\" = ? OR \"name\" = ?)"
        );
        assert_eq!(
            params,
            vec![
                Value::String("Alice".to_string()),
                Value::String("Bob".to_string()),
            ]
        );
    }

    #[test]
    fn test_where_in() {
        let query = Query {
            collection: "users".to_string(),
            filters: Some(vec![Filter::In {
                field: "role".to_string(),
                values: vec![
                    Value::String("admin".to_string()),
                    Value::String("moderator".to_string()),
                ],
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" WHERE \"role\" IN (?, ?)");
        assert_eq!(
            params,
            vec![
                Value::String("admin".to_string()),
                Value::String("moderator".to_string()),
            ]
        );
    }

    #[test]
    fn test_where_raw() {
        let query = Query {
            collection: "users".to_string(),
            filters: Some(vec![Filter::Raw {
                dialect: "$1".to_string(),
                expression: Value::String("age > 18".to_string()),
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" WHERE age > 18");
    }

    #[test]
    fn test_group_by_without_fields() {
        let query = Query {
            collection: "users".to_string(),
            aggregations: Some(vec![AggregationStage::Group {
                by: vec!["country".to_string(), "city".to_string()],
                fields: vec![],
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT \"country\", \"city\" FROM \"users\" GROUP BY \"country\", \"city\""
        );
    }

    #[test]
    fn test_order_by_asc() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "name".to_string(),
                direction: OrderDirection::Asc,
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" ORDER BY \"name\" ASC");
    }

    #[test]
    fn test_pagination_limit_offset() {
        let query = Query {
            collection: "users".to_string(),
            pagination: Some(Pagination::Offset {
                limit: 10,
                offset: Some(20),
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" LIMIT ? OFFSET ?");
        assert_eq!(
            params,
            vec![
                Value::Number(Number::Unsigned(10)),
                Value::Number(Number::Unsigned(20)),
            ]
        );
    }

    #[test]
    fn test_aggregation_limit() {
        let query = Query {
            collection: "users".to_string(),
            aggregations: Some(vec![AggregationStage::Limit(25)]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" LIMIT ?");
        assert_eq!(params, vec![Value::Number(Number::Unsigned(25))]);
    }

    #[test]
    fn test_cursor_pagination_next_ascending() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()), // Changed to Some
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" WHERE \"id\" > ? ORDER BY \"id\" ASC LIMIT ?"
        );
        assert_eq!(
            params,
            vec![
                Value::String("100".to_string()),
                Value::Number(Number::Unsigned(10))
            ]
        );
    }

    #[test]
    fn test_cursor_pagination_previous_ascending() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()),
                limit: 10,
                direction: PaginationDirection::Previous,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" WHERE \"id\" < ? ORDER BY \"id\" DESC LIMIT ?"
        );
        assert_eq!(
            params,
            vec![
                Value::String("100".to_string()),
                Value::Number(Number::Unsigned(10))
            ]
        );
    }

    #[test]
    fn test_cursor_pagination_next_descending() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Desc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" WHERE \"id\" < ? ORDER BY \"id\" DESC LIMIT ?"
        );
        assert_eq!(
            params,
            vec![
                Value::String("100".to_string()),
                Value::Number(Number::Unsigned(10))
            ]
        );
    }

    #[test]
    fn test_cursor_pagination_previous_descending() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Desc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()),
                limit: 10,
                direction: PaginationDirection::Previous,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" WHERE \"id\" > ? ORDER BY \"id\" ASC LIMIT ?"
        );
        assert_eq!(
            params,
            vec![
                Value::String("100".to_string()),
                Value::Number(Number::Unsigned(10))
            ]
        );
    }

    #[test]
    fn test_cursor_pagination_no_ordering() {
        let query = Query {
            collection: "users".to_string(),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let result = builder.build();

        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err().to_string(),
            "Unsupported operation: Cursor pagination requires at least one ordering field"
        );
    }

    #[test]
    fn test_cursor_pagination_empty_cursor() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let result = builder.build();

        assert!(matches!(result, Err(SqlError::ValidationError(_))));
        assert_eq!(
            result.unwrap_err().to_string(),
            "Validation error: Cursor value cannot be empty"
        );
    }

    #[test]
    fn test_cursor_pagination_with_filters() {
        let query = Query {
            collection: "users".to_string(),
            filters: Some(vec![Filter::Equal {
                field: "active".to_string(),
                value: Value::Bool(true),
            }]),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
        sql,
        "SELECT * FROM \"users\" WHERE \"active\" = ? AND \"id\" > ? ORDER BY \"id\" ASC LIMIT ?"
    );
        assert_eq!(
            params,
            vec![
                Value::Bool(true),
                Value::String("100".to_string()),
                Value::Number(Number::Unsigned(10))
            ]
        );
    }

    #[test]
    fn test_cursor_pagination_with_joins() {
        let query = Query {
            collection: "users".to_string(),
            joins: Some(vec![Join::Inner {
                collection: "profiles".to_string(),
                condition: JoinCondition::On {
                    left: "users.id".to_string(),
                    right: "profiles.user_id".to_string(),
                },
            }]),
            ordering: Some(vec![OrderBy {
                field: "users.id".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
        sql,
        "SELECT * FROM \"users\" INNER JOIN \"profiles\" ON \"users\".\"id\" = \"profiles\".\"user_id\" WHERE \"users\".\"id\" > ? ORDER BY \"users\".\"id\" ASC LIMIT ?"
    );
        assert_eq!(
            params,
            vec![
                Value::String("100".to_string()),
                Value::Number(Number::Unsigned(10))
            ]
        );
    }

    #[test]
    fn test_cursor_pagination_with_aggregations() {
        let query = Query {
            collection: "users".to_string(),
            aggregations: Some(vec![AggregationStage::Sort {
                fields: vec![OrderBy {
                    field: "id".to_string(),
                    direction: OrderDirection::Asc,
                }],
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" WHERE \"id\" > ? ORDER BY \"id\" ASC LIMIT ?"
        );
        assert_eq!(
            params,
            vec![
                Value::String("100".to_string()),
                Value::Number(Number::Unsigned(10))
            ]
        );
    }

    #[test]
    fn test_cursor_pagination_composite_ordering() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![
                OrderBy {
                    field: "created_at".to_string(),
                    direction: OrderDirection::Desc,
                },
                OrderBy {
                    field: "id".to_string(),
                    direction: OrderDirection::Asc,
                },
            ]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("2023-01-01T00:00:00Z_100".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let result = builder.build();

        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err().to_string(),
            "Unsupported operation: Composite cursor pagination not implemented"
        );
    }

    #[test]
    fn test_not_equal_filter() {
        let query = Query {
            collection: "users".to_string(),
            filters: Some(vec![Filter::NotEqual {
                field: "status".to_string(),
                value: "active".into(),
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" WHERE \"status\" != ?");
        assert_eq!(params, vec![Value::String("active".to_string())]);
    }

    #[test]
    fn test_greater_than_filter() {
        let query = Query {
            collection: "products".to_string(),
            filters: Some(vec![Filter::GreaterThan {
                field: "price".to_string(),
                value: 100.into(),
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"products\" WHERE \"price\" > $1");
        assert_eq!(params, vec![Value::Number(Number::Integer(100))]);
    }

    #[test]
    fn test_less_than_filter() {
        let query = Query {
            collection: "orders".to_string(),
            filters: Some(vec![Filter::LessThan {
                field: "total".to_string(),
                value: 500.0.into(),
            }]),
            ..Default::default()
        };

        let dialect = MySQLDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM `orders` WHERE `total` < ?");
        assert_eq!(params, vec![Value::Number(Number::Float(500.0))]);
    }

    #[test]
    fn test_contains_filter() {
        let query = Query {
            collection: "posts".to_string(),
            filters: Some(vec![Filter::Contains {
                field: "title".to_string(),
                value: "rust".into(),
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"posts\" WHERE \"title\" LIKE ?");
        assert_eq!(params, vec![Value::String("%rust%".to_string())]);
    }

    #[test]
    fn test_exists_filter() {
        let query = Query {
            collection: "profiles".to_string(),
            filters: Some(vec![Filter::Exists {
                field: "avatar".to_string(),
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"profiles\" WHERE \"avatar\" IS NOT NULL"
        );
        assert!(params.is_empty());
    }

    #[test]
    fn test_project_select() {
        let query = Query {
            collection: "users".to_string(),
            selection: Some(vec![Select::Project {
                expression: "(first_name || ' ' || last_name)".to_string(),
                alias: Some("full_name".to_string()),
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
            sql,
            "SELECT (first_name || ' ' || last_name) AS \"full_name\" FROM \"users\""
        );
    }

    #[test]
    fn test_many_to_many_join() {
        let query = Query {
            collection: "users".to_string(),
            joins: Some(vec![Join::ManyToMany {
                target: "roles".to_string(),
                through: "user_roles".to_string(),
                from_key: "user_id".to_string(),
                to_key: "role_id".to_string(),
                main_id: "id".to_string(),
                target_id: "id".to_string(),
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
            sql,
            "SELECT * FROM \"users\" \
                   INNER JOIN \"user_roles\" ON \"users\".\"id\" = \"user_roles\".\"user_id\" \
                   INNER JOIN \"roles\" ON \"user_roles\".\"role_id\" = \"roles\".\"id\""
        );
    }

    #[test]
    fn test_elem_match_filter() {
        let query = Query {
            collection: "articles".to_string(),
            filters: Some(vec![Filter::ElemMatch {
                field: "comments".to_string(),
                conditions: vec![Filter::GreaterThan {
                    field: "rating".to_string(),
                    value: 4.into(),
                }],
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(sql, "SELECT * FROM \"articles\" WHERE EXISTS (SELECT 1 FROM UNNEST(\"comments\") AS elem WHERE \"rating\" > $1)");
    }

    #[test]
    fn test_project_select_without_alias() {
        let query = Query {
            collection: "users".to_string(),
            selection: Some(vec![Select::Project {
                expression: "COUNT(*) * 1.5".to_string(),
                alias: None,
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(sql, "SELECT COUNT(*) * 1.5 FROM \"users\"");
    }

    #[test]
    fn test_raw_select_with_different_dialects() {
        let query = Query {
            collection: "data".to_string(),
            selection: Some(vec![Select::Raw {
                dialect: "$".to_string(),
                expression: "NOW()".to_string(),
                alias: Some("current_time".to_string()),
            }]),
            ..Default::default()
        };

        // Test PostgreSQL
        let builder = QueryBuilder::new(&query, &PostgresDialect {});
        assert!(builder.build().is_ok());

        // Test MySQL (should fail)
        let builder = QueryBuilder::new(&query, &MySQLDialect {});
        assert!(matches!(
            builder.build(),
            Err(SqlError::UnsupportedOperation(_))
        ));
    }

    #[test]
    fn test_join_many_to_many_edge_cases() {
        // Test with quoted identifiers
        let query = Query {
            collection: "order-items".to_string(),
            joins: Some(vec![Join::ManyToMany {
                target: "products".to_string(),
                through: "product_orders".to_string(),
                from_key: "item_id".to_string(),
                to_key: "prod_id".to_string(),
                main_id: "id".to_string(),
                target_id: "id".to_string(),
            }]),
            ..Default::default()
        };

        let dialect = MySQLDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
            sql,
            "SELECT * FROM `order-items` \
         INNER JOIN `product_orders` ON `order-items`.`id` = `product_orders`.`item_id` \
         INNER JOIN `products` ON `product_orders`.`prod_id` = `products`.`id`"
        );
    }

    #[test]
    fn test_elem_match_multiple_conditions() {
        let query = Query {
            collection: "posts".to_string(),
            filters: Some(vec![Filter::ElemMatch {
                field: "comments".to_string(),
                conditions: vec![
                    Filter::GreaterThan {
                        field: "rating".to_string(),
                        value: 4.into(),
                    },
                    Filter::Contains {
                        field: "text".to_string(),
                        value: "rust".into(),
                    },
                ],
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
        sql,
        "SELECT * FROM \"posts\" WHERE EXISTS (SELECT 1 FROM UNNEST(\"comments\") AS elem WHERE \"rating\" > $1 AND \"text\" LIKE $2)"
    );
    }

    #[test]
    fn test_text_search_variations() {
        // Test single field
        let query = Query {
            collection: "docs".to_string(),
            filters: Some(vec![Filter::TextSearch {
                fields: vec!["content".to_string()],
                query: "database".to_string(),
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert!(sql.contains("to_tsvector('english', \"content\") @@ to_tsquery('english', $1)"));

        // Test multiple fields
        let query = Query {
            collection: "docs".to_string(),
            filters: Some(vec![Filter::TextSearch {
                fields: vec!["title".to_string(), "body".to_string()],
                query: "search term".to_string(),
            }]),
            ..Default::default()
        };

        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert!(sql.contains("to_tsvector('english', \"title\" || ' ' || \"body\")"));
    }

    #[test]
    fn test_pagination_edge_cases() {
        // Test zero offset
        let query = Query {
            collection: "logs".to_string(),
            pagination: Some(Pagination::Offset {
                limit: 100,
                offset: Some(0),
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();
        assert_eq!(
            params,
            vec![
                Value::Number(Number::Unsigned(100)),
                Value::Number(Number::Unsigned(0))
            ]
        );
        assert_eq!(sql, "SELECT * FROM \"logs\" LIMIT ? OFFSET ?");

        // Test maximum limit
        let query = Query {
            collection: "metrics".to_string(),
            pagination: Some(Pagination::Offset {
                limit: u64::MAX,
                offset: None,
            }),
            ..Default::default()
        };

        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert!(sql.contains(&format!("LIMIT ?")));
    }

    #[test]
    fn test_error_messages() {
        // Test invalid raw filter
        let query = Query {
            collection: "test".to_string(),
            filters: Some(vec![Filter::Raw {
                dialect: "?".to_string(),
                expression: Value::Number(Number::Integer(42)),
            }]),
            ..Default::default()
        };

        let builder = QueryBuilder::new(&query, &SQLiteDialect {});
        let result = builder.build();
        assert!(matches!(result, Err(SqlError::Other(_))));
        assert_eq!(
            result.unwrap_err().to_string(),
            "Other error: Raw filter expression must be a string value"
        );

        // Test unsupported join type
        let query = Query {
            collection: "test".to_string(),
            joins: Some(vec![Join::Left {
                collection: "other".to_string(),
                condition: JoinCondition::Raw {
                    expression: "invalid".to_string(),
                },
            }]),
            ..Default::default()
        };

        let builder = QueryBuilder::new(&query, &SQLiteDialect {});
        let result = builder.build();
        assert!(result.is_ok()); // Should succeed as we support raw join conditions
    }

    #[test]
    fn test_contains_filter_with_special_chars() {
        let query = Query {
            collection: "posts".to_string(),
            filters: Some(vec![Filter::Contains {
                field: "title".to_string(),
                value: "100%_safe".into(),
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"posts\" WHERE \"title\" LIKE ?");
        assert_eq!(params, vec![Value::String("%100\\%\\_safe%".to_string())]);
    }

    proptest! {
        #[test]
        fn test_query_builder_doesnt_crash(s in "\\PC*") {
            let query = Query {
                collection: s,
                ..Default::default()
            };
            let dialect = SQLiteDialect{};
            let builder = QueryBuilder::new(&query, &dialect);
            let _ = builder.build(); // Should not panic
        }
    }

    #[test]
    fn test_elem_match_filter_postgres() {
        let query = Query {
            collection: "articles".to_string(),
            filters: Some(vec![Filter::ElemMatch {
                field: "comments".to_string(),
                conditions: vec![Filter::GreaterThan {
                    field: "rating".to_string(),
                    value: 4.into(),
                }],
            }]),
            ..Default::default()
        };

        let dialect = PostgresDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
        sql,
        "SELECT * FROM \"articles\" WHERE EXISTS (SELECT 1 FROM UNNEST(\"comments\") AS elem WHERE \"rating\" > $1)"
    );
    }

    #[test]
    fn test_elem_match_filter_sqlite() {
        let query = Query {
            collection: "articles".to_string(),
            filters: Some(vec![Filter::ElemMatch {
                field: "comments".to_string(),
                conditions: vec![Filter::GreaterThan {
                    field: "rating".to_string(),
                    value: 4.into(),
                }],
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
        sql,
        "SELECT * FROM \"articles\" WHERE EXISTS (SELECT 1 FROM UNNEST(\"comments\") AS elem WHERE \"rating\" > ?)"
    );
    }

    #[test]
    fn test_elem_match_filter_mysql() {
        let query = Query {
            collection: "articles".to_string(),
            filters: Some(vec![Filter::ElemMatch {
                field: "comments".to_string(),
                conditions: vec![Filter::GreaterThan {
                    field: "rating".to_string(),
                    value: 4.into(),
                }],
            }]),
            ..Default::default()
        };

        let dialect = MySQLDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, _) = builder.build().unwrap();
        assert_eq!(
        sql,
        "SELECT * FROM `articles` WHERE EXISTS (SELECT 1 FROM UNNEST(`comments`) AS elem WHERE `rating` > ?)"
    );
    }

    #[test]
    fn test_build_count() {
        let query = Query {
            collection: "users".to_string(),
            filters: Some(vec![Filter::Equal {
                field: "active".to_string(),
                value: Value::Bool(true),
            }]),
            joins: Some(vec![Join::Inner {
                collection: "profiles".to_string(),
                condition: JoinCondition::On {
                    left: "users.id".to_string(),
                    right: "profiles.user_id".to_string(),
                },
            }]),
            ordering: Some(vec![OrderBy {
                field: "name".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Offset {
                limit: 10,
                offset: Some(0),
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let mut builder = QueryBuilder::new(&query, &dialect);
        let (count_sql, params) = builder.build_count().unwrap();

        // Count query should include FROM, JOINs, and WHERE, but not ORDER BY or LIMIT
        assert_eq!(
            count_sql,
            "SELECT COUNT(*) AS total FROM \"users\" INNER JOIN \"profiles\" ON \"users\".\"id\" = \"profiles\".\"user_id\" WHERE \"active\" = ?"
        );

        // Verify parameters are preserved
        assert_eq!(params.len(), 1);
        assert_eq!(params[0], Value::Bool(true));
    }

    #[test]
    fn test_build_count_with_group_by() {
        let query = Query {
            collection: "orders".to_string(),
            aggregations: Some(vec![AggregationStage::Group {
                by: vec!["customer_id".to_string()],
                fields: vec![Select::Sum {
                    field: "amount".to_string(),
                    alias: Some("total".to_string()),
                }],
            }]),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let mut builder = QueryBuilder::new(&query, &dialect);
        let (count_sql, params) = builder.build_count().unwrap();

        // Count query should include GROUP BY for accurate counting
        assert_eq!(
            count_sql,
            "SELECT COUNT(*) AS total FROM \"orders\" GROUP BY \"customer_id\""
        );
        assert_eq!(params, vec![]);
    }

    #[test]
    fn test_cursor_pagination_initial_request() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: None, // No cursor provided
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(sql, "SELECT * FROM \"users\" ORDER BY \"id\" ASC LIMIT ?");
        assert_eq!(params, vec![Value::Number(Number::Unsigned(10))]);
    }

    #[test]
    fn test_cursor_pagination_empty_string() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("".to_string()), // Empty cursor string
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let result = builder.build();

        assert!(matches!(
            result,
            Err(SqlError::ValidationError(msg)) if msg == "Cursor value cannot be empty"
        ));
    }

    #[test]
    fn test_cursor_pagination_valid() {
        let query = Query {
            collection: "users".to_string(),
            ordering: Some(vec![OrderBy {
                field: "id".to_string(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("100".to_string()), // Valid cursor
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        let dialect = SQLiteDialect {};
        let builder = QueryBuilder::new(&query, &dialect);
        let (sql, params) = builder.build().unwrap();

        assert_eq!(
            sql,
            "SELECT * FROM \"users\" WHERE \"id\" > ? ORDER BY \"id\" ASC LIMIT ?"
        );
        assert_eq!(
            params,
            vec![
                Value::String("100".to_string()),
                Value::Number(Number::Unsigned(10))
            ]
        );
    }
}
