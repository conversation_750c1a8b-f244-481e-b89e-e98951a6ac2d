//! Database connection pooling and migration management
//!
//! This module provides functionality for managing database connections and migrations.
//! It includes:
//! - Connection pooling with configurable settings
//! - Transaction management
//! - Migration system with version control
//! - SQL file parsing and execution

use anyhow::Result;

pub use crate::adapters::DatabaseAdapter;
use crate::Migration;

/// Generic database wrapper that uses a specific adapter
pub struct Database<T>
where
    T: DatabaseAdapter,
{
    pub adapter: T,
}

impl<T> Database<T>
where
    T: DatabaseAdapter,
{
    /// Creates new database instance with connection pool
    ///
    /// # Panics
    /// - If connection string is invalid
    /// - If pool configuration exceeds database limits
    ///
    /// # Arguments
    /// * `database_url` - Database connection URL or path (format depends on adapter type)
    /// * `config` - Security configuration containing pool settings
    ///
    /// # Returns
    /// `Result<Self>` - Database instance on success
    ///
    /// # Example
    /// ```rust
    /// use db::database::Database;
    /// use db::adapters::sqlite::SQLiteAdapter;
    /// use db::SecurityConfig;
    /// use std::time::Duration;
    ///
    /// let config = SecurityConfig {
    ///     max_pool_size: 10,
    ///     connection_timeout: Duration::from_secs(30),
    ///     migration_table: "migrations".to_string(),
    ///     cursor_expiry: Duration::from_secs(300),
    ///     hmac_secret: "secret".to_string().into_bytes(),
    ///     max_query_depth: 5,
    ///     max_page_size: 100,
    ///     max_string_length: 1000,
    ///     max_array_size: 50,
    ///     ..SecurityConfig::default()
    /// };
    /// let db = Database::<SQLiteAdapter>::new("file::memory:", &config)?;
    /// # Ok::<(), Box<dyn std::error::Error>>(())
    /// ```
    pub fn new(database_url: &str, config: &crate::config::SecurityConfig) -> Result<Self> {
        config.validate()?;
        let adapter = T::connect(database_url, config)?;
        Ok(Self { adapter })
    }

    /// Run database migrations from a directory of SQL files
    ///
    /// # Arguments
    /// * `migrations` - Vector of migrations to apply
    ///
    /// # Returns
    /// `Result<()>` - Success if all migrations are applied
    ///
    /// # Errors
    /// Returns an error if any migration fails to apply
    ///
    /// # Example
    /// ```rust
    /// use db::database::Database;
    /// use db::adapters::sqlite::SQLiteAdapter;
    /// use db::SecurityConfig;
    /// use std::time::Duration;
    /// use std::path::Path;
    /// use db::Migration;
    /// use std::collections::HashMap;
    ///
    /// let config = SecurityConfig {
    ///     max_pool_size: 10,
    ///     connection_timeout: Duration::from_secs(30),
    ///     migration_table: "migrations".to_string(),
    ///     cursor_expiry: Duration::from_secs(300),
    ///     hmac_secret: "secret".to_string().into_bytes(),
    ///     max_query_depth: 5,
    ///     max_page_size: 100,
    ///     max_string_length: 1000,
    ///     max_array_size: 50,
    ///     ..SecurityConfig::default()
    /// };
    /// let db = Database::<SQLiteAdapter>::new("file::memory:", &config)?;

    /// let mut migrations_map = HashMap::new();
    /// migrations_map.insert("001_create_users.sql".to_string(), "-- migrate:up\nCREATE TABLE users (id INTEGER PRIMARY KEY);\n-- migrate:down\nDROP TABLE users;".to_string());
    /// let migrations = Migration::load_from_map(migrations_map).unwrap();  
    /// db.run_migrations(migrations)?;
    /// # Ok::<(), Box<dyn std::error::Error>>(())
    /// ```
    pub fn run_migrations(&self, migrations: Vec<Migration>) -> Result<()> {
        self.adapter.run_migrations(migrations)
    }

    /// Get a connection from the pool
    ///
    /// # Returns
    /// `Result<PooledConnection>` - A database connection from the pool
    ///
    /// # Errors
    /// Returns an error if the connection cannot be obtained
    ///
    /// # Example
    /// ```rust
    /// use db::database::Database;
    /// use db::adapters::sqlite::SQLiteAdapter;
    /// use db::SecurityConfig;
    /// use std::time::Duration;
    ///
    /// let config = SecurityConfig {
    ///     max_pool_size: 10,
    ///     connection_timeout: Duration::from_secs(30),
    ///     migration_table: "migrations".to_string(),
    ///     cursor_expiry: Duration::from_secs(300),
    ///     hmac_secret: "secret".to_string().into_bytes(),
    ///     max_query_depth: 5,
    ///     max_page_size: 100,
    ///     max_string_length: 1000,
    ///     max_array_size: 50,
    ///     ..SecurityConfig::default()
    /// };
    /// let db = Database::<SQLiteAdapter>::new("file::memory:", &config)?;
    /// let conn = db.get_connection()?;
    /// # Ok::<(), Box<dyn std::error::Error>>(())
    /// ```
    pub fn get_connection(&self) -> Result<T::Connection> {
        self.adapter.get_connection()
    }
}
