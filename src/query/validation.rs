//! Validation layer for converting user-provided HTTP queries
//! to safe database queries with field allow-listing
//!
//! This module provides a `QueryValidator` struct that validates and converts
//! user-provided HTTP queries into safe database queries. It performs:
//! - Query validation against security best practices
//! - Field name validation against an allow-list
//! - Value type and content validation
//! - Pagination and cursor validation
//! - Query depth and complexity checks
//! - Security checks for string length, array size, and numeric values

use crate::models::{Filter, OrderBy, Pagination, Select, ValidationError};
use crate::models::{HttpFilter, HttpOrderBy, HttpPagination, HttpQuery, HttpSelect, Query};
use crate::{Number, Value};
use hex;
use log::{debug, warn};
use std::collections::HashSet;
use std::time::SystemTime;

use crate::config::SecurityConfig;

use hmac::{Hmac, Mac};
use sha2::{digest::KeyInit, Sha256};
use subtle::ConstantTimeLess;

type HmacSha256 = Hmac<Sha256>;

/// Validates and converts user-provided HTTP queries into safe database queries
///
/// # Fields
/// - `allowed_fields`: Set of field names that are permitted in queries
/// - `config`: Security configuration parameters for validation
///
/// # Example
/// ```
/// use db::validation::QueryValidator;
/// use db::config::SecurityConfig;
///
/// let config = SecurityConfig::default();
/// let validator = QueryValidator::new(&["id", "name", "age"], config);
/// ```
pub struct QueryValidator {
    /// Allowed field names for filtering/selection
    allowed_fields: HashSet<String>,
    /// Security configuration parameters
    pub config: SecurityConfig,
}

impl QueryValidator {
    /// Creates a new QueryValidator instance
    ///
    /// # Arguments
    /// * `allowed_fields` - List of field names that are permitted in queries
    /// * `config` - Security configuration parameters
    ///
    /// # Returns
    /// A new QueryValidator instance
    pub fn new(allowed_fields: &[&str], config: SecurityConfig) -> Self {
        Self {
            allowed_fields: allowed_fields.iter().map(|s| s.to_string()).collect(),
            config,
        }
    }

    /// Validates a single value according to security rules
    ///
    /// # Arguments
    /// * `value` - The value to validate
    ///
    /// # Returns
    /// * `Ok(())` if the value is valid
    /// * `Err(ValidationError)` if the value is invalid
    ///
    /// # Validation Rules
    /// - Strings: Max length and character set restrictions
    /// - Numbers: NaN and infinity checks
    /// - Arrays: Size limits and recursive validation of elements
    /// - Objects: Not allowed
    fn validate_value(&self, value: &Value) -> Result<(), ValidationError> {
        match value {
            Value::String(s) => {
                if s.len() > self.config.max_string_length {
                    warn!(
                        "String value exceeds max length ({} > {})",
                        s.len(),
                        self.config.max_string_length
                    );
                    return Err(ValidationError::StringTooLong(
                        self.config.max_string_length,
                    ));
                }
                if !s.chars().all(|c| {
                    c.is_ascii_alphanumeric() || c.is_ascii_whitespace() || "@.-_".contains(c)
                }) {
                    warn!("Invalid characters detected in string value: {}", s);
                    return Err(ValidationError::InvalidCharacters);
                }
                Ok(())
            }
            Value::Number(n) => match n {
                Number::Integer(_) => Ok(()),
                Number::Unsigned(_) => Ok(()),
                Number::Float(f) => {
                    if f.is_nan() || f.is_infinite() {
                        Err(ValidationError::InvalidNumber)
                    } else {
                        Ok(())
                    }
                }
            },
            Value::Array(arr) => {
                if arr.len() > self.config.max_array_size {
                    warn!(
                        "Array size exceeds limit ({} > {})",
                        arr.len(),
                        self.config.max_array_size
                    );
                    return Err(ValidationError::ArrayTooLarge(self.config.max_array_size));
                }
                arr.iter().try_for_each(|v| self.validate_value(v))
            }
            Value::Object(_) => Err(ValidationError::InvalidValueType),
            _ => Ok(()),
        }
    }

    /// Validates pagination parameters
    ///
    /// # Arguments
    /// * `pagination` - The pagination parameters to validate
    /// * `ordering` - Optional ordering information (required for cursor pagination)
    ///
    /// # Returns
    /// * `Ok(Pagination)` if valid
    /// * `Err(ValidationError)` if invalid
    ///
    /// # Validation Rules
    /// - Limit must be within configured maximum
    /// - Cursor pagination requires ordering
    /// - Cursor must be properly formatted and not expired
    fn validate_pagination(
        &self,
        pagination: &HttpPagination,
        ordering: &Option<Vec<OrderBy>>,
    ) -> Result<Pagination, ValidationError> {
        match pagination {
            HttpPagination::Offset { limit, offset } => {
                if *limit > self.config.max_page_size as u64 {
                    return Err(ValidationError::PaginationLimitExceeded(
                        self.config.max_page_size as u64,
                    ));
                }
                Ok(Pagination::Offset {
                    limit: *limit,
                    offset: *offset,
                })
            }
            HttpPagination::Cursor {
                cursor,
                limit,
                direction,
            } => {
                if *limit > self.config.max_page_size as u64 {
                    return Err(ValidationError::PaginationLimitExceeded(
                        self.config.max_page_size as u64,
                    ));
                }

                let order_fields = ordering
                    .as_ref()
                    .ok_or(ValidationError::CursorRequiresOrdering)?;
                if order_fields.is_empty() {
                    return Err(ValidationError::CursorRequiresOrdering);
                }

                self.validate_cursor(cursor.as_ref().unwrap())?;

                Ok(Pagination::Cursor {
                    cursor: cursor.clone(),
                    limit: *limit,
                    direction: direction.clone(),
                })
            }
        }
    }

    /// Validates a cursor string
    ///
    /// # Arguments
    /// * `cursor` - The cursor string to validate
    ///
    /// # Returns
    /// * `Ok(())` if the cursor is valid
    /// * `Err(ValidationError)` if the cursor is invalid
    ///
    /// # Validation Rules
    /// - Must have three parts: timestamp.data.signature
    /// - Timestamp must be valid and not expired
    /// - Signature must match HMAC verification
    fn validate_cursor(&self, cursor: &str) -> Result<(), ValidationError> {
        debug!("Validating cursor: {}", cursor);
        let parts: Vec<&str> = cursor.split('.').collect();
        if parts.len() != 3 {
            warn!("Invalid cursor format: expected 3 parts");
            return Err(ValidationError::InvalidCursorFormat);
        }

        let [timestamp, data, signature] = parts[..] else {
            return Err(ValidationError::InvalidCursorFormat);
        };

        // Verify expiration
        let timestamp = timestamp
            .parse::<i64>()
            .map_err(|_| ValidationError::InvalidCursorFormat)?;

        let now = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .map_err(|_| ValidationError::InvalidCursorFormat)?
            .as_secs() as i64;

        // Handle possible negative age (future timestamp) and overflow
        let actual_age = now
            .checked_sub(timestamp)
            .ok_or(ValidationError::CursorExpired)?;

        // Constant-time comparison for cursor expiration
        let max_age = self.config.cursor_expiry.as_secs() as i64;
        if (max_age as u64).ct_lt(&(actual_age as u64)).unwrap_u8() == 1 {
            return Err(ValidationError::CursorExpired);
        }

        // Verify HMAC
        let signature_bytes =
            hex::decode(signature).map_err(|_| ValidationError::CursorVerificationFailed)?;
        let mut mac = <HmacSha256 as KeyInit>::new_from_slice(&self.config.hmac_secret)
            .map_err(|_| ValidationError::CursorVerificationFailed)?;
        mac.update(format!("{}.{}", timestamp, data).as_bytes());
        let computed_signature = mac.finalize().into_bytes();

        if subtle::ConstantTimeEq::ct_eq(computed_signature.as_slice(), signature_bytes.as_slice())
            .unwrap_u8()
            == 1
        {
            Ok(())
        } else {
            Err(ValidationError::CursorVerificationFailed)
        }
    }

    /// Validates a complete HTTP query
    ///
    /// # Arguments
    /// * `http_query` - The HTTP query to validate
    ///
    /// # Returns
    /// * `Ok(Query)` if the query is valid
    /// * `Err(ValidationError)` if the query is invalid
    ///
    /// # Validation Steps
    /// 1. Validate filters
    /// 2. Validate selection
    /// 3. Validate ordering
    /// 4. Validate pagination
    pub fn validate_query(&self, http_query: &HttpQuery) -> Result<Query, ValidationError> {
        debug!("Validating HTTP query: {:?}", http_query);
        let mut query = Query::default();

        // Validate filters
        if let Some(filters) = &http_query.filters {
            let mut validated_filters = Vec::new();
            for filter in filters {
                debug!("Validating filter: {:?}", filter);
                self.validate_filter(filter, 0, &mut validated_filters)
                    .map_err(|e| {
                        warn!("Filter validation failed: {}", e);
                        e
                    })?;
            }
            query.filters = Some(validated_filters);
        }

        // Validate selection
        if let Some(selection) = &http_query.selection {
            query.selection = Some(
                selection
                    .iter()
                    .map(|s| self.validate_select(s))
                    .collect::<Result<_, _>>()?,
            );
        }

        // Validate ordering
        if let Some(ordering) = &http_query.ordering {
            query.ordering = Some(
                ordering
                    .iter()
                    .map(|o| self.validate_order(o))
                    .collect::<Result<_, _>>()?,
            );
        }

        // Validate pagination
        if let Some(pagination) = &http_query.pagination {
            query.pagination = Some(self.validate_pagination(pagination, &query.ordering)?);
        }

        Ok(query)
    }

    /// Validates a filter expression
    ///
    /// # Arguments
    /// * `filter` - The filter to validate
    /// * `depth` - Current recursion depth
    /// * `filters` - Accumulator for validated filters
    ///
    /// # Returns
    /// * `Ok(())` if the filter is valid
    /// * `Err(ValidationError)` if the filter is invalid
    ///
    /// # Validation Rules
    /// - Field names must be allowed
    /// - Values must be valid
    /// - Recursion depth must be within limits
    /// - Array sizes must be within limits
    pub fn validate_filter(
        &self,
        filter: &HttpFilter,
        depth: usize,
        filters: &mut Vec<Filter>,
    ) -> Result<(), ValidationError> {
        if depth > self.config.max_query_depth {
            return Err(ValidationError::QueryTooDeep(self.config.max_query_depth));
        }

        match filter {
            HttpFilter::Equal { field, value } => {
                self.validate_field(field)?;
                self.validate_value(value)?;
                filters.push(Filter::Equal {
                    field: field.clone(),
                    value: value.clone(),
                });
            }
            HttpFilter::NotEqual { field, value } => {
                self.validate_field(field)?;
                self.validate_value(value)?;
                filters.push(Filter::NotEqual {
                    field: field.clone(),
                    value: value.clone(),
                });
            }
            HttpFilter::GreaterThan { field, value } => {
                self.validate_field(field)?;
                self.validate_value(value)?;
                filters.push(Filter::GreaterThan {
                    field: field.clone(),
                    value: value.clone(),
                });
            }
            HttpFilter::LessThan { field, value } => {
                self.validate_field(field)?;
                self.validate_value(value)?;
                filters.push(Filter::LessThan {
                    field: field.clone(),
                    value: value.clone(),
                });
            }
            HttpFilter::Contains { field, value } => {
                self.validate_field(field)?;
                self.validate_value(value)?;
                filters.push(Filter::Contains {
                    field: field.clone(),
                    value: value.clone(),
                });
            }
            HttpFilter::Exists { field } => {
                self.validate_field(field)?;
                filters.push(Filter::Exists {
                    field: field.clone(),
                });
            }
            HttpFilter::In { field, values } => {
                self.validate_field(field)?;
                if values.len() > self.config.max_array_size {
                    return Err(ValidationError::TooManyInValues(self.config.max_array_size));
                }
                for value in values {
                    self.validate_value(value)?;
                }
                filters.push(Filter::In {
                    field: field.clone(),
                    values: values.clone(),
                });
            }
            HttpFilter::And { conditions } | HttpFilter::Or { conditions } => {
                let mut nested = Vec::new();
                for condition in conditions {
                    self.validate_filter(condition, depth + 1, &mut nested)?;
                }
                filters.push(match filter {
                    HttpFilter::And { .. } => Filter::And { conditions: nested },
                    _ => Filter::Or { conditions: nested },
                });
            }
        }

        Ok(())
    }

    fn validate_select(&self, select: &HttpSelect) -> Result<Select, ValidationError> {
        match select {
            HttpSelect::Field { name, alias } => {
                self.validate_field(name)?;
                Ok(Select::Field {
                    name: name.clone(),
                    alias: alias.clone(),
                })
            }
            HttpSelect::Count { field, alias } => {
                self.validate_field(field)?;
                Ok(Select::Count {
                    field: field.clone(),
                    alias: alias.clone(),
                })
            }
            HttpSelect::Sum { field, alias } => {
                self.validate_field(field)?;
                Ok(Select::Sum {
                    field: field.clone(),
                    alias: alias.clone(),
                })
            }
            HttpSelect::Avg { field, alias } => {
                self.validate_field(field)?;
                Ok(Select::Avg {
                    field: field.clone(),
                    alias: alias.clone(),
                })
            }
        }
    }

    fn validate_order(&self, order: &HttpOrderBy) -> Result<OrderBy, ValidationError> {
        self.validate_field(&order.field)?;
        Ok(OrderBy {
            field: order.field.clone(),
            direction: order.direction.clone().into(),
        })
    }

    fn validate_field(&self, field: &str) -> Result<(), ValidationError> {
        // Check for empty field name first
        if field.is_empty() {
            warn!("Attempted to use empty field name");
            return Err(ValidationError::InvalidFieldName(field.to_string()));
        }

        // Validate field format
        if !field.chars().all(|c| c.is_ascii_alphanumeric() || c == '_') {
            warn!("Invalid field name format: {}", field);
            return Err(ValidationError::InvalidFieldName(field.to_string()));
        }

        // Then check against allow list
        if self.allowed_fields.contains(field) {
            Ok(())
        } else {
            warn!("Attempted to access unauthorized field: {}", field);
            Err(ValidationError::InvalidFieldName(field.to_string()))
        }
    }

    /// Generates a secure cursor string
    ///
    /// # Arguments
    /// * `data` - The data to include in the cursor
    ///
    /// # Returns
    /// A secure cursor string containing:
    /// - Timestamp
    /// - Data
    /// - HMAC signature
    ///
    /// # Example
    /// ```
    /// use db::validation::QueryValidator;
    /// use db::config::SecurityConfig;
    ///
    /// let config = SecurityConfig::default();
    /// let validator = QueryValidator::new(&[], config);
    /// let cursor = validator.generate_cursor("some_data");
    /// ```
    ///
    /// # Format
    /// `timestamp.data.base64_signature`
    ///
    /// # Security
    /// - Time-limited validity
    /// - HMAC-SHA256 signature
    /// - Opaque to clients
    pub fn generate_cursor(&self, data: &str) -> Result<String, ValidationError> {
        if self.config.hmac_secret.is_empty() || self.config.hmac_secret.len() < 32 {
            return Err(ValidationError::InvalidHmacKey);
        }

        let timestamp = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .map_err(|_| ValidationError::InvalidCursorFormat)?
            .as_secs() as i64;

        let message = format!("{}.{}", timestamp, data);

        // Create HMAC instance
        let mut mac = <HmacSha256 as KeyInit>::new_from_slice(&self.config.hmac_secret)
            .map_err(|_| ValidationError::CursorVerificationFailed)?;
        mac.update(message.as_bytes());

        // Get result and convert to hex
        let result = mac.finalize();
        let signature = hex::encode(result.into_bytes());

        Ok(format!("{}.{}.{}", timestamp, data, signature))
    }
}

#[cfg(test)]
mod tests {
    use hmac::digest::KeyInit;
    use hmac::Mac;

    use crate::models::{HttpFilter, HttpQuery, HttpSortDirection, ValidationError, Value};
    use crate::models::{HttpOrderBy, HttpPagination, HttpSelect, Number};
    use crate::validation::{HmacSha256, QueryValidator};
    use crate::{PaginationDirection, SecurityConfig};
    use std::collections::HashMap;
    use std::time::Duration;
    use std::time::SystemTime;

    use crate::test_logger;

    #[test]
    fn test_valid_field_usage() {
        test_logger::setup();
        let validator = QueryValidator::new(&["id", "name"], SecurityConfig::default());
        let query = HttpQuery {
            filters: Some(vec![HttpFilter::Equal {
                field: "id".to_string(),
                value: Value::String("123".to_string()),
            }]),
            ..Default::default()
        };

        assert!(validator.validate_query(&query).is_ok());
    }

    #[test]
    fn test_invalid_field_name() {
        let validator = QueryValidator::new(&["id", "name"], SecurityConfig::default());
        let query = HttpQuery {
            filters: Some(vec![HttpFilter::Equal {
                field: "password".to_string(),
                value: Value::String("secret".to_string()),
            }]),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::InvalidFieldName(field)) => assert_eq!(field, "password"),
            _ => panic!("Expected InvalidFieldName error"),
        }
    }

    fn test_validator() -> QueryValidator {
        QueryValidator::new(
            &[
                "id",
                "name",
                "email",
                "created_at",
                "score",
                "active",
                "status",
            ],
            SecurityConfig {
                max_page_size: 1000,
                max_array_size: 100,
                cursor_expiry: Duration::from_secs(3600),
                ..SecurityConfig::default()
            },
        )
    }

    #[test]
    fn test_query_depth_exceeded() {
        let validator = test_validator();
        let filter = HttpFilter::And {
            conditions: vec![HttpFilter::And {
                conditions: vec![HttpFilter::And {
                    conditions: vec![HttpFilter::And {
                        conditions: vec![HttpFilter::And {
                            conditions: vec![HttpFilter::And {
                                conditions: vec![HttpFilter::Equal {
                                    field: "id".to_string(),
                                    value: Value::Null,
                                }],
                            }],
                        }],
                    }],
                }],
            }],
        };

        let query = HttpQuery {
            filters: Some(vec![filter]),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::QueryTooDeep(max)) => assert_eq!(max, 5),
            _ => panic!("Expected QueryTooDeep error"),
        }
    }

    #[test]
    fn test_in_clause_limit_exceeded() {
        let validator = test_validator();
        let values = vec![Value::Number(Number::Integer(1)); 101];

        let query = HttpQuery {
            filters: Some(vec![HttpFilter::In {
                field: "id".to_string(),
                values,
            }]),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::TooManyInValues(max)) => assert_eq!(max, 100),
            _ => panic!("Expected TooManyInValues error"),
        }
    }

    #[test]
    fn test_valid_value_types() {
        let validator = test_validator();
        let query = HttpQuery {
            filters: Some(vec![
                HttpFilter::Equal {
                    field: "id".to_string(),
                    value: Value::Number(Number::Integer(123)),
                },
                HttpFilter::Equal {
                    field: "name".to_string(),
                    value: Value::String("test".to_string()),
                },
                HttpFilter::Equal {
                    field: "email".to_string(),
                    value: Value::String("<EMAIL>".to_string()),
                },
            ]),
            ..Default::default()
        };

        assert!(validator.validate_query(&query).is_ok());
    }

    #[test]
    fn test_invalid_value_type_object() {
        let validator = test_validator();
        let mut obj = HashMap::new();
        obj.insert("key".to_string(), Value::String("value".to_string()));

        let query = HttpQuery {
            filters: Some(vec![HttpFilter::Equal {
                field: "id".to_string(),
                value: Value::Object(obj),
            }]),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::InvalidValueType) => (),
            _ => panic!("Expected InvalidValueType error"),
        }
    }

    #[test]
    fn test_valid_pagination_offset() {
        let validator = test_validator();
        let query = HttpQuery {
            pagination: Some(HttpPagination::Offset {
                limit: 100,
                offset: Some(10),
            }),
            ..Default::default()
        };

        assert!(validator.validate_query(&query).is_ok());
    }

    #[test]
    fn test_excessive_pagination_limit() {
        let validator = test_validator();
        let query = HttpQuery {
            pagination: Some(HttpPagination::Offset {
                limit: 1001,
                offset: None,
            }),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::PaginationLimitExceeded(max)) => assert_eq!(max, 1000),
            _ => panic!("Expected PaginationLimitExceeded error"),
        }
    }

    #[test]
    fn test_cursor_without_ordering() {
        let validator = test_validator();
        let query = HttpQuery {
            pagination: Some(HttpPagination::Cursor {
                cursor: Some("abc123".to_string()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::CursorRequiresOrdering) => (),
            _ => panic!("Expected CursorRequiresOrdering error"),
        }
    }

    #[test]
    fn test_valid_cursor_pagination() {
        let mut config = SecurityConfig::default();
        config.hmac_secret = b"test-secret".repeat(3).to_vec();

        let validator = QueryValidator::new(&["created_at"], config);
        let valid_cursor = validator.generate_cursor("created_at=2023-01-01").unwrap();

        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "created_at".to_string(),
                direction: HttpSortDirection::Desc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(valid_cursor),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        assert!(validator.validate_query(&query).is_ok());
    }

    #[test]
    fn test_tampered_cursor() {
        let mut config = SecurityConfig::default();
        config.hmac_secret = b"test-secret".repeat(3).to_vec();

        let validator = QueryValidator::new(&["created_at"], config);
        let mut valid_cursor = validator.generate_cursor("created_at=2023-01-01").unwrap();

        // Tamper with the data
        valid_cursor = valid_cursor.replace("2023-01-01", "2024-01-01");

        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "created_at".to_string(),
                direction: HttpSortDirection::Desc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(valid_cursor),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::CursorVerificationFailed) => (),
            _ => panic!("Expected CursorVerificationFailed error"),
        }
    }

    #[test]
    fn test_expired_cursor() {
        let mut config = SecurityConfig::default();
        config.hmac_secret = b"test-secret".to_vec();
        config.cursor_expiry = Duration::from_secs(1); // 1 second expiry

        let validator = QueryValidator::new(&["created_at"], config);

        // Instead of sleeping, craft an expired cursor manually using a helper
        fn create_cursor(validator: &QueryValidator, timestamp: i64) -> String {
            let data = "created_at=2023-01-01";
            let message = format!("{}.{}", timestamp, data);
            let mut mac = <HmacSha256 as KeyInit>::new_from_slice(&validator.config.hmac_secret)
                .expect("Invalid HMAC key length");
            mac.update(message.as_bytes());
            let signature = hex::encode(mac.finalize().into_bytes());
            format!("{}.{}.{}", timestamp, data, signature)
        }

        let now = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;
        // Subtract more than the expiry period to ensure the cursor is expired (expiry is 1 sec)
        let expired_cursor = create_cursor(&validator, now - 2);

        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "created_at".to_string(),
                direction: HttpSortDirection::Desc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(expired_cursor),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::CursorExpired) => (),
            _ => panic!("Expected CursorExpired error"),
        }
    }

    #[test]
    fn test_empty_cursor_string() {
        let validator = test_validator();
        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "id".to_string(),
                direction: HttpSortDirection::Asc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(String::new()),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::InvalidCursorFormat) => (),
            _ => panic!("Expected InvalidCursorFormat error"),
        }
    }

    #[test]
    fn test_complex_valid_query() {
        let validator = test_validator();
        let query = HttpQuery {
            filters: Some(vec![HttpFilter::And {
                conditions: vec![
                    HttpFilter::Or {
                        conditions: vec![
                            HttpFilter::Equal {
                                field: "email".to_string(),
                                value: Value::String("<EMAIL>".to_string()),
                            },
                            HttpFilter::GreaterThan {
                                field: "score".to_string(),
                                value: Value::Number(Number::Integer(50)),
                            },
                        ],
                    },
                    HttpFilter::In {
                        field: "id".to_string(),
                        values: vec![
                            Value::Number(Number::Integer(1)),
                            Value::Number(Number::Integer(2)),
                        ],
                    },
                ],
            }]),
            selection: Some(vec![
                HttpSelect::Field {
                    name: "name".to_string(),
                    alias: Some("username".to_string()),
                },
                HttpSelect::Count {
                    field: "id".to_string(),
                    alias: Some("total".to_string()),
                },
            ]),
            ordering: Some(vec![HttpOrderBy {
                field: "created_at".to_string(),
                direction: HttpSortDirection::Desc,
            }]),
            pagination: Some(HttpPagination::Offset {
                limit: 25,
                offset: Some(0),
            }),
        };

        assert!(validator.validate_query(&query).is_ok());
    }

    #[test]
    fn test_future_timestamp_cursor() {
        let mut config = SecurityConfig::default();
        config.hmac_secret = b"test-secret".to_vec();
        config.cursor_expiry = Duration::from_secs(3600);

        let validator = QueryValidator::new(&["created_at"], config);

        // Helper to create cursor with specific timestamp
        fn create_cursor(validator: &QueryValidator, timestamp: i64) -> String {
            let data = "created_at=2023-01-01";
            let message = format!("{}.{}", timestamp, data);
            let mut mac = <HmacSha256 as KeyInit>::new_from_slice(&validator.config.hmac_secret)
                .expect("Invalid HMAC key length");
            mac.update(message.as_bytes());
            let signature = hex::encode(mac.finalize().into_bytes());
            format!("{}.{}.{}", timestamp, data, signature)
        }

        // Create cursor with future timestamp
        let now = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;
        let future_cursor = create_cursor(&validator, now + 3600);

        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "created_at".to_string(),
                direction: HttpSortDirection::Desc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(future_cursor),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::CursorExpired) => (),
            _ => panic!("Expected CursorExpired error for future timestamp"),
        }
    }

    #[test]
    fn test_cursor_exact_expiry_time() {
        let mut config = SecurityConfig::default();
        config.hmac_secret = b"test-secret".repeat(3).to_vec();
        let max_age = 3600;
        config.cursor_expiry = Duration::from_secs(max_age);

        let validator = QueryValidator::new(&["created_at"], config);

        // Create cursor with timestamp exactly at expiry boundary
        let now = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;
        let valid_timestamp = now - max_age as i64;

        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "created_at".to_string(),
                direction: HttpSortDirection::Desc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(
                    validator
                        .generate_cursor(&format!("created_at={}", valid_timestamp))
                        .unwrap(),
                ),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        assert!(
            validator.validate_query(&query).is_ok(),
            "Cursor with exact expiry time should be valid"
        );
    }

    #[test]
    fn test_empty_field_name() {
        let validator = QueryValidator::new(&["id", "name"], SecurityConfig::default());
        let query = HttpQuery {
            filters: Some(vec![HttpFilter::Equal {
                field: "".to_string(), // Empty field name
                value: Value::String("123".to_string()),
            }]),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::InvalidFieldName(field)) => assert!(field.is_empty()),
            _ => panic!("Expected InvalidFieldName error for empty field"),
        }
    }

    #[test]
    fn test_invalid_hmac_key_length() {
        let mut config = SecurityConfig::default();
        config.hmac_secret = vec![]; // Empty key is invalid
        let validator = QueryValidator::new(&["created_at"], config);

        let result = validator.generate_cursor("data");
        assert!(
            matches!(result, Err(ValidationError::InvalidHmacKey)),
            "Expected InvalidHmacKey, got {:?}",
            result
        );
    }

    #[test]
    fn test_hmac_verification_failure_different_key() {
        let mut config1 = SecurityConfig::default();
        config1.hmac_secret = b"key1".repeat(8).to_vec();
        let validator1 = QueryValidator::new(&["field"], config1);

        let mut config2 = SecurityConfig::default();
        config2.hmac_secret = b"key2".to_vec();
        let validator2 = QueryValidator::new(&["field"], config2);

        let cursor = validator1.generate_cursor("data").unwrap();
        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "field".to_string(),
                direction: HttpSortDirection::Asc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(cursor),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        match validator2.validate_query(&query) {
            Err(ValidationError::CursorVerificationFailed) => (),
            _ => panic!("Expected CursorVerificationFailed error"),
        }
    }

    #[test]
    fn test_invalid_cursor_components() {
        let validator = test_validator();

        // Test missing components
        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "id".to_string(),
                direction: HttpSortDirection::Asc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some("timestamp.data".to_string()), // Missing signature
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::InvalidCursorFormat) => (),
            _ => panic!("Expected InvalidCursorFormat error"),
        }
    }

    #[test]
    fn test_non_hex_signature() {
        let validator = test_validator();

        // Create a current timestamp to avoid expiration
        let now = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;

        let invalid_cursor = format!("{}.data.zzzzzz", now); // z's are not valid hex

        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "id".to_string(),
                direction: HttpSortDirection::Asc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(invalid_cursor),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        match validator.validate_query(&query) {
            Err(ValidationError::CursorVerificationFailed) => (),
            other => panic!("Expected CursorVerificationFailed error, got {:?}", other),
        }
    }

    #[test]
    fn test_very_long_data_in_cursor() {
        let mut config = SecurityConfig::default();
        config.hmac_secret = b"secret".repeat(6).to_vec();
        config.max_string_length = 1024; // Set higher limit for this test

        let validator = QueryValidator::new(&["field"], config);
        let long_data = "a".repeat(2048); // Exceeds normal limit but allowed in cursor?

        let cursor = validator.generate_cursor(&long_data).unwrap();
        let query = HttpQuery {
            ordering: Some(vec![HttpOrderBy {
                field: "field".to_string(),
                direction: HttpSortDirection::Asc,
            }]),
            pagination: Some(HttpPagination::Cursor {
                cursor: Some(cursor),
                limit: 10,
                direction: PaginationDirection::Next,
            }),
            ..Default::default()
        };

        // Should succeed as cursor data isn't subject to normal string limits
        assert!(validator.validate_query(&query).is_ok());
    }
}
