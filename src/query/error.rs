use thiserror::Error;

#[derive(Debug, Error)]
pub enum SqlError {
    #[error("Unsupported operation: {0}")]
    UnsupportedOperation(String),

    #[error("Invalid parameter value: {0}")]
    InvalidParameter(String),

    #[error("Query build error: {0}")]
    BuildError(String),

    #[error("Database error: {0}")]
    DatabaseError(String),

    #[error("Query returned no rows")]
    QueryReturnedNoRows,

    #[error("Connection error: {0}")]
    ConnectionError(String),

    #[error("Pool error: {0}")]
    PoolError(String),

    #[error("Migration error: {0}")]
    MigrationError(String),

    #[error("Validation error: {0}")]
    ValidationError(String),

    #[error("Encryption error: {0}")]
    EncryptionError(String),

    #[error("Other error: {0}")]
    Other(String),

    #[error("Id not set")]
    IdNotSet,

    #[error("Id not match")]
    IdNotMatch,
}

impl From<anyhow::Error> for SqlError {
    fn from(err: anyhow::Error) -> Self {
        SqlError::Other(err.to_string())
    }
}

impl From<rusqlite::Error> for SqlError {
    fn from(err: rusqlite::Error) -> Self {
        SqlError::DatabaseError(err.to_string())
    }
}

impl From<crate::ValidationError> for SqlError {
    fn from(err: crate::ValidationError) -> Self {
        SqlError::ValidationError(err.to_string())
    }
}
