//! Data models for constructing database queries
//!
//! This module contains the core data structures used to represent database queries
//! in a dialect-agnostic way. The main structures are:
//! - Filter conditions
//! - Sorting requirements
//! - Pagination settings
//! - Join operations
//! - `Query`: The complete query specification
//! - `Filter`: Conditions for WHERE clauses
//! - `Select`: Fields to select and aggregations
//! - `Join`: Join specifications
//! - `Pagination`: Pagination options
//! - `AggregationStage`: Aggregation pipeline stages

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::convert::TryFrom;
use std::fmt;
use std::time::SystemTime;
use postgres::types::{ToSql, Type, IsNull};
use postgres::types::private::BytesMut;
use std::error::Error;

/// Represents a complete database query
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct Query {
    /// Primary table/collection name
    pub collection: String,
    /// Filter conditions (WHERE clause)
    pub filters: Option<Vec<Filter>>,
    /// Selected fields/aggregations
    pub selection: Option<Vec<Select>>,
    /// Sorting order (ORDER BY)
    pub ordering: Option<Vec<OrderBy>>,
    /// Pagination configuration
    pub pagination: Option<Pagination>,
    /// Join definitions
    pub joins: Option<Vec<Join>>,
    /// Aggregation pipeline stages
    pub aggregations: Option<Vec<AggregationStage>>,
    /// Dialect-specific extensions
    pub extensions: Option<HashMap<String, Value>>,
}

/// Represents a filter condition for WHERE clauses
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase")]
pub enum Filter {
    /// Equality comparison (=)
    Equal { field: String, value: Value },
    /// Inequality comparison (!= or <>)
    NotEqual { field: String, value: Value },
    /// Range filter (>)
    GreaterThan { field: String, value: Value },
    /// Range filter (<)
    LessThan { field: String, value: Value },
    /// Partial string match (LIKE %value%)
    Contains { field: String, value: Value },
    /// Set membership check
    In { field: String, values: Vec<Value> },
    /// Logical AND combination
    And { conditions: Vec<Filter> },
    /// Logical OR combination
    Or { conditions: Vec<Filter> },
    /// Null/Exists check
    Exists { field: String },
    /// Match elements in an array
    ElemMatch {
        field: String,
        conditions: Vec<Filter>,
    },
    /// Full text search across multiple fields
    TextSearch { fields: Vec<String>, query: String },
    /// Raw SQL expression
    Raw { dialect: String, expression: Value },
}

/// Represents a field selection or aggregation
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase")]
pub enum Select {
    /// Simple field selection
    Field { name: String, alias: Option<String> },
    /// COUNT aggregation
    Count {
        field: String,
        alias: Option<String>,
    },
    /// SUM aggregation
    Sum {
        field: String,
        alias: Option<String>,
    },
    /// AVG aggregation
    Avg {
        field: String,
        alias: Option<String>,
    },
    /// Projection expression
    Project {
        expression: String,
        alias: Option<String>,
    },
    /// Raw SQL expression
    Raw {
        dialect: String,
        expression: String,
        alias: Option<String>,
    },
}

/// Represents an ordering specification
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct OrderBy {
    /// Field to order by
    pub field: String,
    /// Ordering direction
    pub direction: OrderDirection,
}

/// Represents the direction of ordering
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum OrderDirection {
    /// Ascending order
    Asc,
    /// Descending order
    Desc,
}

/// Represents a join specification
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase")]
pub enum Join {
    /// INNER JOIN
    Inner {
        collection: String,
        condition: JoinCondition,
    },
    /// LEFT JOIN
    Left {
        collection: String,
        condition: JoinCondition,
    },
    /// Many-to-many relationship through a join table
    ManyToMany {
        target: String,
        through: String,
        from_key: String,
        to_key: String,
        main_id: String,
        target_id: String,
    },
}

/// Represents a join condition
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase")]
pub enum JoinCondition {
    /// ON clause with field comparison
    On { left: String, right: String },
    /// USING clause with common field
    Using { field: String },
    /// Raw SQL expression
    Raw { expression: String },
}

/// Represents pagination options
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase")]
pub enum Pagination {
    /// Offset-based pagination
    Offset { limit: u64, offset: Option<u64> },
    /// Cursor-based pagination
    Cursor {
        cursor: Option<String>,
        limit: u64,
        direction: PaginationDirection,
    },
}

/// Represents pagination direction for cursor-based pagination
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PaginationDirection {
    /// Next page
    Next,
    /// Previous page
    Previous,
}

/// Represents an aggregation pipeline stage
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase")]
pub enum AggregationStage {
    /// Filter documents
    Match { filters: Vec<Filter> },
    /// Group documents
    Group {
        by: Vec<String>,
        fields: Vec<Select>,
    },
    /// Sort documents
    Sort { fields: Vec<OrderBy> },
    /// Limit number of documents
    Limit(u64),
    /// Skip number of documents
    Skip(u64),
    /// Join with another collection
    Lookup(Join),
    /// Unwind array field
    Unwind { path: String, preserve_null: bool },
    /// Raw aggregation stage
    Raw { stage: Value },
}

/// Represents a value that can be used in queries
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(untagged)]
pub enum Value {
    /// Null value
    Null,
    /// Boolean value
    Bool(bool),
    /// Numeric value
    Number(Number),
    /// DateTime value
    DateTime(DateTime<Utc>),
    /// String value
    String(String),
    /// Array of values
    Array(Vec<Value>),
    /// Object/map of values
    Object(HashMap<String, Value>),
}

/// Represents a numeric value
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(untagged)]
pub enum Number {
    /// Signed integer
    Integer(i64),
    /// Unsigned integer
    Unsigned(u64),
    /// Floating point number
    Float(f64),
}

impl fmt::Display for Value {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Value::Null => write!(f, "null"),
            Value::Bool(b) => write!(f, "{}", b),
            Value::Number(n) => write!(f, "{}", n),
            Value::String(s) => write!(f, "\"{}\"", s),
            Value::DateTime(dt) => write!(f, "\"{}\"", dt.to_rfc3339()),
            Value::Array(arr) => {
                write!(f, "[")?;
                for (i, item) in arr.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", item)?;
                }
                write!(f, "]")
            }
            Value::Object(obj) => {
                write!(f, "{{")?;
                for (i, (k, v)) in obj.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "\"{}\": {}", k, v)?;
                }
                write!(f, "}}")
            }
        }
    }
}

impl fmt::Display for Number {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Number::Integer(i) => write!(f, "{}", i),
            Number::Unsigned(u) => write!(f, "{}", u),
            Number::Float(fl) => {
                if fl.is_nan() {
                    return Err(fmt::Error);
                }
                write!(f, "{}", fl)
            }
        }
    }
}

// Conversion implementations
impl From<&str> for Value {
    fn from(s: &str) -> Self {
        Value::String(s.to_string())
    }
}

impl From<String> for Value {
    fn from(s: String) -> Self {
        Value::String(s)
    }
}

impl<'a> From<&'a String> for Value {
    fn from(s: &'a String) -> Self {
        Value::String(s.clone())
    }
}

impl From<i64> for Value {
    fn from(i: i64) -> Self {
        Value::Number(Number::Integer(i))
    }
}

impl<'a> From<&'a i64> for Value {
    fn from(i: &i64) -> Self {
        Value::Number(Number::Integer(*i))
    }
}

impl From<u64> for Value {
    fn from(u: u64) -> Self {
        Value::Number(Number::Unsigned(u))
    }
}

impl<'a> From<&'a u64> for Value {
    fn from(u: &u64) -> Self {
        Value::Number(Number::Unsigned(*u))
    }
}

impl From<f64> for Value {
    fn from(f: f64) -> Self {
        Value::Number(Number::Float(f))
    }
}

impl<'a> From<&'a f64> for Value {
    fn from(f: &f64) -> Self {
        Value::Number(Number::Float(*f))
    }
}

impl From<i32> for Value {
    fn from(i: i32) -> Self {
        Value::Number(Number::Integer(i as i64))
    }
}

impl<'a> From<&'a i32> for Value {
    fn from(i: &i32) -> Self {
        Value::Number(Number::Integer(*i as i64))
    }
}

impl From<bool> for Value {
    fn from(b: bool) -> Self {
        Value::Bool(b)
    }
}

impl<'a> From<&'a bool> for Value {
    fn from(b: &bool) -> Self {
        Value::Bool(*b)
    }
}

impl From<SystemTime> for Value {
    fn from(time: SystemTime) -> Self {
        Value::DateTime(DateTime::from(time))
    }
}

impl<'a> From<&'a SystemTime> for Value {
    fn from(time: &'a SystemTime) -> Self {
        Value::DateTime(DateTime::from(*time))
    }
}

impl<T> From<Option<T>> for Value
where
    T: Into<Value>,
{
    fn from(opt: Option<T>) -> Self {
        match opt {
            Some(v) => v.into(),
            None => Value::Null,
        }
    }
}

impl<'a> From<&'a Option<String>> for Value {
    fn from(opt: &'a Option<String>) -> Self {
        match opt {
            Some(v) => Value::String(v.clone()),
            None => Value::Null,
        }
    }
}

impl<T> From<Vec<T>> for Value
where
    T: Into<Value>,
{
    fn from(vec: Vec<T>) -> Self {
        Value::Array(vec.into_iter().map(Into::into).collect())
    }
}

impl<'a> From<&'a Vec<Value>> for Value {
    fn from(vec: &'a Vec<Value>) -> Self {
        Value::Array(vec.clone())
    }
}

impl<K, V> From<HashMap<K, V>> for Value
where
    K: Into<String>,
    V: Into<Value>,
{
    fn from(map: HashMap<K, V>) -> Self {
        Value::Object(map.into_iter().map(|(k, v)| (k.into(), v.into())).collect())
    }
}

impl<'a> From<&'a HashMap<String, Value>> for Value {
    fn from(map: &'a HashMap<String, Value>) -> Self {
        Value::Object(map.clone())
    }
}

// Add new HTTP-specific models with validation
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase", deny_unknown_fields)]
pub struct HttpQuery {
    #[serde(default)]
    pub filters: Option<Vec<HttpFilter>>,
    #[serde(default)]
    pub selection: Option<Vec<HttpSelect>>,
    #[serde(default)]
    pub ordering: Option<Vec<HttpOrderBy>>,
    #[serde(default)]
    pub pagination: Option<HttpPagination>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase", deny_unknown_fields)]
pub enum HttpFilter {
    Equal { field: String, value: Value },
    NotEqual { field: String, value: Value },
    GreaterThan { field: String, value: Value },
    LessThan { field: String, value: Value },
    Contains { field: String, value: Value },
    In { field: String, values: Vec<Value> },
    And { conditions: Vec<HttpFilter> },
    Or { conditions: Vec<HttpFilter> },
    Exists { field: String },
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase", deny_unknown_fields)]
pub enum HttpSelect {
    Field {
        name: String,
        alias: Option<String>,
    },
    Count {
        field: String,
        alias: Option<String>,
    },
    Sum {
        field: String,
        alias: Option<String>,
    },
    Avg {
        field: String,
        alias: Option<String>,
    },
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct HttpOrderBy {
    pub field: String,
    pub direction: HttpSortDirection,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum HttpSortDirection {
    Asc,
    Desc,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "camelCase")]
pub enum HttpPagination {
    Offset {
        limit: u64,
        offset: Option<u64>,
    },
    Cursor {
        cursor: Option<String>,
        limit: u64,
        direction: PaginationDirection,
    },
}

// Add conversion implementations
impl TryFrom<HttpQuery> for Query {
    type Error = ValidationError;

    fn try_from(http_query: HttpQuery) -> Result<Self, Self::Error> {
        // Remove the default validator - validation should be done separately
        // with properly configured QueryValidator before conversion
        Ok(Query {
            collection: String::new(), // This should come from context
            filters: http_query
                .filters
                .map(|filters| {
                    filters
                        .into_iter()
                        .map(TryInto::try_into)
                        .collect::<Result<_, _>>()
                })
                .transpose()?,
            selection: http_query
                .selection
                .map(|selects| {
                    selects
                        .into_iter()
                        .map(TryInto::try_into)
                        .collect::<Result<_, _>>()
                })
                .transpose()?,
            ordering: http_query
                .ordering
                .map(|orders| {
                    orders
                        .into_iter()
                        .map(TryInto::try_into)
                        .collect::<Result<_, _>>()
                })
                .transpose()?,
            pagination: http_query.pagination.map(TryInto::try_into).transpose()?,
            joins: None,
            aggregations: None,
            extensions: None,
        })
    }
}

impl TryFrom<HttpFilter> for Filter {
    type Error = ValidationError;

    fn try_from(value: HttpFilter) -> Result<Self, Self::Error> {
        // Convert without validation - validation should be done before conversion
        match value {
            HttpFilter::Equal { field, value } => Ok(Filter::Equal { field, value }),
            HttpFilter::NotEqual { field, value } => Ok(Filter::NotEqual { field, value }),
            HttpFilter::GreaterThan { field, value } => Ok(Filter::GreaterThan { field, value }),
            HttpFilter::LessThan { field, value } => Ok(Filter::LessThan { field, value }),
            HttpFilter::Contains { field, value } => Ok(Filter::Contains { field, value }),
            HttpFilter::In { field, values } => Ok(Filter::In { field, values }),
            HttpFilter::And { conditions } => Ok(Filter::And {
                conditions: conditions
                    .into_iter()
                    .map(TryInto::try_into)
                    .collect::<Result<_, _>>()?,
            }),
            HttpFilter::Or { conditions } => Ok(Filter::Or {
                conditions: conditions
                    .into_iter()
                    .map(TryInto::try_into)
                    .collect::<Result<_, _>>()?,
            }),
            HttpFilter::Exists { field } => Ok(Filter::Exists { field }),
        }
    }
}

impl TryFrom<HttpSelect> for Select {
    type Error = ValidationError;

    fn try_from(value: HttpSelect) -> Result<Self, Self::Error> {
        match value {
            HttpSelect::Field { name, alias } => Ok(Select::Field { name, alias }),
            HttpSelect::Count { field, alias } => Ok(Select::Count { field, alias }),
            HttpSelect::Sum { field, alias } => Ok(Select::Sum { field, alias }),
            HttpSelect::Avg { field, alias } => Ok(Select::Avg { field, alias }),
        }
    }
}

impl From<HttpSortDirection> for OrderDirection {
    fn from(direction: HttpSortDirection) -> Self {
        match direction {
            HttpSortDirection::Asc => OrderDirection::Asc,
            HttpSortDirection::Desc => OrderDirection::Desc,
        }
    }
}

impl TryFrom<HttpOrderBy> for OrderBy {
    type Error = ValidationError;

    fn try_from(value: HttpOrderBy) -> Result<Self, Self::Error> {
        Ok(OrderBy {
            field: value.field,
            direction: value.direction.into(),
        })
    }
}

impl TryFrom<HttpPagination> for Pagination {
    type Error = ValidationError;

    fn try_from(value: HttpPagination) -> Result<Self, Self::Error> {
        match value {
            HttpPagination::Offset { limit, offset } => Ok(Pagination::Offset { limit, offset }),
            HttpPagination::Cursor {
                cursor,
                limit,
                direction,
            } => Ok(Pagination::Cursor {
                cursor: cursor,
                limit,
                direction,
            }),
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ValidationError {
    #[error("Unsupported filter operation")]
    UnsupportedFilterOperation,
    #[error("Invalid field name: {0}")]
    InvalidFieldName(String),
    #[error("Query too deep (max depth: {0})")]
    QueryTooDeep(usize),
    #[error("Too many values in IN clause (max: {0})")]
    TooManyInValues(usize),
    #[error("Cursor pagination requires ordering")]
    CursorRequiresOrdering,
    #[error("Invalid cursor format")]
    InvalidCursorFormat,
    #[error("Nested array operations not allowed")]
    NestedArrayOperations,
    #[error("Invalid value type")]
    InvalidValueType,
    #[error("Pagination limit exceeds maximum of {0}")]
    PaginationLimitExceeded(u64),
    #[error("Invalid number")]
    InvalidNumber,
    #[error("Invalid characters")]
    InvalidCharacters,
    #[error("String too long (max length: {0})")]
    StringTooLong(usize),
    #[error("Cursor expired")]
    CursorExpired,
    #[error("Cursor verification failed")]
    CursorVerificationFailed,
    #[error("Array too large (max size: {0})")]
    ArrayTooLarge(usize),
    #[error("Invalid value")]
    InvalidValue,
    #[error("Invalid HMAC key")]
    InvalidHmacKey,
}

impl rusqlite::ToSql for Value {
    fn to_sql(&self) -> rusqlite::Result<rusqlite::types::ToSqlOutput<'_>> {
        use rusqlite::types::Value as SqlValue;
        
        let sql_value = match self {
            Value::Null => SqlValue::Null,
            Value::Bool(b) => if *b { SqlValue::Integer(1) } else { SqlValue::Integer(0) },
            Value::Number(n) => match n {
                Number::Integer(i) => SqlValue::Integer(*i),
                Number::Unsigned(u) => SqlValue::Integer(*u as i64),
                Number::Float(f) => SqlValue::Real(*f),
            },
            Value::DateTime(dt) => {
                SqlValue::Text(dt.to_rfc3339())
            },
            Value::String(s) => SqlValue::Text(s.as_str().to_string()),
            Value::Array(_) => return Err(rusqlite::Error::InvalidParameterName(
                "Arrays not supported as SQL parameters".into()
            )),
            Value::Object(_) => return Err(rusqlite::Error::InvalidParameterName(
                "Objects not supported as SQL parameters".into()
            )),
        };
        
        Ok(rusqlite::types::ToSqlOutput::Owned(sql_value))
    }
}

// Add this implementation for MySQL value conversion
impl From<&Value> for mysql::Value {
    fn from(value: &Value) -> Self {
        match value {
            Value::Null => mysql::Value::NULL,
            Value::Bool(b) => mysql::Value::Int(if *b { 1 } else { 0 }),
            Value::Number(n) => match n {
                Number::Integer(i) => mysql::Value::Int(*i),
                Number::Unsigned(u) => mysql::Value::UInt(*u),
                Number::Float(f) => mysql::Value::Float(*f as f32),
            },
            Value::DateTime(dt) => mysql::Value::Bytes(dt.to_rfc3339().into_bytes()),
            Value::String(s) => mysql::Value::Bytes(s.as_bytes().to_vec()),
            Value::Array(arr) => {
                let bytes = serde_json::to_vec(arr).unwrap_or_default();
                mysql::Value::Bytes(bytes)
            }
            Value::Object(obj) => {
                let bytes = serde_json::to_vec(obj).unwrap_or_default();
                mysql::Value::Bytes(bytes)
            }
        }
    }
}

impl ToSql for Value {
    fn to_sql(
        &self,
        ty: &Type,
        out: &mut BytesMut,
    ) -> Result<IsNull, Box<dyn Error + Sync + Send>> {
        match self {
            Value::Null => Ok(postgres::types::IsNull::Yes),
            Value::Bool(b) => ToSql::to_sql(b, ty, out),
            Value::Number(n) => match n {
                Number::Integer(i) => ToSql::to_sql(i, ty, out),
                Number::Unsigned(u) => ToSql::to_sql(&(*u as i64), ty, out),
                Number::Float(f) => ToSql::to_sql(f, ty, out),
            },
            Value::DateTime(dt) => {
                let pg_timestamp = postgres::types::Timestamp::<chrono::DateTime<Utc>>::from(postgres::types::Timestamp::Value(*dt));
                ToSql::to_sql(&pg_timestamp, ty, out)
            },
            Value::String(s) => ToSql::to_sql(s, ty, out),
            Value::Array(arr) => {
                let json = serde_json::to_value(arr)?;
                postgres::types::Json(json).to_sql(ty, out)
            }
            Value::Object(obj) => {
                let json = serde_json::to_value(obj)?;
                postgres::types::Json(json).to_sql(ty, out)
            }
        }
    }

    fn to_sql_checked(
        &self,
        ty: &Type,
        out: &mut BytesMut,
    ) -> Result<IsNull, Box<dyn Error + Sync + Send>> {
        self.to_sql(ty, out)
    }

    fn accepts(ty: &Type) -> bool {
        ty == &postgres::types::Type::BOOL ||
        ty == &postgres::types::Type::INT8 ||
        ty == &postgres::types::Type::FLOAT8 ||
        ty == &postgres::types::Type::TIMESTAMPTZ ||
        ty == &postgres::types::Type::TEXT ||
        ty == &postgres::types::Type::JSONB
    }
}

#[cfg(test)]
mod tests {
    use chrono::{DateTime, TimeZone as _, Utc};
    use super::{
        HttpSortDirection, Number, Value, AggregationStage, Filter, HttpFilter, HttpOrderBy, HttpPagination, HttpQuery, HttpSelect, Join, JoinCondition, OrderBy, OrderDirection, Pagination, PaginationDirection, Query, Select
    };
    use std::{collections::HashMap, time::SystemTime};

    #[test]
    fn test_value_enum() {
        let null = Value::Null;
        let boolean = Value::Bool(true);
        let integer = Value::Number(Number::Integer(-42));
        let unsigned = Value::Number(Number::Unsigned(42));
        let float = Value::Number(Number::Float(3.14));
        let string = Value::String("test".into());
        let datetime = Value::DateTime(Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap());
        let array = Value::Array(vec![Value::Null, Value::Bool(true)]);
        let mut obj = HashMap::new();
        obj.insert("key".into(), Value::String("value".into()));
        let object = Value::Object(obj);

        assert_eq!(format!("{}", null), "null");
        assert_eq!(format!("{}", boolean), "true");
        assert_eq!(format!("{}", integer), "-42");
        assert_eq!(format!("{}", unsigned), "42");
        assert_eq!(format!("{}", float), "3.14");
        assert_eq!(format!("{}", string), "\"test\"");
        assert_eq!(format!("{}", datetime), "\"2023-01-01T00:00:00+00:00\"");
        assert_eq!(format!("{}", array), "[null, true]");
        assert_eq!(format!("{}", object), "{\"key\": \"value\"}");
    }

    #[test]
    fn test_number_serialization() {
        let numbers = vec![
            Number::Integer(-42),
            Number::Float(3.14),
            Number::Unsigned(10_000_000_000_000_000_000), // A u64 value that exceeds i64's maximum
        ];

        for num in numbers {
            let serialized = serde_json::to_string(&num).unwrap();
            let deserialized: Number = serde_json::from_str(&serialized).unwrap();
            assert_eq!(num, deserialized);
        }
    }

    #[test]
    fn test_filter_serialization() {
        let filter = Filter::And {
            conditions: vec![
                Filter::Equal {
                    field: "age".into(),
                    value: 25.into(),
                },
                Filter::Or {
                    conditions: vec![
                        Filter::GreaterThan {
                            field: "score".into(),
                            value: 80.0.into(),
                        },
                        Filter::In {
                            field: "tags".into(),
                            values: vec!["admin".into(), "user".into()],
                        },
                    ],
                },
            ],
        };

        let serialized = serde_json::to_string(&filter).unwrap();
        let deserialized: Filter = serde_json::from_str(&serialized).unwrap();
        assert_eq!(filter, deserialized);
    }

    #[test]
    fn test_select_serialization() {
        let select = Select::Avg {
            field: "score".into(),
            alias: Some("average_score".into()),
        };

        let serialized = serde_json::to_string(&select).unwrap();
        let deserialized: Select = serde_json::from_str(&serialized).unwrap();
        assert_eq!(select, deserialized);
    }

    #[test]
    fn test_pagination_serialization() {
        let offset_pagination = Pagination::Offset {
            limit: 10,
            offset: Some(20),
        };
        let cursor_pagination = Pagination::Cursor {
            cursor: Some("abc123".into()),
            limit: 50,
            direction: PaginationDirection::Next,
        };

        let serialized_offset = serde_json::to_string(&offset_pagination).unwrap();
        let serialized_cursor = serde_json::to_string(&cursor_pagination).unwrap();

        let deserialized_offset: Pagination = serde_json::from_str(&serialized_offset).unwrap();
        let deserialized_cursor: Pagination = serde_json::from_str(&serialized_cursor).unwrap();

        assert_eq!(offset_pagination, deserialized_offset);
        assert_eq!(cursor_pagination, deserialized_cursor);
    }

    #[test]
    fn test_join_serialization() {
        let join = Join::Inner {
            collection: "orders".into(),
            condition: JoinCondition::On {
                left: "users.id".into(),
                right: "orders.user_id".into(),
            },
        };

        let serialized = serde_json::to_string(&join).unwrap();
        let deserialized: Join = serde_json::from_str(&serialized).unwrap();
        assert_eq!(join, deserialized);
    }

    #[test]
    fn test_aggregation_stage_serialization() {
        let stage = AggregationStage::Group {
            by: vec!["department".into()],
            fields: vec![Select::Count {
                field: "id".into(),
                alias: Some("total".into()),
            }],
        };

        let serialized = serde_json::to_string(&stage).unwrap();
        let deserialized: AggregationStage = serde_json::from_str(&serialized).unwrap();
        assert_eq!(stage, deserialized);
    }

    #[test]
    fn test_full_query_roundtrip() {
        let mut extensions = HashMap::new();
        extensions.insert(
            "sqlite".into(),
            Value::Object(HashMap::from([("explain".into(), Value::Bool(true))])),
        );

        let query = Query {
            collection: "users".into(),
            filters: Some(vec![Filter::Equal {
                field: "active".into(),
                value: true.into(),
            }]),
            selection: Some(vec![
                Select::Field {
                    name: "name".into(),
                    alias: None,
                },
                Select::Count {
                    field: "id".into(),
                    alias: Some("total".into()),
                },
            ]),
            ordering: Some(vec![OrderBy {
                field: "name".into(),
                direction: OrderDirection::Asc,
            }]),
            pagination: Some(Pagination::Offset {
                limit: 10,
                offset: Some(0),
            }),
            joins: Some(vec![]),
            aggregations: Some(vec![]),
            extensions: Some(extensions),
        };

        let serialized = serde_json::to_string(&query).unwrap();
        let deserialized: Query = serde_json::from_str(&serialized).unwrap();
        assert_eq!(query.collection, deserialized.collection);
        assert_eq!(query.filters, deserialized.filters);
        assert_eq!(query.selection, deserialized.selection);

        let now = SystemTime::now();
        let value: Value = now.into();
        match value {
            Value::DateTime(dt) => {
                let converted_time: SystemTime = dt.into();
                let diff = now
                    .duration_since(converted_time)
                    .unwrap_or_else(|_| converted_time.duration_since(now).unwrap());
                assert!(diff.as_nanos() < 1_000_000); // Less than 1 millisecond difference
            }
            _ => panic!("Invalid conversion"),
        }
    }

    #[test]
    fn test_conversion_implementations() {
        // Test From implementations
        let from_str: Value = "hello".into();
        assert!(matches!(from_str, Value::String(s) if s == "hello"));

        let from_i64: Value = (-42i64).into();
        assert!(matches!(from_i64, Value::Number(Number::Integer(-42))));

        let from_u64: Value = 42u64.into();
        assert!(matches!(from_u64, Value::Number(Number::Unsigned(42))));

        let from_f64: Value = 3.14.into();
        assert!(matches!(from_f64, Value::Number(Number::Float(f)) if f == 3.14));

        let from_bool: Value = true.into();
        assert!(matches!(from_bool, Value::Bool(true)));

        let from_vec: Value = vec![1, 2, 3].into();
        assert!(matches!(from_vec, Value::Array(arr) if arr.len() == 3));

        let mut map = HashMap::new();
        map.insert("key", "value");
        let from_hashmap: Value = map.into();
        match from_hashmap {
            Value::Object(obj) => {
                assert_eq!(obj.get("key").unwrap(), &Value::String("value".into()));
            }
            _ => panic!("Invalid conversion"),
        }

        let from_option: Value = Some(42u64).into(); // Explicitly use u64
        assert!(matches!(from_option, Value::Number(Number::Unsigned(42))));

        let from_none: Value = None::<i32>.into();
        assert!(matches!(from_none, Value::Null));
    }

    #[test]
    fn test_edge_cases() {
        let empty_array = Value::Array(Vec::new());
        assert_eq!(format!("{}", empty_array), "[]");

        let mut empty_obj = HashMap::new();
        empty_obj.insert("".into(), Value::Null);
        let empty_object = Value::Object(empty_obj);
        assert_eq!(format!("{}", empty_object), "{\"\": null}");

        let big_num = Value::Number(Number::Unsigned(u64::MAX));
        assert_eq!(format!("{}", big_num), u64::MAX.to_string());

        let nan = Value::Number(Number::Float(f64::NAN));
        let serialized = serde_json::to_string(&nan).unwrap();
        assert_eq!(serialized, "null");
    }

    #[test]
    fn test_value_json_roundtrip() {
        let values = vec![
            Value::Null,
            Value::Bool(true),
            Value::Bool(false),
            Value::Number(Number::Integer(-42)),
            Value::Number(Number::Unsigned(18446744073709551615)), // u64::MAX
            Value::Number(Number::Float(3.14)),
            Value::String("".into()),
            Value::String("hello\tworld\n".into()),
            Value::String("🦀".into()),
            Value::DateTime(Utc.with_ymd_and_hms(2023, 1, 1, 23, 59, 59).unwrap()),
            Value::Array(vec![Value::Null, Value::Bool(true)]),
            Value::Object(HashMap::from([
                ("key1".into(), Value::Null),
                (
                    "key2".into(),
                    Value::Array(vec![Value::String("nested".into())]),
                ),
                (
                    "key3".into(),
                    Value::Object(HashMap::from([("subkey".into(), Value::Bool(false))])),
                ),
            ])),
        ];

        for value in values {
            let serialized = serde_json::to_string(&value).unwrap();
            let deserialized: Value = serde_json::from_str(&serialized).unwrap();
            assert_eq!(value, deserialized, "Failed roundtrip for {}", serialized);
        }

        // Test NaN separately
        let nan = Value::Number(Number::Float(f64::NAN));
        let serialized = serde_json::to_string(&nan).unwrap();
        let deserialized: Value = serde_json::from_str(&serialized).unwrap();
        assert!(matches!(deserialized, Value::Null));
    }

    #[test]
    fn test_query_with_all_fields() {
        let query = Query {
            collection: "test".into(),
            filters: Some(vec![Filter::Raw {
                dialect: "mongo".into(),
                expression: Value::Object(HashMap::from([(
                    "$where".into(),
                    Value::String("function() { return true; }".into()),
                )])),
            }]),
            selection: Some(vec![Select::Raw {
                dialect: "sql".into(),
                expression: "CAST(age AS FLOAT)".into(),
                alias: Some("age_float".into()),
            }]),
            ordering: Some(vec![OrderBy {
                field: "created_at".into(),
                direction: OrderDirection::Desc,
            }]),
            pagination: Some(Pagination::Cursor {
                cursor: Some("last_id".into()),
                limit: 100,
                direction: PaginationDirection::Next,
            }),
            joins: Some(vec![Join::Inner {
                collection: "profiles".into(),
                condition: JoinCondition::Raw {
                    expression: "users.id = profiles.user_id".into(),
                },
            }]),
            aggregations: Some(vec![AggregationStage::Raw {
                stage: Value::Object(HashMap::from([(
                    "$addFields".into(),
                    Value::Object(HashMap::from([(
                        "newField".into(),
                        Value::String("value".into()),
                    )])),
                )])),
            }]),
            extensions: Some(HashMap::from([
                ("audit".into(), Value::Bool(true)),
                (
                    "metrics".into(),
                    Value::Object(HashMap::from([
                        ("level".into(), Value::String("detailed".into())),
                        ("sampling".into(), Value::Number(Number::Float(0.1))),
                    ])),
                ),
            ])),
        };

        let serialized = serde_json::to_string(&query).unwrap();
        let deserialized: Query = serde_json::from_str(&serialized).unwrap();
        assert_eq!(query, deserialized);
    }

    #[test]
    fn test_edge_case_serialization() {
        // Test empty collections
        let empty_query = Query {
            collection: "empty".into(),
            filters: None,
            selection: None,
            ordering: None,
            pagination: None,
            joins: None,
            aggregations: None,
            extensions: None,
        };

        let serialized_empty = serde_json::to_string(&empty_query).unwrap();
        assert_eq!(
            serialized_empty,
            r#"{"collection":"empty","filters":null,"selection":null,"ordering":null,"pagination":null,"joins":null,"aggregations":null,"extensions":null}"#
        );

        // Test maximum numeric values
        let max_values = Value::Array(vec![
            Value::Number(Number::Integer(i64::MAX)),
            Value::Number(Number::Unsigned(u64::MAX)),
            Value::Number(Number::Float(f64::MAX)),
        ]);

        let serialized_max = serde_json::to_string(&max_values).unwrap();
        let deserialized_max: Value = serde_json::from_str(&serialized_max).unwrap();
        match deserialized_max {
            Value::Array(arr) => {
                assert_eq!(arr[0], Value::Number(Number::Integer(i64::MAX)));
                assert_eq!(arr[1], Value::Number(Number::Unsigned(u64::MAX)));
                assert_eq!(arr[2], Value::Number(Number::Float(f64::MAX)));
            }
            _ => panic!("Invalid deserialization"),
        }

        // Test special characters in strings
        let special_string = Value::String("\0\x1F\t\n\r\"\\".into());
        let serialized_str = serde_json::to_string(&special_string).unwrap();
        assert_eq!(serialized_str, r#""\u0000\u001f\t\n\r\"\\""#);
    }

    #[test]
    fn test_filter_variants() {
        let filters = vec![
            Filter::NotEqual {
                field: "status".into(),
                value: Value::String("archived".into()),
            },
            Filter::GreaterThan {
                field: "age".into(),
                value: Value::Number(Number::Integer(18)),
            },
            Filter::LessThan {
                field: "price".into(),
                value: Value::Number(Number::Float(99.99)),
            },
            Filter::Contains {
                field: "tags".into(),
                value: Value::String("sale".into()),
            },
            Filter::Exists {
                field: "updated_at".into(),
            },
            Filter::ElemMatch {
                field: "comments".into(),
                conditions: vec![Filter::GreaterThan {
                    field: "likes".into(),
                    value: Value::Number(Number::Integer(100)),
                }],
            },
        ];

        for filter in filters {
            let serialized = serde_json::to_string(&filter).unwrap();
            let deserialized: Filter = serde_json::from_str(&serialized).unwrap();
            assert_eq!(filter, deserialized);
        }
    }

    #[test]
    fn test_select_aliases() {
        let selects = vec![
            Select::Field {
                name: "username".into(),
                alias: Some("user".into()),
            },
            Select::Count {
                field: "id".into(),
                alias: None,
            },
            Select::Sum {
                field: "amount".into(),
                alias: Some("total".into()),
            },
            Select::Project {
                expression: "price * quantity".into(),
                alias: Some("total".into()),
            },
        ];

        for select in selects {
            let serialized = serde_json::to_string(&select).unwrap();
            let deserialized: Select = serde_json::from_str(&serialized).unwrap();
            assert_eq!(select, deserialized);
        }
    }

    #[test]
    fn test_join_conditions() {
        let joins = vec![
            Join::Inner {
                collection: "profiles".into(),
                condition: JoinCondition::On {
                    left: "users.id".into(),
                    right: "profiles.user_id".into(),
                },
            },
            Join::Left {
                collection: "logs".into(),
                condition: JoinCondition::Using {
                    field: "session_id".into(),
                },
            },
            Join::ManyToMany {
                target: "orders".into(),
                through: "order_items".into(),
                from_key: "user_id".into(),
                to_key: "product_id".into(),
                main_id: "user_id".into(),
                target_id: "product_id".into(),
            },
        ];

        for join in joins {
            let serialized = serde_json::to_string(&join).unwrap();
            let deserialized: Join = serde_json::from_str(&serialized).unwrap();
            assert_eq!(join, deserialized);
        }
    }

    #[test]
    fn test_aggregation_stages() {
        let stages = vec![
            AggregationStage::Group {
                by: vec!["department".into()],
                fields: vec![Select::Count {
                    field: "id".into(),
                    alias: Some("employees".into()),
                }],
            },
            AggregationStage::Raw {
                stage: Value::Object(HashMap::from([(
                    "$lookup".into(),
                    Value::Object(HashMap::from([
                        ("from".into(), Value::String("orders".into())),
                        ("localField".into(), Value::String("_id".into())),
                        ("foreignField".into(), Value::String("user_id".into())),
                        ("as".into(), Value::String("orders".into())),
                    ])),
                )])),
            },
        ];

        for stage in stages {
            let serialized = serde_json::to_string(&stage).unwrap();
            let deserialized: AggregationStage = serde_json::from_str(&serialized).unwrap();
            assert_eq!(stage, deserialized);
        }
    }

    #[test]
    fn test_all_value_types() {
        let values = vec![
            Value::DateTime(DateTime::from(SystemTime::now())),
            Value::Null,
        ];

        for value in values {
            let serialized = serde_json::to_string(&value).unwrap();
            let deserialized: Value = serde_json::from_str(&serialized).unwrap();
            assert_eq!(value, deserialized);
        }
    }

    #[test]
    fn test_security_edge_cases() {
        // Test very long strings
        let long_string = Value::String("a".repeat(1_000_000).into());
        let serialized_long = serde_json::to_string(&long_string).unwrap();
        let deserialized_long: Value = serde_json::from_str(&serialized_long).unwrap();
        assert_eq!(long_string, deserialized_long);

        // Test deeply nested structures (100 levels deep)
        let mut deep_value = Value::Object(HashMap::new());
        for i in 0..100 {
            let mut inner = HashMap::new();
            inner.insert(format!("level_{i}"), deep_value);
            deep_value = Value::Object(inner);
        }
        let serialized_deep = serde_json::to_string(&deep_value).unwrap();
        let deserialized_deep: Value = serde_json::from_str(&serialized_deep).unwrap();
        assert_eq!(deep_value, deserialized_deep);

        // Test large array sizes
        let large_array = Value::Array(vec![Value::Null; 100_000]);
        let serialized_large = serde_json::to_string(&large_array).unwrap();
        let deserialized_large: Value = serde_json::from_str(&serialized_large).unwrap();
        assert_eq!(large_array, deserialized_large);

        // Test recursive filter structures
        let mut deep_filter = Filter::Equal {
            field: "base".into(),
            value: 0.into(),
        };
        for i in 0..50 {
            deep_filter = Filter::And {
                conditions: vec![
                    deep_filter,
                    Filter::Equal {
                        field: format!("level_{i}"),
                        value: i.into(),
                    },
                ],
            };
        }
        let serialized_filter = serde_json::to_string(&deep_filter).unwrap();
        let deserialized_filter: Filter = serde_json::from_str(&serialized_filter).unwrap();
        assert_eq!(deep_filter, deserialized_filter);
    }

    #[test]
    fn test_invalid_filters() {
        // Unknown filter variant
        let json = r#"{"InvalidVariant": {"field": "name"}}"#;
        let result = serde_json::from_str::<Filter>(json);
        assert!(result.is_err(), "Should reject unknown filter variants");

        // Malicious nested structure
        let malicious_json = format!(
            r#"{{"And": {{"conditions": [{}]}}}}"#,
            "[null]".repeat(1000)
        );
        let result = serde_json::from_str::<Filter>(&malicious_json);
        assert!(result.is_err(), "Should reject overly nested filters");

        // Invalid raw filter
        let invalid_raw_json = r#"{"Raw": {"dialect": 42, "expression": "SELECT *"}}"#;
        let result = serde_json::from_str::<Filter>(invalid_raw_json);
        assert!(
            result.is_err(),
            "Should reject invalid raw filter structure"
        );
    }

    #[test]
    fn test_invalid_selects() {
        // Type mismatch in Sum selector
        let json = r#"{"Sum": {"field": 123, "alias": true}}"#;
        let result = serde_json::from_str::<Select>(json);
        assert!(result.is_err(), "Should reject invalid select types");

        // Invalid projection format
        let invalid_proj_json = r#"{"Project": {"expression": 42, "alias": null}}"#;
        let result = serde_json::from_str::<Select>(invalid_proj_json);
        assert!(result.is_err(), "Should reject non-string projections");
    }

    #[test]
    fn test_invalid_joins() {
        // Invalid collection type
        let json = r#"{"Inner": {"collection": 42, "condition": {}}}"#;
        let result = serde_json::from_str::<Join>(json);
        assert!(result.is_err(), "Should reject non-string collection names");

        // Malformed ManyToMany join
        let invalid_m2m_json = r#"{"ManyToMany": {"target": 123}}"#;
        let result = serde_json::from_str::<Join>(invalid_m2m_json);
        assert!(
            result.is_err(),
            "Should reject invalid ManyToMany structure"
        );
    }

    #[test]
    fn test_invalid_values() {
        // Invalid datetime format (should be RFC3339)
        let json = r#""2023-13-45T25:61:61Z""#; // Invalid date components
        let result = serde_json::from_str::<Value>(json);

        // check if the result is not a Value::DateTime and if it is, check if it is valid
        if let Ok(value) = result {
            if let Value::DateTime(_) = value {
                assert!(false, "Should reject invalid datetime strings");
            }
        }

        // Correct format but invalid numbers
        let invalid_num_json = r#"{"Number": {"Float": "nan"}}"#;
        let result = serde_json::from_str::<Value>(invalid_num_json);
        if let Ok(value) = result {
            if let Value::Number(_) = value {
                assert!(false, "Should reject invalid number representations");
            }
        }
    }

    #[test]
    fn test_invalid_aggregations() {
        // Invalid group by field
        let json = r#"{"Group": {"by": [123], "fields": []}}"#;
        let result = serde_json::from_str::<AggregationStage>(json);
        assert!(result.is_err(), "Should reject non-string group by fields");

        // Malformed raw stage
        let invalid_raw_json = r#"{"Raw": {"stage": "invalid"}}"#;
        let result = serde_json::from_str::<AggregationStage>(invalid_raw_json);
        assert!(result.is_err(), "Should reject invalid raw stages");
    }

    #[test]
    fn test_invalid_queries() {
        // Type mismatch in collection
        let json = r#"{"collection": 42, "filters": "not-an-array"}"#;
        let result = serde_json::from_str::<Query>(json);
        assert!(result.is_err(), "Should reject non-string collection names");

        // Invalid extension format
        let invalid_ext_json = r#"{"extensions": {"sql": "invalid"}}"#;
        let result = serde_json::from_str::<Query>(invalid_ext_json);
        assert!(result.is_err(), "Should reject malformed extensions");
    }

    #[test]
    fn test_invalid_numbers() {
        // Exceeds u64::MAX
        let json = r#"{"Integer": 18446744073709551616}"#;
        let result = serde_json::from_str::<Number>(json);
        assert!(result.is_err(), "Should reject out-of-range integers");

        // Invalid float representation
        let invalid_float_json = r#"{"Float": "nan"}"#;
        let result = serde_json::from_str::<Number>(invalid_float_json);
        assert!(result.is_err(), "Should reject non-numeric float values");
    }

    #[test]
    fn test_invalid_pagination() {
        // Type mismatch in cursor limit
        let json = r#"{"Cursor": {"limit": "100"}}"#;
        let result = serde_json::from_str::<Pagination>(json);
        assert!(result.is_err(), "Should reject non-integer limits");

        // Invalid offset combination
        let invalid_offset_json = r#"{"Offset": {"limit": "10"}}"#;
        let result = serde_json::from_str::<Pagination>(invalid_offset_json);
        assert!(result.is_err(), "Should reject invalid offset types");
    }

    #[test]
    fn test_empty_collections() {
        // Test empty arrays and objects
        let empty_array = Value::Array(vec![]);
        let empty_obj = HashMap::new();
        let empty_object = Value::Object(empty_obj);

        assert_eq!(format!("{}", empty_array), "[]");
        assert_eq!(format!("{}", empty_object), "{}");
    }

    #[test]
    fn test_max_depth_structures() {
        // Test exactly at maximum allowed depth (if applicable)
        let mut value = Value::Null;
        for _ in 0..128 {
            // Test common recursion limit
            value = Value::Array(vec![value]);
        }
        let result = serde_json::to_string(&value);
        assert!(result.is_ok(), "Should handle max depth structures");
    }

    #[test]
    fn test_string_edge_cases() {
        // Test special characters
        let special_string = Value::String("\0\t\n\r\"\\".into());
        let escaped = serde_json::to_string(&special_string).unwrap();
        assert_eq!(escaped, r#""\u0000\t\n\r\"\\""#);

        // Test non-UTF8 sequences (should be rejected at higher layers)
        let valid_utf8 = Value::String("🦀".into());
        let serialized = serde_json::to_string(&valid_utf8).unwrap();
        assert_eq!(serialized, r#""🦀""#);
    }

    #[test]
    fn test_pagination_edge_cases() {
        // Test maximum limit value
        let max_limit = Pagination::Offset {
            limit: u32::MAX as u64,
            offset: None,
        };
        let serialized = serde_json::to_string(&max_limit).unwrap();
        let deserialized: Pagination = serde_json::from_str(&serialized).unwrap();
        assert_eq!(max_limit, deserialized);

        // Test invalid cursor format
        let invalid_cursor_json = r#"{"Cursor": {"cursor": 123}}"#;
        let result = serde_json::from_str::<Pagination>(invalid_cursor_json);
        assert!(result.is_err(), "Should reject non-string cursors");
    }

    #[test]
    fn test_http_query_conversion() {
        let http_query = HttpQuery {
            filters: Some(vec![HttpFilter::Equal {
                field: "status".into(),
                value: "active".into(),
            }]),
            selection: Some(vec![HttpSelect::Count {
                field: "id".into(),
                alias: Some("total".into()),
            }]),
            ordering: Some(vec![HttpOrderBy {
                field: "created_at".into(),
                direction: HttpSortDirection::Desc,
            }]),
            pagination: Some(HttpPagination::Offset {
                limit: 10,
                offset: Some(20),
            }),
        };

        let query: Query = http_query.try_into().unwrap();
        
        assert_eq!(query.collection, "");
        assert_eq!(query.filters.unwrap().len(), 1);
        assert_eq!(query.selection.unwrap().len(), 1);
        assert_eq!(query.ordering.unwrap().len(), 1);
        assert!(matches!(query.pagination.unwrap(), Pagination::Offset { limit: 10, offset: Some(20) }));
    }

    #[test]
    fn test_http_filter_conversion() {
        let http_filter = HttpFilter::And {
            conditions: vec![
                HttpFilter::GreaterThan {
                    field: "age".into(),
                    value: 18.into(),
                },
                HttpFilter::Or {
                    conditions: vec![
                        HttpFilter::Exists { field: "email".into() },
                        HttpFilter::In {
                            field: "tags".into(),
                            values: vec!["admin".into(), "user".into()],
                        },
                    ],
                },
            ],
        };

        let filter: Filter = http_filter.try_into().unwrap();
        
        assert!(matches!(filter, Filter::And { conditions } if conditions.len() == 2));
    }

    #[test]
    fn test_http_select_conversion() {
        let http_select = HttpSelect::Avg {
            field: "score".into(),
            alias: Some("average".into()),
        };

        let select: Select = http_select.try_into().unwrap();
        
        assert!(matches!(select, Select::Avg { field, alias } 
            if field == "score" && alias == Some("average".into())));
    }

    #[test]
    fn test_http_order_by_conversion() {
        let http_order = HttpOrderBy {
            field: "name".into(),
            direction: HttpSortDirection::Asc,
        };

        let order: OrderBy = http_order.try_into().unwrap();
        
        assert_eq!(order.field, "name");
        assert!(matches!(order.direction, OrderDirection::Asc));
    }

    #[test]
    fn test_http_pagination_conversion() {
        let http_pagination = HttpPagination::Cursor {
            cursor: Some("abc123".into()),
            limit: 50,
            direction: PaginationDirection::Next,
        };

        let pagination: Pagination = http_pagination.try_into().unwrap();
        
        assert!(matches!(pagination, Pagination::Cursor { cursor, limit: 50, direction: PaginationDirection::Next }
            if cursor == Some("abc123".into())));
    }

    #[test]
    fn test_empty_http_query_conversion() {
        let http_query = HttpQuery::default();
        let query: Query = http_query.try_into().unwrap();
        
        assert!(query.filters.is_none());
        assert!(query.selection.is_none());
        assert!(query.ordering.is_none());
        assert!(query.pagination.is_none());
    }

    #[test]
    fn test_complex_filter_conversion() {
        let http_filter = HttpFilter::Or {
            conditions: vec![
                HttpFilter::And {
                    conditions: vec![
                        HttpFilter::Equal {
                            field: "role".into(),
                            value: "admin".into(),
                        },
                        HttpFilter::GreaterThan {
                            field: "clearance".into(),
                            value: 5.into(),
                        },
                    ],
                },
                HttpFilter::In {
                    field: "departments".into(),
                    values: vec!["engineering".into(), "security".into()],
                },
            ],
        };

        let filter: Filter = http_filter.try_into().unwrap();
        
        if let Filter::Or { conditions } = filter {
            assert!(matches!(&conditions[0], Filter::And { conditions } if conditions.len() == 2));
            assert!(matches!(&conditions[1], Filter::In { field, values } 
                if field == "departments" && values.len() == 2));
        }
    }
}