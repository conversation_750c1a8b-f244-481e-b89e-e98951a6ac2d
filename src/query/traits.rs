use crate::Query;

pub trait IdProvider {
    fn get_id(&self) -> String;
}

pub trait PreExecutor: IdProvider {
    fn pre_insert(&mut self, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }

    fn pre_update(&mut self, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }

    fn pre_delete(&mut self, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }

    fn pre_get(_id: &str, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }

    fn pre_get_list(_query: &Query, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }
}

pub trait PostExecutor: IdProvider {
    fn post_insert(&mut self, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }

    fn post_update(&mut self, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }

    fn post_delete(&mut self, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }

    fn post_get(&mut self, _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError> {
        Ok(())
    }

    fn post_get_list(_results: &[Self], _tx: &rusqlite::Transaction) -> Result<(), crate::SqlError>
    where
        Self: Sized,
    {
        Ok(())
    }
}

pub trait DataSanitizer {
    fn sanitize(&mut self);
}
