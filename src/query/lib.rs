//! A SQL query builder and database abstraction with support for multiple dialects
//!
//! # Features
//!
//! - **Multi-database support**: SQLite, PostgreSQL, MySQL
//! - **Type-safe query building**: Construct complex queries using Rust types
//! - **Migrations system**: Version-controlled schema migrations
//! - **Security features**: Configurable limits, query validation, and cursor pagination
//! - **Database abstraction**: Unified interface across different databases
//!
//! # Quick Start
//!
//! Add to your `Cargo.toml`:
//! ```toml
//! [dependencies]
//! db = { version = "0.1" }
//! ```
//!
//! Basic usage:
//! ```rust
//! use db::{Database, SecurityConfig, Query, QueryBuilder};
//! use db::adapters::SQLiteAdapter;
//! use db::query_builder::SQLiteDialect;
//! use db::models::Filter;
//! use db::Pagination;
//! use std::time::Duration;
//! use std::path::Path;
//! use db::Migration;
//! use std::collections::HashMap;
//!
//! # fn main() -> anyhow::Result<()> {
//! // Initialize configuration
//! let config = SecurityConfig {
//!     max_pool_size: 10,
//!     connection_timeout: Duration::from_secs(30),
//!     migration_table: "migrations".into(),
//!     cursor_expiry: Duration::from_secs(3600),
//!     hmac_secret: "<super-secret-key>".into(),
//!     max_query_depth: 5,
//!     max_page_size: 100,
//!     max_string_length: 1000,
//!     max_array_size: 50,
//!     ..SecurityConfig::default()
//! };
//!
//! // Create database connection
//! let db = Database::<SQLiteAdapter>::new("file::memory:", &config)?;
//!
//! // Run migrations
//! let mut migrations_map = HashMap::new();
//! migrations_map.insert("001_create_users.sql".to_string(), "-- migrate:up\nCREATE TABLE users (id INTEGER PRIMARY KEY);\n-- migrate:down\nDROP TABLE users;".to_string());
//! let migrations = Migration::load_from_map(migrations_map)?;
//! db.run_migrations(migrations)?;
//!
//! // Build a query
//! let query = Query {
//!     collection: "users".into(),
//!     filters: Some(vec![Filter::Equal {
//!         field: "active".into(),
//!         value: true.into(),
//!     }]),
//!     pagination: Some(Pagination::Offset { limit: 10, offset: None }),
//!     ..Default::default()
//! };
//!
//! let dialect = SQLiteDialect {};
//! let builder = QueryBuilder::new(&query, &dialect);
//! let (sql, params) = builder.build()?;
//! # Ok(())
//! # }
//! ```
//!
//! # Database Adapters
//!
//! ## SQLite
//! ```rust,no_run
//! use db::{Database, SecurityConfig};
//! use db::adapters::SQLiteAdapter;
//!
//! let config = SecurityConfig::default();
//! let db = Database::<SQLiteAdapter>::new("sqlite.db", &config).unwrap();
//! ```
//!
//! ## PostgreSQL
//! ```rust,no_run
//! use db::{Database, SecurityConfig};
//! use db::adapters::PostgresAdapter;
//!
//! let config = SecurityConfig::default();
//! let db = Database::<PostgresAdapter>::new("postgres://user:pass@localhost/db", &config).unwrap();
//! ```
//!
//! ## MySQL
//! ```rust,no_run
//! use db::{Database, SecurityConfig};
//! use db::adapters::MySQLAdapter;
//!
//! let config = SecurityConfig::default();
//! let db = Database::<MySQLAdapter>::new("mysql://user:pass@localhost/db", &config).unwrap();
//! ```
//!
//! # Query Building
//!
//! ```rust
//! use db::{Query, QueryBuilder};
//! use db::models::{Filter, OrderBy, OrderDirection};
//! use db::{Pagination, PaginationDirection};
//! use db::query_builder::SQLiteDialect;
//!
//! let query = Query {
//!     collection: "posts".into(),
//!     filters: Some(vec![
//!         Filter::Contains {
//!             field: "title".into(),
//!             value: "Rust".into(),
//!         },
//!         Filter::GreaterThan {
//!             field: "views".into(),
//!             value: 1000.into(),
//!         }
//!     ]),
//!     ordering: Some(vec![OrderBy {
//!         field: "created_at".into(),
//!         direction: OrderDirection::Desc,
//!     }]),
//!     pagination: Some(Pagination::Cursor {
//!         cursor: Some("cursor_string".into()),
//!         limit: 20,
//!         direction: PaginationDirection::Next,
//!     }),
//!     ..Default::default()
//! };
//!
//! let dialect = SQLiteDialect {};
//! let builder = QueryBuilder::new(&query, &dialect);
//! let (sql, params) = builder.build().unwrap();
//! ```
//!
//! # Security & Validation
//!
//! ```rust
//! use db::{QueryValidator, SecurityConfig};
//! use db::models::HttpQuery;
//! use db::models::HttpFilter;
//!
//! // Define allowed fields that can be queried
//! let allowed_fields = &["id", "name", "email", "created_at"];
//!
//! // Create a security configuration
//! let config = SecurityConfig::default();
//!
//! // Create the validator
//! let validator = QueryValidator::new(allowed_fields, config);
//!
//! // Validate user input
//! let user_query = HttpQuery {
//!     filters: Some(vec![
//!         HttpFilter::Equal {
//!             field: "email".into(),
//!             value: "<EMAIL>".into()
//!         }
//!     ]),
//!     ..Default::default()
//! };
//!
//! // This will validate the query and convert it to a safe internal Query
//! let safe_query = validator.validate_query(&user_query);
//! // Now you can use safe_query to build and execute a safe SQL query
//! if safe_query.is_ok() {
//!     // Proceed with building and executing the query
//! } else {
//!     // Handle validation errors
//! }
//! ```

pub(crate) mod test_logger {
    use std::sync::Once;
    #[allow(dead_code)] // This is used in tests
    static INIT: Once = Once::new();

    #[allow(dead_code)] // This is used in tests
    pub(crate) fn setup() {
        INIT.call_once(|| {
            let result =
                env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info"))
                    .try_init();

            if let Err(e) = result {
                // Use eprintln since logger might not be available
                eprintln!("Failed to initialize logger: {}", e);
            }
        });
    }
}

pub mod adapters;
pub mod config;
pub mod database;
pub mod error;
pub mod migrations;
pub mod models;
pub mod query_builder;
pub mod traits;
pub mod utils;
pub mod validation;

pub use config::SecurityConfig;
pub use database::Database;
pub use error::*;
pub use migrations::Migration;
pub use models::{
    AggregationStage, Filter, Join, JoinCondition, Number, OrderBy, OrderDirection, Pagination,
    PaginationDirection, Query, Select, Value,
};
pub use models::{HttpFilter, HttpOrderBy, HttpPagination, HttpQuery, HttpSelect, ValidationError};
pub use query_builder::*;
pub use validation::QueryValidator;
pub mod global;
pub use adapters::*;
pub use global::*;
pub use models::*;
pub use traits::*;
pub use utils::*;
