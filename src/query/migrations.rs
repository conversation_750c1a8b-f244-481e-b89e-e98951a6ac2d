//! Migration file parsing and version management
//!
//! This module provides:
//! - Version-controlled schema changes
//! - Transactional migration execution
//! - Up/down migration support
//! - File-based migration history
use anyhow::Result;
use log::error;
use rusqlite::Transaction;
use std::path::Path;

/// Represents a database migration with version control
///
/// # Fields
/// - `version`: Migration version number (must be unique)
/// - `description`: Human-readable description of the migration
/// - `up`: SQL commands to apply the migration
/// - `down`: SQL commands to rollback the migration
#[derive(Debug, Clone)]
pub struct Migration {
    /// Sequential migration version
    pub version: i64,
    /// Human-readable description
    pub description: String,
    /// SQL to apply migration
    pub up: String,
    /// SQL to revert migration
    pub down: String,
}

impl Migration {
    /// Load migrations from a directory containing SQL files
    ///
    /// # Arguments
    /// * `path` - Path to directory containing migration files
    ///
    /// # Returns
    /// `Result<Vec<Self>>` - Sorted list of migrations
    ///
    /// # Errors
    /// Returns an error if:
    /// - Directory cannot be read
    /// - Migration files are invalid
    /// - Duplicate versions are found
    ///
    /// # File Format
    /// - Filename: VERSION_DESCRIPTION.sql
    /// - Content:
    ///   -- migrate:up
    ///   CREATE TABLE ...;
    ///   
    ///   -- migrate:down
    ///   DROP TABLE ...;
    pub fn load_from_dir(path: impl AsRef<Path>) -> Result<Vec<Self>> {
        let mut migrations = Vec::new();
        let path = path.as_ref();

        for entry in std::fs::read_dir(path)? {
            let entry = entry?;
            let path = entry.path();

            if path.extension().and_then(|s| s.to_str()) == Some("sql") {
                let sql = std::fs::read_to_string(&path)?;

                let (up, down) = parse_migration_file(&sql)
                    .inspect_err(|e| error!("Failed to parse migration file {:?}: {}", path, e))?;

                let migration = Migration {
                    version: parse_version_from_filename(&path).inspect_err(|e| {
                        error!("Invalid migration filename format in {:?}: {}", path, e)
                    })?,
                    description: parse_description_from_filename(&path).inspect_err(|e| {
                        error!("Invalid migration description in {:?}: {}", path, e)
                    })?,
                    up,
                    down,
                };

                migrations.push(migration);
            }
        }

        migrations.sort_by_key(|m| m.version);

        // Check for duplicate versions
        let mut prev_version = None;
        for migration in &migrations {
            if let Some(prev) = prev_version {
                if migration.version == prev {
                    return Err(anyhow::anyhow!("Duplicate migration version {}", prev));
                }
            }
            prev_version = Some(migration.version);
        }

        Ok(migrations)
    }

    /// Load migrations from a HashMap of filename to SQL content
    ///
    /// # Arguments
    /// * `migrations_map` - HashMap where key is filename (e.g. "001_create_users.sql") and value is SQL content
    ///
    /// # Returns
    /// `Result<Vec<Self>>` - Sorted list of migrations
    ///
    /// # Errors
    /// Returns an error if:
    /// - Migration content is invalid
    /// - Duplicate versions are found
    /// - Filename format is invalid
    ///
    /// # Example
    /// ```rust
    /// use std::collections::HashMap;
    /// use db::Migration;
    ///
    /// let mut migrations_map = HashMap::new();
    /// migrations_map.insert("001_create_users.sql".to_string(), "-- migrate:up\nCREATE TABLE users (id INTEGER PRIMARY KEY);\n-- migrate:down\nDROP TABLE users;".to_string());
    /// let migrations = Migration::load_from_map(migrations_map);
    /// ```
    pub fn load_from_map(
        migrations_map: std::collections::HashMap<String, String>,
    ) -> Result<Vec<Self>> {
        let mut migrations = Vec::new();

        for (filename, sql) in migrations_map {
            let path = Path::new(&filename);
            let (up, down) = parse_migration_file(&sql).inspect_err(|e| {
                error!("Failed to parse migration content for {}: {}", filename, e)
            })?;

            let migration = Migration {
                version: parse_version_from_filename(path).inspect_err(|e| {
                    error!("Invalid migration filename format in {}: {}", filename, e)
                })?,
                description: parse_description_from_filename(path).inspect_err(|e| {
                    error!("Invalid migration description in {}: {}", filename, e)
                })?,
                up,
                down,
            };

            migrations.push(migration);
        }

        migrations.sort_by_key(|m| m.version);

        // Check for duplicate versions
        let mut prev_version = None;
        for migration in &migrations {
            if let Some(prev) = prev_version {
                if migration.version == prev {
                    return Err(anyhow::anyhow!("Duplicate migration version {}", prev));
                }
            }
            prev_version = Some(migration.version);
        }

        Ok(migrations)
    }

    /// Apply this migration within a transaction
    ///
    /// # Arguments
    /// * `transaction` - Active database transaction
    ///
    /// # Returns
    /// `Result<()>` - Success if migration is applied
    pub fn apply(&self, transaction: &Transaction<'_>) -> Result<()> {
        transaction.execute_batch(&self.up)?;
        Ok(())
    }

    /// Roll back this migration within a transaction
    ///
    /// # Arguments
    /// * `transaction` - Active database transaction
    ///
    /// # Returns
    /// `Result<()>` - Success if migration is rolled back
    pub fn rollback(&self, transaction: &Transaction<'_>) -> Result<()> {
        transaction.execute_batch(&self.down)?;
        Ok(())
    }
}

/// Parse a migration file into up and down SQL sections
///
/// # Arguments
/// * `contents` - Contents of the migration SQL file
///
/// # Returns
/// `Result<(String, String)>` - Tuple of (up_sql, down_sql)
///
/// # Errors
/// Returns an error if:
/// - File is missing up or down sections
/// - File format is invalid
pub(crate) fn parse_migration_file(contents: &str) -> Result<(String, String)> {
    let mut up = String::new();
    let mut down = String::new();
    let mut current_section = None;

    for line in contents.lines() {
        if line.trim().starts_with("-- migrate:up") {
            current_section = Some(&mut up);
        } else if line.trim().starts_with("-- migrate:down") {
            current_section = Some(&mut down);
        } else if let Some(section) = &mut current_section {
            section.push_str(line);
            section.push('\n');
        }
    }

    // Validate sections after parsing
    let up = up.trim().to_string();
    let down = down.trim().to_string();

    if up.is_empty() {
        return Err(anyhow::anyhow!("Migration missing 'up' section"));
    }
    if down.is_empty() {
        return Err(anyhow::anyhow!("Migration missing 'down' section"));
    }

    Ok((up, down))
}

/// Parse version number from migration filename
///
/// # Arguments
/// * `path` - Path to migration file
///
/// # Returns
/// `Result<i64>` - Version number
///
/// # Errors
/// Returns an error if filename format is invalid
///
/// # Example
/// For filename "001_create_users.sql", returns 1
pub(crate) fn parse_version_from_filename(path: &Path) -> Result<i64> {
    path.file_stem()
        .and_then(|s| s.to_str())
        .and_then(|s| s.split_once('_'))
        .and_then(|(version, _)| version.parse().ok())
        .ok_or_else(|| anyhow::anyhow!("Invalid migration filename format"))
}

/// Parse description from migration filename
///
/// # Arguments
/// * `path` - Path to migration file
///
/// # Returns
/// `Result<String>` - Human-readable description
///
/// # Errors
/// Returns an error if filename format is invalid
///
/// # Example
/// For filename "001_create_users.sql", returns "create users"
pub(crate) fn parse_description_from_filename(path: &Path) -> Result<String> {
    path.file_stem()
        .and_then(|s| s.to_str())
        .and_then(|s| s.split_once('_'))
        .map(|(_, desc)| desc.replace('_', " ").trim().to_string())
        .ok_or_else(|| anyhow::anyhow!("Invalid migration filename format"))
}

#[cfg(test)]
mod tests {
    use super::Migration;
    use super::{
        parse_description_from_filename, parse_migration_file, parse_version_from_filename,
    };
    use anyhow::Result;
    use std::collections::HashMap;
    use std::fs::File;
    use std::io::Write;
    use std::path::Path;
    use tempfile::tempdir;

    #[test]
    fn test_parse_valid_version() {
        let path = Path::new("001_create_users.sql");
        assert_eq!(parse_version_from_filename(path).unwrap(), 1);

        let path = Path::new("123_another_migration.sql");
        assert_eq!(parse_version_from_filename(path).unwrap(), 123);
    }

    #[test]
    fn test_parse_invalid_version() {
        let path = Path::new("invalid_filename.sql");
        assert!(parse_version_from_filename(path).is_err());

        let path = Path::new("abc_create_users.sql");
        assert!(parse_version_from_filename(path).is_err());

        let path = Path::new("001.sql");
        assert!(parse_version_from_filename(path).is_err());
    }

    #[test]
    fn test_parse_valid_description() {
        let path = Path::new("001_create_users.sql");
        assert_eq!(
            parse_description_from_filename(path).unwrap(),
            "create users"
        );

        let path = Path::new("002_add_email_column.sql");
        assert_eq!(
            parse_description_from_filename(path).unwrap(),
            "add email column"
        );
    }

    #[test]
    fn test_parse_invalid_description() {
        let path = Path::new("001.sql");
        assert!(parse_description_from_filename(path).is_err());
    }

    #[test]
    fn test_parse_migration_file_valid() {
        let sql = r#"
-- migrate:up
CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT);
-- migrate:down
DROP TABLE users;
        "#;

        let (up, down) = parse_migration_file(sql).unwrap();
        assert_eq!(
            up.trim(),
            "CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT);"
        );
        assert_eq!(down.trim(), "DROP TABLE users;");
    }

    #[test]
    fn test_parse_migration_file_missing_sections() {
        let sql = "-- migrate:up\nCREATE TABLE users;";
        assert!(parse_migration_file(sql).is_err());

        let sql = "-- migrate:down\nDROP TABLE users;";
        assert!(parse_migration_file(sql).is_err());

        let sql = "CREATE TABLE users;";
        assert!(parse_migration_file(sql).is_err());

        let sql = "-- migrate:up\n\n-- migrate:down\nDROP TABLE users;";
        assert!(parse_migration_file(sql).is_err());

        let sql = "-- migrate:up\nCREATE TABLE users;\n-- migrate:down\n";
        assert!(parse_migration_file(sql).is_err());
    }

    #[test]
    fn test_parse_migration_file_empty_sections() {
        let sql = "-- migrate:up\n-- migrate:down";
        assert!(parse_migration_file(sql).is_err());

        // Test with whitespace in up section
        let sql = "-- migrate:up\n  \t\n-- migrate:down\nDROP TABLE;";
        assert!(parse_migration_file(sql).is_err());

        // Test with whitespace in down section
        let sql = "-- migrate:up\nCREATE TABLE;\n-- migrate:down\n  ";
        assert!(parse_migration_file(sql).is_err());

        // Test with whitespace in both sections
        let sql = "-- migrate:up\n \n-- migrate:down\n ";
        assert!(parse_migration_file(sql).is_err());
    }

    #[test]
    fn test_load_migrations_from_dir() -> Result<()> {
        let dir = tempdir()?;
        let path = dir.path();

        // Create valid migration files
        let mut file1 = File::create(path.join("001_first.sql"))?;
        file1.write_all(
            b"
-- migrate:up
CREATE TABLE users (id INTEGER);
-- migrate:down
DROP TABLE users;
        ",
        )?;

        let mut file2 = File::create(path.join("002_second.sql"))?;
        file2.write_all(
            b"
-- migrate:up
ALTER TABLE users ADD COLUMN email TEXT;
-- migrate:down
ALTER TABLE users DROP COLUMN email;
        ",
        )?;

        let migrations = Migration::load_from_dir(path)?;
        assert_eq!(migrations.len(), 2);
        assert_eq!(migrations[0].version, 1);
        assert_eq!(migrations[0].description, "first");
        assert_eq!(migrations[0].up.trim(), "CREATE TABLE users (id INTEGER);");
        assert_eq!(migrations[0].down.trim(), "DROP TABLE users;");
        assert_eq!(migrations[1].version, 2);
        assert_eq!(migrations[1].description, "second");
        assert_eq!(
            migrations[1].up.trim(),
            "ALTER TABLE users ADD COLUMN email TEXT;"
        );
        assert_eq!(
            migrations[1].down.trim(),
            "ALTER TABLE users DROP COLUMN email;"
        );

        Ok(())
    }

    #[test]
    fn test_load_migrations_duplicate_versions() {
        let dir = tempdir().unwrap();
        let path = dir.path();

        // First file with zero-padded version
        let mut file1 = File::create(path.join("001_duplicate.sql")).unwrap();
        file1
            .write_all(
                b"
-- migrate:up
CREATE TABLE test;
-- migrate:down
DROP TABLE test;
        ",
            )
            .unwrap();

        // Second file with same version but different formatting
        let mut file2 = File::create(path.join("1_another.sql")).unwrap();
        file2
            .write_all(
                b"
-- migrate:up
CREATE TABLE test2;
-- migrate:down
DROP TABLE test2;
        ",
            )
            .unwrap();

        let result = Migration::load_from_dir(path);
        assert!(result.is_err());
        assert!(result
            .unwrap_err()
            .to_string()
            .contains("Duplicate migration version 1"));
    }

    #[test]
    fn test_load_from_map() {
        let mut migrations_map = HashMap::new();

        migrations_map.insert(
            "001_create_users.sql".to_string(),
            r#"-- migrate:up
CREATE TABLE users (id INTEGER PRIMARY KEY);

-- migrate:down
DROP TABLE users;"#
                .to_string(),
        );

        migrations_map.insert(
            "002_add_email.sql".to_string(),
            r#"-- migrate:up
ALTER TABLE users ADD COLUMN email TEXT;

-- migrate:down
ALTER TABLE users DROP COLUMN email;"#
                .to_string(),
        );

        let migrations = Migration::load_from_map(migrations_map).unwrap();
        assert_eq!(migrations.len(), 2);

        assert_eq!(migrations[0].version, 1);
        assert_eq!(migrations[0].description, "create users");
        assert!(migrations[0].up.contains("CREATE TABLE users"));
        assert!(migrations[0].down.contains("DROP TABLE users"));

        assert_eq!(migrations[1].version, 2);
        assert_eq!(migrations[1].description, "add email");
        assert!(migrations[1].up.contains("ADD COLUMN email"));
        assert!(migrations[1].down.contains("DROP COLUMN email"));
    }

    #[test]
    fn test_load_from_map_invalid_filename() {
        let mut migrations_map = HashMap::new();
        migrations_map.insert(
            "invalid_filename.sql".to_string(),
            r#"-- migrate:up
CREATE TABLE test (id INTEGER PRIMARY KEY);

-- migrate:down
DROP TABLE test;"#
                .to_string(),
        );

        assert!(Migration::load_from_map(migrations_map).is_err());
    }

    #[test]
    fn test_load_from_map_duplicate_versions() {
        let mut migrations_map = HashMap::new();

        migrations_map.insert(
            "001_first.sql".to_string(),
            r#"-- migrate:up
CREATE TABLE test1 (id INTEGER PRIMARY KEY);

-- migrate:down
DROP TABLE test1;"#
                .to_string(),
        );

        migrations_map.insert(
            "001_second.sql".to_string(),
            r#"-- migrate:up
CREATE TABLE test2 (id INTEGER PRIMARY KEY);

-- migrate:down
DROP TABLE test2;"#
                .to_string(),
        );

        assert!(Migration::load_from_map(migrations_map).is_err());
    }

    #[test]
    fn test_load_from_map_invalid_sql() {
        let mut migrations_map = HashMap::new();
        migrations_map.insert(
            "001_test.sql".to_string(),
            "Invalid SQL without up/down sections".to_string(),
        );

        assert!(Migration::load_from_map(migrations_map).is_err());
    }
}
